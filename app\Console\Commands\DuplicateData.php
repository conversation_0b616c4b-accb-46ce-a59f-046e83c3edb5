<?php

namespace App\Console\Commands;
use DB;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;

class DuplicateData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:cronjob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
//         $mon=Carbon::now()->format('m');
//        $cur_mon=Carbon::now()->format('Y-m');
//         $level=DB::table('level_status')->where('month',$cur_mon)->first();
//         // dd($level->id);

//         if($mon==04){
//             $store=[
//                 'month'=>Carbon::now()->format('Y-m'),
//             ];
//             $level=DB::table('level_status')->insertGetId($store);
//             $data=DB::table('tbl_project_tasks')->where('level',$level-1)->where('status','<>',5)->where('status', '<>', 6)->where('deleted_status','<>',1)->get();
//             foreach($data as $values){
//                 DB::table('tbl_project_tasks')->insert([
//                     'project_id' => $values->project_id,
//                     'project_category_id' => $values->project_category_id,
//                     'project_name' => $values->project_name,
//                     'business_id' => $values->business_id,
//                     'scientist_id' =>$values->scientist_id,
//                     'division_id' => $values->division_id,
//                     'brand'         => $values->brand,
//                     'product_topic' => $values->product_topic,
//                     'pilist_variants' => $values->pilist_variants,
//                     'formulation' =>  $values->formulation,
//                     'variants_status' => $values->variants_status,
//                     'user_id'=>$values->user_id,
//                     'status' => $values->status,
//                     'metrics' => $values->metrics,
//                     'delayr_d' => $values->delayr_d,
//                     'delay_project' => $values->delay_project,
//                     'reason_delay' => $values->reason_delay,
//                     'reason_rd_delay' => $values->reason_rd_delay,
//                     'criticalupdate_issue'=>$values->criticalupdate_issue,
//                     'deleted_status'=>$values->deleted_status,
//                     'created_date' => $cur_mon.'-1',
//                     'level'=>$level,
//                     'Selected_ideas'=>$values->Selected_ideas,
//                     'created_by'=>$values->created_by,
//                     'assigned_to'=>$values->assigned_to,
//                     'index_id'=>$values->id,
//                     'create_current_date'=>$values->create_current_date,
//                 ]);
//             }
//         }else{
            
//             $store=[
//                 'month'=>Carbon::now()->format('Y-m'),
//             ];
//             $level=DB::table('level_status')->insertGetId($store);
//             $data=DB::table('tbl_project_tasks')->where('level',$level-1)->where('deleted_status','<>',1)->get();
//             foreach($data as $values){
                
//                 DB::table('tbl_project_tasks')->insert([
//                     'project_id' => $values->project_id,
//                     'project_category_id' => $values->project_category_id,
//                     'project_name' => $values->project_name,
//                     'business_id' => $values->business_id,
//                     'scientist_id' =>$values->scientist_id,
//                     'division_id' => $values->division_id,
//                     'brand'         => $values->brand,
//                     'product_topic' => $values->product_topic,
//                     'pilist_variants' => $values->pilist_variants,
//                     'formulation' =>  $values->formulation,
//                     'variants_status' => $values->variants_status,
//                     'user_id'=>$values->user_id,
//                     'status' => $values->status,
//                     'metrics' => $values->metrics,
//                     'delayr_d' => $values->delayr_d,
//                     'delay_project' => $values->delay_project,
//                     'reason_delay' => $values->reason_delay,
//                     'reason_rd_delay' => $values->reason_rd_delay,
//                     'criticalupdate_issue'=>$values->criticalupdate_issue,
//                     'deleted_status'=>$values->deleted_status,
//                     'created_date' =>$cur_mon.'-1',
//                     'level'=>$level,

//                     'Selected_ideas'=>$values->Selected_ideas,

//                     'created_by'=>$values->created_by,
//                     'assigned_to'=>$values->assigned_to,
//                     'index_id'=>$values->id,
//                     'create_current_date'=>$values->create_current_date,
//                 ]);
//             }
//         }
//         // dd($level->id);
//         $level_1=DB::table('level_status')->where('month',$cur_mon)->first();
//         // dd($level_1->id-1);
//         $level_status=$level_1->id-1;
        
//         // function prefered_concept 
//         $data=DB::table('tbl_preferedconcepts')->where('level',$level_status)->where('deleted_status',0)->get();
//         foreach($data as $val)
//         {
//             DB::table('tbl_preferedconcepts')->insert([
//             'concept_details'=>$val->concept_details,
//             'bussiness_master_id'=>$val->bussiness_master_id,
//             'user_id'=>$val->user_id,
//             'status'=>$val->status,
//             'concept_weight'=>$val->concept_weight,
//             'remarks'=>$val->remarks,
//             'deleted_status'=>$val->deleted_status,
//             'level'=>$level_1->id,
//             'created_date'=>$cur_mon.'-1',
//             'created_current_date'=>$val->created_current_date,
//             ]);
//         }
//         $data=DB::table('tbl_preferedproducts')->where('level',$level_status)->where('deleted_status',0)->get();
//         foreach($data as $val)
//         {
//             DB::table('tbl_preferedproducts')->insert([
//             'product_details'=>$val->product_details,
//             'user_id'=>$val->user_id,
//             'business_id'=>$val->business_id,
//             'status'=>$val->status,
//             'purchase_percentage'=>$val->purchase_percentage,
//             'remarks'=>$val->remarks,
//             'deleted_status'=>$val->deleted_status,
//             'level'=>$level_1->id,
//             'created_date'=>$cur_mon.'-1',
//             'created_current_date'=>$val->created_current_date,
//             ]);
//         }
//         //ipr_task
//         $data=DB::table('tbl_ipr_task')->where('level',$level_status)->where('deleted_status',0)->get();
//         foreach($data as $val)
//         {
//             DB::table('tbl_ipr_task')->insert([
//             'ipr_id'=>$val->ipr_id,
//             'user_id'=>$val->user_id,
//             'created_by'=>$val->created_by,
//             'project_details'=>$val->project_details,
//             'status'=>$val->status,
//             'remarks'=>$val->remarks,
//             'deleted_status'=>$val->deleted_status,
//             'level'=>$level_1->id,
//             'created_date'=>$cur_mon.'-1',
//             'created_current_date'=>$val->created_current_date,
           
//             ]);
//         }
        
// // info($level);
//         return 0;
   }
}
