<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Session;
use Illuminate\Http\Request;
//Auth facade
use Auth;
use DB;
use Validator;
use DateTime;
use Illuminate\Support\Facades\Crypt;
use App\Models\User;
use App\Models\Objective;
use App\Models\Division;
use App\Models\Taskinfo;
use App\Models\Taskinfo_month;
use App\Models\Taskinfo_week;
use App\Models\Master_table;
use App\Models\Bussiness;
use App\Models\CFR;
use App\Models\Feedback;
use App\Models\Recognition;
use App\Models\Conversationnew;
use App\Models\MainCategory;
use App\Models\InsightCategory;
use App\Models\SubCategory;
use App\Models\Brand;
use App\Models\LanddInsight;
use App\Models\GoalAchivement;
use App\Models\TypeofIdea;
use App\Models\Productivityoutsourcing;
use App\Models\ProductCategory;
use App\Models\TargetSegment;
use App\Models\ReplicationCompetitors;
use App\Models\PublicationScope;
use App\Models\IdeaCategory;
use App\Models\IdeaStatus;
use App\Models\Worktype;
use App\Models\Subworktype;
use App\Models\Category;
use App\Models\Sampletest;
use App\Models\Standardtime;
Use Alert;
 
 
use Mail;
use Redirect;
use Socialite;

use Dcblogdev\MsGraph\Facades\MsGraph;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }








    public function connect()
    {
          return MsGraph::connect();
      
    }
  
     
    public function microsoft_callback_hemas(Request $request)
    {

        $socialUser = Socialite::driver('azure')->setConfig(getConfighemas())->user();
 
    //   echo $socialUser->email;
    //   print_r($socialUser);
          if($socialUser->email !== null  )
        {
       
       $userlist =  User::where('email', $socialUser->email)->get();

      
        $user = Auth::attempt(['email' => $userlist[0]->email, 'password' => $userlist[0]->real_pass, 'status' => '1']);
        if($user) {

     
         Session::put('logintype',"social");
 
         if(Auth::User()->role==1)
         {
             return redirect('/home');
         }
         else
         {
             return redirect('/home');

         } 

        }  
        else 
        {
                $res['success'] = 'failed';
                $res['message']= trans('lang.invalid_login'); 


                Alert::warning('Invalid Login', 'Incorrect User name or Password');
                Auth::logout();
                return MsGraph::disconnect('/');
                return view( 'auth.login' );
        }

        }
        else
        {
            $res['success'] = 'failed';
            $res['message']= trans('lang.invalid_login'); 


            Alert::warning('Invalid Login', 'Microsoft Incorrect Username or Password');
            Auth::logout();
            return MsGraph::disconnect('/');
            return redirect('/login');

        }



    }



    public function microsoft_callback_cavincare(Request $request)
      {

        $socialUser = Socialite::driver('azure')->setConfig(getConfigcavincare())->user();

          //  print_r($socialUser);
          //  exit();
              if($socialUser->email != null  )
            {
           
           $userlist =  User::where('email', $socialUser->email)->first();
              
           if($userlist) {


         
    
          // $string_to_encrypt="12345678";
           $password="randdencryptpasswordstring";
        //  echo   $encrypted_string=openssl_encrypt($string_to_encrypt,"AES-128-ECB",$password);
             $decrypted_password=openssl_decrypt($userlist->encrypt_password,"AES-128-ECB",$password);
            
             $user = Auth::attempt(['email' => $userlist->email, 'password' => $decrypted_password, 'status' => '1']);
             
           
             Session::put('logintype',"social");
     
            //  if(Auth::User()->role==2)
            //  {
            //      return redirect('/bussiness');
            //  }
            //  else
            //  {
                 return redirect('/home');
    
            //  } 
    
           
            
            }
            else 
            {
                    $res['success'] = 'failed';
                    $res['message']= trans('lang.invalid_login'); 


                    Alert::warning('Microsoft Login', 'Your Mail id not available in this portal ');

                  //  Alert::warning('Invalid Login', 'Incorrect User name or Password ');
                    Auth::logout();
                  // return MsGraph::disconnect('/');
                   
                   return redirect('/login');
            }
        
            }
            else
            {
                $res['success'] = 'failed';
                $res['message']= trans('lang.invalid_login'); 
 
                Alert::warning('Microsoft Login', 'Login Again');

               // Alert::warning('Invalid Login', 'Microsoft Incorrect Username or Password');
                Auth::logout();
                //return MsGraph::disconnect('/');
                return redirect('/login');
    
            }
    


      }
      protected function authenticated(Request $request, $user)
      {
        /* $role = Auth::user()->role;
        $auth_status = Auth::user()->status;
         //dd($role);
        //dd($auth_status); 
        if($role == '1' && $auth_status =='0' ||$role == '2'&& $auth_status =='0' ||$role == '3' && $auth_status =='0' ||$role == '4' && $auth_status =='0' ||$role == '5' && $auth_status =='0' ||$role == '6' && $auth_status =='0' ||$role == '7' && $auth_status =='0' ||$role == '8' && $auth_status =='0' ){
            
             //dd('check inside'); 
             Auth::logout();
            Alert::warning(' Login', 'Your Mail id not active in this portal, Please ask your admin ');
            return redirect('/login_user');
        }else if($role == '1'||$role == '3'||$role == '4'||$role == '5'||$role == '6'){

            $user_list = User::where('status', 1)->get();
            $supervisor_list = User::where('is_supervisor', 1)->get();
            $division_list = Division::where('status', 1)->get();
            $division = Division::get();
            $project_categories = DB::table('reasons_for_delay_yes_no')->where('status', 1)->get();

            $bussiness_list = DB::table('bussiness_with_division')->select('bussiness_master.id','bussiness_master.bussiness_name')
            ->join('bussiness_master', 'bussiness_with_division.bussiness_id', '=', 'bussiness_master.id')
            ->where('bussiness_with_division.deleted_status', 1)
            ->where('bussiness_with_division.division_id',Auth::user()->division_id)->get();;
            $project_list = Project::where('deleted_status', 1)->get();
            return redirect('/dashboard')

            // return view('admin/masters')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('bussiness_list' ,$bussiness_list)
            ->with('project_list' ,$project_list)
            ->with('supervisor_list' ,$supervisor_list)
            ->with('division' ,$division)
            ->with('project_tasks' ,DB::table('tbl_project_tasks')->get())
            ->with('project_categories', $project_categories)
            
            ->with('project_status' ,DB::table('tbl_cft_status')->get());


        }
        else if($role == '2'){

            $user_list = User::where('status', 1)->get();
            $supervisor_list = User::where('is_supervisor', 1)->get();
            if ($role == 5) {
                $division_list = Division::where('status', 1)->get();
            }else{
                $user_id = Auth::user()->id;
                $user_division_id = Auth::user()->division_id;
                $division = User::where('supervisor_id', $user_id)->pluck('division_id')->toArray();
                $division[] = $user_division_id;
                $division_list = Division::where('status', 1)->whereIn('id', $division)->get();
            }
            $division = Division::get();
            $bussiness_list = DB::table('bussiness_with_division')->select('bussiness_master.id','bussiness_master.bussiness_name')
            ->join('bussiness_master', 'bussiness_with_division.bussiness_id', '=', 'bussiness_master.id')
            ->where('bussiness_with_division.deleted_status', 1)
            ->where('bussiness_with_division.division_id',Auth::user()->division_id)->get();
            // $bussiness_list = Bussiness::where('deleted_status', 1)->where('division_id',Auth::user()->division_id)->get();
            $project_list = Project::where('deleted_status', 1)->get();
            $scientist = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->get();
            $project_status = ProjectStatus::where('del_status', 1)->get();
            $product_category = ProductCategory::where('status', 1)->get();
            $brand_list = DB::table('brand_with_division')->select('brand.id','brand.brand_name')
            ->join('brand', 'brand_with_division.brand_id', '=', 'brand.id')
            ->where('brand_with_division.deleted_status', 1)
            ->where('brand_with_division.division_id',Auth::user()->division_id)->get();
            $project_categories = DB::table('reasons_for_delay_yes_no')->where('status', 1)->get();
    
            
            // return view('level1/dashboard_user')->with('objective',$objective);
            return view('admin/bussiness')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('bussiness_list' ,$bussiness_list)
            ->with('project_list' ,$project_list)
            ->with('supervisor_list' ,$supervisor_list)
            ->with('division' ,$division)
            ->with('brand_list' ,$brand_list)

            ->with('scientist' ,$scientist)
            ->with('project_status_new' ,$project_status)
            ->with('project_tasks' ,DB::table('tbl_project_tasks')->get())
            ->with('project_status' ,DB::table('tbl_cft_status')->get())
            ->with('project_categories', $project_categories)
            ->with('product_category',$product_category);

        }else if($role == '3'){

            $user_id = Auth::user()->id;
            $supervisor_id = Auth::user()->supervisor_id;
            $user_list = User::where('status',1)->get();
            $division = Division::get();
            $objective = Objective::get();
            $brand_list = DB::table('brand_with_division')->select('brand.id','brand.brand_name')
            ->join('brand', 'brand_with_division.brand_id', '=', 'brand.id')
            ->where('brand_with_division.deleted_status', 1)
            ->where('brand_with_division.division_id',Auth::user()->division_id)->get();

            $division_yearly = Objective::where('user', $supervisor_id)->select('division_id')->groupBy('division_id')->get();

            $year_yearly = Objective::where('user', $supervisor_id)->select('year')->groupBy('year')->get();

            $project_info = ProjectSubinfo::get();



            $user_list_weekly = User::where('status',1)->where('supervisor_id', $user_id)->where('role', 3)->get();

            $scientist = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->get();


            $bussiness_list = DB::table('bussiness_with_division')->select('bussiness_master.id','bussiness_master.bussiness_name')
            ->join('bussiness_master', 'bussiness_with_division.bussiness_id', '=', 'bussiness_master.id')
            ->where('bussiness_with_division.deleted_status', 1)
            ->where('bussiness_with_division.division_id',Auth::user()->division_id)->get();
            $project_status = ProjectStatus::where('del_status', 1)->get();
            $project_categories = DB::table('reasons_for_delay_yes_no')->where('status', 1)->get();

            
            $task_info_monthly = Taskinfo_month::get();
            $product_category = ProductCategory::where('status', 1)->get();
            return redirect('admin/project_dashboard')

        //  return view('level2/project_dashboard_level2')
         ->with('user_list',$user_list)
        ->with('objective',$objective)
        ->with('division',$division)
        ->with('project_info',$project_info)
        ->with('division_yearly',$division_yearly)
        ->with('bussiness_list',$bussiness_list)
        ->with('year_yearly',$year_yearly)
        ->with('scientist',$scientist)
        ->with('brand_list' ,$brand_list)

        ->with('task_info_monthly',$task_info_monthly)
        ->with('project_status_new',$project_status)
        ->with('product_category',$product_category)
        ->with('project_categories', $project_categories)
        ->with('user_list_weekly',$user_list_weekly);

        }else if($role == '7' && $auth_status ==1){
            $worktype_list_subwork = Worktype::where('worktype', 'assays')->where('deleted_status', 0)->get();
            $worktype_list = Worktype::where('deleted_status', 0)->get();
            $subworktype_list = Subworktype::where('deleted_status', 0)->get();
            $category_list = Category::where('deleted_status', 0)->get();
            $sampletest_list = Sampletest::where('deleted_status', 0)->get();
            $division_list = Division::where('status', 1)->get();
            return redirect('masters_hepl')
                ->with('worktype_list', $worktype_list)
                ->with('subworktype_list', $subworktype_list)
                ->with('category_list', $category_list)
                ->with('sampletest_list', $sampletest_list)
                ->with('division_list', $division_list)
                ->with('worktype_list_subwork', $worktype_list_subwork);
        }else if($role == '8' && $auth_status == '1'){
            
            $worktype = Worktype::where('deleted_status',0)->get();
            $subworktype = Subworktype::where('deleted_status',0)->get();
            return redirect('test_sample')
                    ->with('worktype',$worktype)
                    ->with('subworktype',$subworktype);
            // return redirect()->route('test_sample');
        }else{
            // Auth::logout();
            return redirect('/home');
        } */
      }


      public function login_user(Request $request)
      {
        return view('auth/login_user');
      }

}
