<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;

use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Objective;
use App\Models\Division;
use App\Models\Bussiness;
use App\Models\Taskinfo;
use App\Models\Taskinfo_month;
use App\Models\Taskinfo_week;
use App\Models\CFR;
use App\Models\Feedback;
use App\Models\Recognition;
use App\Models\Conversationnew;
use App\Models\Master_table;
use App\Models\MainCategory;
use App\Models\SubCategory;
use App\Models\Productivityoutsourcing;
use App\Models\Completitioninformation;
use DB;
use Session;
use Auth;
use DateTime;
use QueryException;

class DashboardController extends Controller
{
  

}
