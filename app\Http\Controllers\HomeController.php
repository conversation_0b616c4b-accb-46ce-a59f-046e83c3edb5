<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use Illuminate\Support\Facades\Crypt;
use App\Models\User;
use App\Models\Objective;
use App\Models\Division;
use App\Models\Taskinfo;
use App\Models\Taskinfo_month;
use App\Models\Taskinfo_week;
use App\Models\Master_table;
use App\Models\Bussiness;
use App\Models\CFR;
use App\Models\Feedback;
use App\Models\Recognition;
use App\Models\Conversationnew;
use App\Models\MainCategory;
use App\Models\InsightCategory;
use App\Models\SubCategory;
use App\Models\Brand;
use App\Models\LanddInsight;
use App\Models\GoalAchivement;
use App\Models\TypeofIdea;
use App\Models\Productivityoutsourcing;
use App\Models\ProductCategory;
use App\Models\TargetSegment;
use App\Models\ReplicationCompetitors;
use App\Models\PublicationScope;
use App\Models\IdeaCategory;
use App\Models\IdeaStatus;
use App\Models\Worktype;
use App\Models\Subworktype;
use App\Models\Category;
use App\Models\Sampletest;
use App\Models\Standardtime;
use DB;
use Session;
use DateTime;
Use Alert;
use App\Models\DepartmentMaster;
use App\Models\InsightsMaster;
use App\Models\LearningDevelopment;

class HomeController extends Controller
{

    public function overall_view(Request $request)
    {
        $objective_id = Crypt::decryptString($request->id);
        $division_id = Objective::where('obj_id',$objective_id)
                                ->where('year','2022-2023')->first();
        $division_name = DB::table('tbl_divion_master' )
                                ->where( 'id', $division_id->division_id)
                                ->first();
        $objective_name = DB::table( 'tbl_objective_master' )
                                ->where( 'obj_id', $objective_id)
                                ->first();
        $monthly_task = DB::table( 'tbl_task_info_monthly' )
                            ->where( 'objective', $objective_id)
                            ->groupBy('month','month_number')
                            ->select('month','month_number')
                            ->get();
        $week_task = DB::Table('tbl_task_info_weekly')
                            ->where( 'objective', $objective_id)
                            ->groupBy('month','month_number')
                            ->select('month','month_number')
                            ->get();
        $user_list = User::where('status',1)->get();
      
      
        return view('admin/overall_view')
                ->with('user_list',$user_list)
                ->with('objective_id',$objective_id)
                ->with('division_name',$division_name)
                ->with('objective_name',$objective_name)
                ->with('monthly_task',$monthly_task)
                ->with('week_task',$week_task)
                ->with('division_id',$division_id->division_id);
    }


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $role = Auth::user()->role;
        $auth_status = Auth::user()->status;
        if($role == '1' && $auth_status =='0' ||$role == '2'&& $auth_status =='0' ||$role == '3' && $auth_status =='0' ||$role == '4' && $auth_status =='0' ||$role == '5' && $auth_status =='0' ||$role == '6' && $auth_status =='0' ||$role == '7' && $auth_status =='0' ||$role == '8' && $auth_status =='0' ){
            
             //dd('check inside'); 
             Auth::logout();
            Alert::warning(' Login', 'Your Mail id not active in this portal, Please ask your admin ');
            return redirect('/login_user');
        }else if($role == '1'||$role == '3'||$role == '4'||$role == '5'||$role == '6' ||$role = '2'){

            $user_list = User::where('status', 1)->get();
            $supervisor_list = User::where('is_supervisor', 1)->get();
            $division_list = Division::where('status', 1)->get();
            $division = Division::get();

            return redirect('/learning_development')

            // return view('admin/masters')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('supervisor_list' ,$supervisor_list)
            ->with('division' ,$division);
           }
        else if($role == '2'){

            $user_list = User::where('status', 1)->get();
            $supervisor_list = User::where('is_supervisor', 1)->get();
            if ($role == 5) {
                $division_list = Division::where('status', 1)->get();
            }else{
                $user_id = Auth::user()->id;
                $user_division_id = Auth::user()->division_id;
                $division = User::where('supervisor_id', $user_id)->pluck('division_id')->toArray();
                $division[] = $user_division_id;
                $division_list = Division::where('status', 1)->whereIn('id', $division)->get();
            }
            $division = Division::get();
          
            // $bussiness_list = Bussiness::where('deleted_status', 1)->where('division_id',Auth::user()->division_id)->get();
            $scientist = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->get();
            $brand_list = DB::table('brand_with_division')->select('brand.id','brand.brand_name')
            ->join('brand', 'brand_with_division.brand_id', '=', 'brand.id')
            ->where('brand_with_division.deleted_status', 1)
            ->where('brand_with_division.division_id',Auth::user()->division_id)->get();
    
            
            // return view('level1/dashboard_user')->with('objective',$objective);
            return view('admin/bussiness')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('supervisor_list' ,$supervisor_list)
            

            ->with('scientist' ,$scientist);
          

        }else if($role == '3'){

            $user_id = Auth::user()->id;
            $supervisor_id = Auth::user()->supervisor_id;
            $user_list = User::where('status',1)->get();
            $division = Division::get();
            $objective = Objective::get();
            $brand_list = DB::table('brand_with_division')->select('brand.id','brand.brand_name')
            ->join('brand', 'brand_with_division.brand_id', '=', 'brand.id')
            ->where('brand_with_division.deleted_status', 1)
            ->where('brand_with_division.division_id',Auth::user()->division_id)->get();

            $division_yearly = Objective::where('user', $supervisor_id)->select('division_id')->groupBy('division_id')->get();

            $year_yearly = Objective::where('user', $supervisor_id)->select('year')->groupBy('year')->get();




            $user_list_weekly = User::where('status',1)->where('supervisor_id', $user_id)->where('role', 3)->get();

            $scientist = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->get();
            
            return view('admin/project_dashboard')

        //  return view('level2/project_dashboard_level2')
         ->with('user_list',$user_list)
        ->with('objective',$objective)
        ->with('division',$division)
        ->with('division_yearly',$division_yearly)
        ->with('year_yearly',$year_yearly)
        ->with('scientist',$scientist)
        ->with('brand_list' ,$brand_list)

        ->with('task_info_monthly',$task_info_monthly)
        ->with('product_category',$product_category)
        ->with('user_list_weekly',$user_list_weekly);

        }else if($role == '7' && $auth_status ==1){
            $worktype_list_subwork = Worktype::where('worktype', 'assays')->where('deleted_status', 0)->get();
            $worktype_list = Worktype::where('deleted_status', 0)->get();
            $subworktype_list = Subworktype::where('deleted_status', 0)->get();
            $category_list = Category::where('deleted_status', 0)->get();
            $sampletest_list = Sampletest::where('deleted_status', 0)->get();
            $division_list = Division::where('status', 1)->get();
            return view('masters_hepl')
                ->with('worktype_list', $worktype_list)
                ->with('subworktype_list', $subworktype_list)
                ->with('category_list', $category_list)
                ->with('sampletest_list', $sampletest_list)
                ->with('division_list', $division_list)
                ->with('worktype_list_subwork', $worktype_list_subwork);
        }else if($role == '8' && $auth_status == '1'){
            
            $worktype = Worktype::where('deleted_status',0)->get();
            $subworktype = Subworktype::where('deleted_status',0)->get();
            return view('test_sample')
                    ->with('worktype',$worktype)
                    ->with('subworktype',$subworktype);
            // return redirect()->route('test_sample');
        }else{
            // Auth::logout();
            return redirect('/home');
        } 
    }

    public function dashboard_new()
     {
    $role = Auth::user()->role;
      $user_list = User::where('status', 1)->get();
      $supervisor_list = User::where('is_supervisor', 1)->get();
      $division_list = Division::where('status', 1)->get();
      $division = Division::get();
    
      $brand_list = DB::table('brand_with_division')->select('brand.id','brand.brand_name')
      ->join('brand', 'brand_with_division.brand_id', '=', 'brand.id')
      ->where('brand_with_division.deleted_status', 1)
      ->where('brand_with_division.division_id',Auth::user()->division_id)->get();
      $scientist = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->get();
      $product_category = ProductCategory::where('status', 1)->get();
      return view('admin/project_dashboard_new')
      // return view('admin/masters')
      ->with('user_list', $user_list)
      ->with('division_list', $division_list)
      ->with('supervisor_list' ,$supervisor_list)
      ->with('scientist' ,$scientist);
   
  }
    
 
    public function scientist_colab()
    {
        $scientist_colab = DB::table('users')->get();
        $html ='<option value="0">---Select---</option> ';
        foreach($scientist_colab as $val)
        {
            if($val->id == Auth::id())
            {
                 $html .='<option value='.$val->id .' selected="selected">'.$val->name .'</option>';
            }
            else{
                $html .='<option value='.$val->id .'>'.$val->name .'</option>';

            }
           
        }
        return $html;

    }
  
   


    // public function prefered_concepts()
    // {
    //     $user_list = User::where('status',1)->get();
    //     $business= DB::table('bussiness_master')->where('deleted_status',0)->get()->toArray();
    //     return view('prefered_concepts')->with('business',$business)->with('scientist',$user_list);

    // }
    // public function prefered_products()
    // {
    //     $user_list = User::where('status',1)->get();
    //     $business= DB::table('bussiness_master')->where('deleted_status',0)->get()->toArray();
    //     return view('prefered_products')->with('business',$business)->with('scientist',$user_list);

    // }
    // public function productivity_outsourcing()
    // {
    //     return view('productivity_outsourcing');

    // }

    public function learning_development()
    {

        $myteams = DB::table('users')->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->pluck('id');


        // $user_list = User::where('status',1)->get();
        //->whereIn('id',$myteams)
 
        if (Auth::user()->role == 1) {
            $learning_insight  = LanddInsight::all();
         } else if (Auth::user()->role == 5) {
            $learning_insight  = LanddInsight::all();
         } else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =  LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =  LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
         } else  if (Auth::user()->role == 3) {
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =   LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
         } else {
            $learning_insight =   LanddInsight::where('scientist', Auth::user()->id)->get();
        }
 

        if (Auth::user()->role == 1) {
            $user_list = User::where('status',1)->where('id', Auth::id())->get();
         } else if (Auth::user()->role == 5) {
            // $user_list = User::where('status',1)->where('id', Auth::id())->get();

            // $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->Orwhere('id', Auth::id())->pluck('id');
            // dd($user_listid);
            $user_list = User::where('status',1)->get();

         } else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $user_list = User::where('status',1)->whereIn('id',$user_listid)->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            // $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $user_list = User::where('status',1)->whereIn('id',$user_listid1)->get();
         } else  if (Auth::user()->role == 3) {
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->Orwhere('id', Auth::id())->pluck('id');
            // dd($user_listid);
            $user_list = User::where('status',1)->whereIn('id',$user_listid)->get();
         } else {
            $user_listid1 = User::where('status', 1)->where('id', Auth::user()->id)->pluck('supervisor_id');
            $user_list = User::where('status',1)->whereIn('supervisor_id',$user_listid1)->where('id','<>', Auth::id())->get();
           
            // dd($user_list);
        }
        // dd($user_list);

        $learning_development = DB::table('tbl_learning_devlopment_idea')->get();
        $idea_type  = DB::table('tbl_parent_idea')->where('status', 1)->get();
        $parent_idea = DB::table('tbl_parent_idea')->where('status', 1)->get();
        $iiy_digital_learning = DB::table('iiy_digital_learning')->where('status', 1)->get();
        $parent_description=DB::table('tbl_learning_devlopment_idea')->select('id')->get();
        $tbl_insight_category = InsightCategory::select('name','id')->where('status',1)->get();
        $tbl_insight_category_hr = DB::table('iiy_digital_learning')->select('source','id')->where('status',1)->get();
        // $role_user = Auth::user()->role;
        // dd($role_user);
       
        $co_creator_list = User::where('status',1)->where('id','<>', Auth::id())->get();
        $co_worker_list = User::where('role',2)->get();

        // topic learnt 
        $topic_learnt = LearningDevelopment::select('id','topics_present')->where('deleted_status',0)->get();
        return view('learning_development')
        ->with('topic_learnt',$topic_learnt)
        ->with('iiy_digital_learning',$iiy_digital_learning)
        ->with('co_creator_list',$co_creator_list)
        ->with('tbl_insight_category_hr',$tbl_insight_category_hr)
        ->with('learning_insight',$learning_insight)
        ->with('tbl_insight_category',$tbl_insight_category)
        ->with('scientist',$user_list)
        ->with('learning_development',$learning_development)
        ->with('parent_idea',$parent_idea)
        ->with('idea_type',$idea_type);
    }
    public function get_scientist_insight(Request $request)
    {
        if (!empty($request->idea_insight)) {
            $authUserId = $request->auth_user_id; 
    
            $insight = LanddInsight::select(
                    'tbl_landd_insights.scientist', 
                    'users.name as scientist_name',
                    'users.id as scientist_id',
                    'tbl_landd_insights.created_by'
                )
                ->leftJoin('users', 'users.id', '=', 'tbl_landd_insights.scientist')
                ->whereIn('tbl_landd_insights.id', $request->idea_insight)
                ->get()
                ->map(function ($item) use ($authUserId) {
                    // If the scientist_id matches the logged-in user's ID, remove the name
                    if ($item->scientist_id == $authUserId) {
                        $item->scientist_name = null;
                    }
                    return $item;
                });
    
            return response()->json($insight);
        } 
        else {
            return response()->json([]);
        }
    }
    
    

    
    public function get_parent_descri(Request $request)
    {
        $insight=DB::table('tbl_learning_devlopment_idea')->select('id')->get();
        $data='';
        $data.='<option value="0">Select</option>';
        foreach ($insight as $parent_desc){
            $data.='<option value="'.$parent_desc->id.'">'.$parent_desc->ideas_generated.' </option>';
            }
        return $data; 
    }


    
    
    public function intro()
    {

        return view('intro');
    }


     

    public function get_learning_development(Request $req)
    {
        $myteams = DB::table('users')->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->pluck('id');
        // $user_list = User::where('status',1)->get()->toArray();
        //->whereIn('id',$myteams)
        if (Auth::user()->role == 1) {
            $learning_insight  = LanddInsight::all();
         } else if (Auth::user()->role == 5) {
            $learning_insight  = LanddInsight::all();
         } else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =  LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =  LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
         } else  if (Auth::user()->role == 3) {
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $learning_insight =   LanddInsight::whereIn('scientist', $user_listid) 
            ->get();
         } else {
            $learning_insight =   LanddInsight::where('scientist', Auth::user()->id)->get();
        }
        if (Auth::user()->role == 1) {
            $user_list = User::where('status',1)->where('id','<>', Auth::id())->get();
         } else if (Auth::user()->role == 5) {
            $user_list = User::where('status',1)->where('id','<>', Auth::id())->get();
            // dd($user_list);
         } else if (Auth::user()->role == 4) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $user_list = User::where('status',1)->whereIn('id',$user_listid)->where('id','<>', Auth::id())->get();
        } else if (Auth::user()->role == 6) {
            $user_listid1 = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->orWhere('id', Auth::id())->pluck('id');
            $user_listid = User::where('status', 1)->whereIn('supervisor_id', $user_listid1)->orWhere('id', Auth::id())->pluck('id');
            $user_list = User::where('status',1)->whereIn('id',$user_listid)->where('id','<>', Auth::id())->get();
         } else  if (Auth::user()->role == 3) {
            $user_listid = User::where('status', 1)->where('supervisor_id', Auth::user()->id)->where('id','<>', Auth::id())->pluck('id');
            $user_list = User::where('status',1)->whereIn('id',$user_listid)->get();
         } else {
            // $user_listid1 = User::where('status', 1)->where('id', Auth::user()->id)->pluck('supervisor_id');
            // // dd($user_listid1);
            // $user_list = User::where('status',1)->whereIn('supervisor_id',$user_listid1)->where('id','<>', Auth::id())->get();
            $user_list = User::where('status',1)->where('id','<>', Auth::id())->get();
        }
        // $user_list = User::where('status',1)->get();


        $learning_development = DB::table('tbl_learning_devlopment_idea')->get();
        $parent_idea = DB::table('tbl_parent_idea')->where('status',1)->get();
        $parent_description=DB::table('tbl_learning_devlopment_idea')->select('id')->get();
        $tbl_insight_category = InsightCategory::all();
       
        $select_learning_development=DB::table('tbl_learning_devlopment_idea')->join('title_is_new_product_ideas_type', 'tbl_learning_devlopment_idea.id', '=', 'title_is_new_product_ideas_type.learning_development_idea_id')->where('tbl_learning_devlopment_idea.id',$req->id)->first();
      //    dd($req->id,$select_learning_development);
        $scientist_list=json_decode($select_learning_development->scientist);
        // $scientist_edit_list = DB::table('users')->whereIn('id',$scientist_list)->pluck('id')->toArray();
        // dd($scientist_list);

        if(!is_array($scientist_list)){
            $scientist_list=[$scientist_list];
        }
        $scientist_edit_list_percentage=$select_learning_development->scientist_percentage;
            
        // dd($scientist_list);
        
        return view('ajax/get_view_learning_idea')->with('select_learning_development', $select_learning_development)->with('TypeofIdeamethod',$TypeofIdeamethod)->with('TypeofIdeasquees',$TypeofIdeasquees)->with('Ideastatusreasearch',$Ideastatusreasearch)->with('Ideastatusmethod',$Ideastatusmethod)->with('Ideastatussquees',$Ideastatussquees)->with('Ideastatusproduct',$Ideastatusproduct)->with('PublicationScope',$PublicationScope)->with('ReplicationCompetitors',$ReplicationCompetitors)->with('TargetSegment',$TargetSegment)->with('Brand',$Brand)->with('ProductCategory',$ProductCategory)->with('ProductCategory',$ProductCategory)->with('tbl_insight_category',$tbl_insight_category)->with('scientist',$user_list)->with('learning_development',$learning_development)->with('scientist_edit_list',$scientist_list)->with('scientist_edit_list_percentage',$scientist_edit_list_percentage)->with('parent_idea',$parent_idea)->with('parent_description',$parent_description)->with('child_idea',$child_idea);
        
    }

    public function get_view_learning_development(Request $req)
    {
        $myteams = DB::table('users')->where('supervisor_id', Auth::id())->orWhere('id',Auth::id())->pluck('id');


        // $user_list = User::where('status',1)->whereIn('id',$myteams)->get();
        $user_list = User::where('status',1)->get()->toArray();
        $parent_idea = DB::table('tbl_parent_idea')->get();
        $parent_description=DB::table('tbl_learning_devlopment_idea')->where('deleted_status',1)->select('id')->get();


        $learning_development = DB::table('tbl_learning_devlopment_idea')->where('deleted_status',0)->get();
        $learning_insight  = LanddInsight::all();
        $tbl_insight_category = InsightCategory::all();
        $ProductCategory = ProductCategory::all();
        $Brand = Brand::all();
        $TargetSegment = TargetSegment::all();
        $ReplicationCompetitors = ReplicationCompetitors::all();
        $PublicationScope = PublicationScope::all();
       
        $select_learning_development=DB::table('tbl_learning_devlopment_idea')->join('title_is_new_product_ideas_type', 'tbl_learning_devlopment_idea.id', '=', 'title_is_new_product_ideas_type.learning_development_idea_id')->where('tbl_learning_devlopment_idea.id',$req->id)->first();
        $scientist_list=json_decode($select_learning_development->scientist);
        
        $view=1;
        if(!is_array($scientist_list)){
            $scientist_list=[$scientist_list];
        }
        
        return view('ajax/get_view_learning_idea')->with('select_learning_development', $select_learning_development)->with('TypeofIdeamethod',$TypeofIdeamethod)->with('TypeofIdeasquees',$TypeofIdeasquees)->with('Ideastatusreasearch',$Ideastatusreasearch)->with('Ideastatusmethod',$Ideastatusmethod)->with('Ideastatussquees',$Ideastatussquees)->with('Ideastatusproduct',$Ideastatusproduct)->with('PublicationScope',$PublicationScope)->with('ReplicationCompetitors',$ReplicationCompetitors)->with('TargetSegment',$TargetSegment)->with('Brand',$Brand)->with('ProductCategory',$ProductCategory)->with('ProductCategory',$ProductCategory)->with('tbl_insight_category',$tbl_insight_category)->with('scientist',$user_list)->with('learning_development',$learning_development)->with('parent_idea',$parent_idea)->with('scientist_edit_list',$scientist_list)->with('parent_description',$parent_description)->with('child_idea',$child_idea)->with('view',$view);
        
    }
 
   
    public function get_reporting_scientist_cl() {
        //dd(response()->json($this->get_scientist_cl()));
        // if (Auth::user()->id == 74) {
        //     $scientist = DB::table('users')->where('status', 1)->select('name','id','role','supervisor_id')->get();
        // }else{
            $scientist = DB::table('users')->where('status', 1)->select('name','id','role','supervisor_id')->get();
            $scientist_cl = DB::table('users')->where('status', 1)->where('supervisor_id', Auth::id())->select('name','id','role','supervisor_id')->get();
            $scientist_tl = DB::table('users')
            ->where('status', 1)
            ->whereIn('supervisor_id', $scientist_cl->pluck('id')) 
            ->select('name', 'id', 'role', 'supervisor_id')
            ->get();
            $scientist_tm = DB::table('users')
            ->where('status', 1)
            ->whereIn('supervisor_id', $scientist_tl->pluck('id')) 
            ->select('name', 'id', 'role', 'supervisor_id')
            ->get();
            $scientist = DB::table('users')
            ->whereIn('id', $scientist_cl->pluck('id')->merge(Auth::id())->merge($scientist_tl->pluck('id'))->merge($scientist_tm->pluck('id'))->all())
            ->select('name', 'id', 'role', 'supervisor_id')
            ->get();
        // }

        $user_id =Auth::user()->id;
        $user_role =Auth::user()->role; 
        $data = [
            'scientists' => $scientist,
            'user_id' => $user_id,
            'user_role' => $user_role,
        ];
        return  $data;
    }


    /* Phase 3 -- For HEPL */
     public function test_sample()
    {
        $worktype = Worktype::where('deleted_status',0)->get();
        $subworktype = Subworktype::where('deleted_status',0)->get();
        return view('test_sample')
                ->with('worktype',$worktype)
                ->with('subworktype',$subworktype);
    } 
   

    public function user_management()
    {
        return view('user_management');
    } 


}
