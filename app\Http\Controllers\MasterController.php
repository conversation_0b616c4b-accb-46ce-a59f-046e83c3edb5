<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Category;
use App\Models\Worktype;
use App\Models\Subworktype;
use App\Models\Sampletest;
use App\Models\Division;
use App\Models\Standardtime;
/* use App\Models\Objective;
 */

use DB;
use Session;
use Auth;
use DateTime;
use QueryException;

class MasterController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        /*  $role = Session::get('role');
         echo "<pre>"; print_r($role); exit;
         if($role != '1'){
            //Auth::logout();
            return redirect('/login');
         } */
    }
    public function masters_hepl()
    {
        
        $user_list = User::where('status', 1)->get();
        $worktype_list = Worktype::where('deleted_status', 0)->get();
       
        $worktype_list_subwork = Worktype::where('worktype', 'assays')->where('deleted_status', 0)->get();
        //dd($worktype_list_subwork);
        
        $subworktype_list = Subworktype::where('deleted_status', 0)->get();
        $category_list = Category::where('deleted_status', 0)->get();
        $sampletest_list = Sampletest::where('deleted_status', 0)->get();
        $division_list = Division::where('status', 1)->get();
        return view('masters_hepl')
            ->with('worktype_list', $worktype_list)
            ->with('subworktype_list', $subworktype_list)
            ->with('category_list', $category_list)
            ->with('sampletest_list', $sampletest_list)
            ->with('division_list', $division_list)
            ->with('worktype_list_subwork', $worktype_list_subwork);
    }
    /* public function masters()
    {
        $user_list = User::where('status', 1)->get();
        $supervisor_list = User::where('is_supervisor', 1)->get();
        $division_list = Division::where('status', 1)->get();
        $bussiness_list = Bussiness::get();
        $project_list = Project::where('deleted_status', 1)->get();
        $insight_category_list = InsightCategory::all();
        $idea_type_list = IdeaType::all();
        $idea_weight_list = IdeaCategory::all();
        $replication_competitors_list = ReplicationCompetitors::all();
        $publication_scope_list= PublicationScope::all();
        $idea_status_list = IdeaStatus::all();
        $user_role = DB::table('user_role')->get();
        return view('admin/masters')
            ->with('user_list', $user_list)
            ->with('division_list', $division_list)
            ->with('bussiness_list', $bussiness_list)
            ->with('project_list', $project_list)
            ->with('supervisor_list', $supervisor_list)
            ->with('insight_category_list', $insight_category_list)
            ->with('idea_type_list', $idea_type_list)
            ->with('idea_weight_list', $idea_weight_list)
            ->with('replication_competitors_list', $replication_competitors_list)
            ->with('publication_scope_list', $publication_scope_list)
            ->with('idea_status_list', $idea_status_list)
            ->with('user_role', $user_role);
    } */

    /******** Master tab worktype Start *********************/

    public function get_all_worktype(Request $req)
    {
        $get_all_worktype = Worktype::where('deleted_status', 0)->orderBy('id','desc')->get();
        return Datatables::of($get_all_worktype)
            ->addColumn('id', function ($get_all_worktype) {
                $id = '';
                $id .= $get_all_worktype->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_all_worktype) {
                $worktype = '';
                $worktype .= $get_all_worktype->worktype;
                return $worktype . '';
            })
            ->addColumn('action', function ($get_all_worktype) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-info" onClick="edit_worktype(' . $get_all_worktype->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_worktype(' . $get_all_worktype->id . ')"><i class="fa fa-trash"></i></button>
                ';
                /* $action .= ' <button class="btn btn-sm btn-info" onClick="edit_worktype(' . $get_all_worktype->id . ')" ><i class="fa fa-edit"></i></button>'; */
                return $action . '';
            })
            ->rawColumns(array("worktype", "worktype", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
    }
    public function save_worktype(Request $request)
    {
        //dd($request->all());
        $count = Worktype::whereRaw('LOWER(worktype) = ?', [strtolower($request->name)])->where('deleted_status',0)->count();
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            // $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'worktype' => $request->name,
                
            );
            Worktype::insert($data);
            $rdata = "success";
            $data = [
                'message' => "worktype Added Successfully!",
                "action" => "success"
            ];
        } else {
            $rdata = "fail";
            $data = [
                'message' => "Duplicate Work Type ",
                "action" => "warning"
            ];
        }
        return json_encode($data);
    }
    public function edit_worktype(request $request){
        $get_list =DB::table('tbl_worktype')->where('id',$request->id)->get();
        return $get_list;
    }
    public function update_worktype(Request $request)
    {
        $worktype_count = Worktype::whereRaw('LOWER(worktype) = ?', [strtolower($request->name)])->where('id', '!=', $request->id)->where('deleted_status',0)->get()->count();
        // dd($request->all());
        if ($worktype_count == 0) {
            $update_worktype_data = [
                'worktype' =>  $request->name,
            ];
            DB::table('tbl_worktype')->where('id', $request->id)->update($update_worktype_data);
            //Worktype::where('id', $request->id)->update($update_worktype_data);
            $data = [
                'message' => "worktype Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            //return $update_worktype_data;
        } else {
            $data = [
                'message' => "Duplicate Work Type ",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_worktype(Request $request)
    {
        $del_worktype_data = [
            'deleted_status' => 1,
        ];
        Worktype::where('id', $request->id)->update($del_worktype_data);
        //$delete = DB::table('tbl_divion_master')->where('id', $request->id)->delete();
        $data = [
            'message' => "worktype Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
        return $del_worktype_data;
    }
    /************ Master tab worktype end*/

    /******** Master tab subworktype Start *********************/

    public function get_all_subworktype(Request $req)
    {
        $get_all_subworktype = SubWorktype::where('deleted_status', 0)->orderBy('id','desc')->get();
        return Datatables::of($get_all_subworktype)
            ->addColumn('id', function ($get_all_subworktype) {
                $id = '';
                $id .= $get_all_subworktype->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_all_subworktype) {
                $subworktype = '';
                $subworktype .= $get_all_subworktype->subworktype;
                return $subworktype . '';
            })
            ->addColumn('worktype', function ($get_all_subworktype) {
                $worktype = '';
                $get_details=Worktype::where('id',$get_all_subworktype->worktype_id)->pluck('worktype');
                $worktype .= $get_details[0];
                return $worktype . '';
            })
            ->addColumn('action', function ($get_all_subworktype) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-info" onClick="edit_subworktype(' . $get_all_subworktype->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_subworktype(' . $get_all_subworktype->id . ')"><i class="fa fa-trash"></i></button>
                ';
                /* $action .= ' <button class="btn btn-sm btn-info" onClick="edit_subworktype(' . $get_all_subworktype->id . ')" ><i class="fa fa-edit"></i></button>
                '; */
                return $action . '';
            })
            ->rawColumns(array("subworktype", "subworktype", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
    }
    public function save_subworktype(Request $request)
    {
        //dd($request->all());
        $count = SubWorktype::whereRaw('LOWER(subworktype) = ?', [strtolower($request->name)])->where('worktype_id', $request->worktype)->where('deleted_status',0)->count();
        //var_dump($count);
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            // $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'subworktype' => $request->name,
                'worktype_id' => $request->worktype,
            );
            //dd($data);
            SubWorktype::insert($data);
            $rdata = "success";
            $data = [
                'message' => "sub work type Added Successfully!",
                "action" => "success"
            ];
        } else {
            $rdata = "fail";
            $data = [
                'message' => "Duplicate Sub Work Type ",
                "action" => "warning"
            ];
        }
        return json_encode($data);
    }
    public function edit_subworktype(request $request){
        $get_list =DB::table('tbl_subworktype')->where('id',$request->id)->get();
        return $get_list;
    }
    public function update_subworktype(Request $request)
    {
        $subworktype_count = SubWorktype::whereRaw('LOWER(subworktype) = ?', [strtolower($request->name)])->where('worktype_id', $request->worktype)->where('id', '!=', $request->id)->where('deleted_status',0)->get()->count();
        // dd($request->all());
        if ($subworktype_count == 0) {
            $update_subworktype_data = [
                'subworktype' =>  $request->name,
            ];
            DB::table('tbl_subworktype')->where('id', $request->id)->update($update_subworktype_data);
            //SubWorktype::where('id', $request->id)->update($update_subworktype_data);
            $data = [
                'message' => "sub work type Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            //return $update_subworktype_data;
        } else {
            $data = [
                'message' => "Duplicate Sub Work Type ",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_subworktype(Request $request)
    {
        $del_subworktype_data = [
            'deleted_status' => 1,
        ];
        
        SubWorktype::where('id', $request->id)->update($del_subworktype_data);
        //$delete = DB::table('tbl_divion_master')->where('id', $request->id)->delete();
        $data = [
            'message' => "subworktype Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
    }
    /************ Master tab subworktype end*/

    /******** Master tab category Start *********************/

    public function get_all_category(Request $req)
    {
        $get_all_category = Category::where('deleted_status', 0)->orderBy('id','desc')->get();
        return Datatables::of($get_all_category)
            ->addColumn('id', function ($get_all_category) {
                $id = '';
                $id .= $get_all_category->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_all_category) {
                $category = '';
                $category .= $get_all_category->category;
                return $category . '';
            })
            ->addColumn('worktype', function ($get_all_category) {
                $worktype = '';
                //$get_worktype_id=Subworktype::where('id',$get_all_category->subworktype_id)->pluck('worktype_id');
                $get_details=Worktype::where('id',$get_all_category->worktype_id)->pluck('worktype');
                $worktype .= $get_details[0];
                return $worktype . '';
            })
            ->addColumn('subworktype', function ($get_all_category) {
                $subworktype = '';
                if($get_all_category->subworktype_id){
                    
                    $get_details=Subworktype::where('id',$get_all_category->subworktype_id)->pluck('subworktype');
                    $subworktype .= $get_details[0];
                }else{
                    $subworktype='-';
                }
                
                return $subworktype . '';
            })
            ->addColumn('action', function ($get_all_category) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-info" onClick="edit_category(' . $get_all_category->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_category(' . $get_all_category->id . ')"><i class="fa fa-trash"></i></button>
                ';
                return $action . '';
            })
            ->rawColumns(array("category", "category", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
    }
    public function save_category(Request $request)
    {
        //dd($request->all());
        $count = Category::whereRaw('LOWER(category) = ?', [strtolower($request->name)])->where('subworktype_id', $request->subworktype)->where('deleted_status',0)->count();
        //var_dump($count);
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            // $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'category' => $request->name,
                'worktype_id' => $request->worktype,
                'subworktype_id' => $request->subworktype,
            );
            //dd($data);
            Category::insert($data);
            $rdata = "success";
            $data = [
                'message' => "Category Added Successfully!",
                "action" => "success"
            ];
        } else {
            $rdata = "fail";
            $data = [
                'message' => "Duplicate Category ",
                "action" => "warning"
            ];
        }
        return json_encode($data);
    }
    public function edit_category(request $request){
        $get_list =DB::table('tbl_category')->where('id',$request->id)->get();
        return $get_list;
    }
    public function update_category(Request $request)
    {
        $category_count = Category::whereRaw('LOWER(category) = ?', [strtolower($request->name)])->where('subworktype_id', $request->subworktype)->where('id', '!=', $request->id)->where('deleted_status',0)->get()->count();
        // dd($request->all());
        if ($category_count == 0) {
            $update_category_data = [
                'category' =>  $request->name,
                'worktype_id' => $request->worktype,
                'subworktype_id' =>  $request->subworktype,
            ];
            DB::table('tbl_category')->where('id', $request->id)->update($update_category_data);
            //Category::where('id', $request->id)->update($update_category_data);
            $data = [
                'message' => "Category Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            //return $update_category_data;
        } else {
            $data = [
                'message' => "Duplicate Category ",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_category(Request $request)
    {
        $del_category_data = [
            'deleted_status' => 1,
        ];
        
        Category::where('id', $request->id)->update($del_category_data);
        //$delete = DB::table('tbl_divion_master')->where('id', $request->id)->delete();
        $data = [
            'message' => "Category Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
    }
    /************ Master tab Category end*/

    /******** Master tab sample test Start *********************/

    public function get_all_sampletest(Request $req)
    {
        $get_all_sampletest = Sampletest::where('deleted_status', 0)->orderBy('id','desc')->get();
        return Datatables::of($get_all_sampletest)
            ->addColumn('id', function ($get_all_sampletest) {
                $id = '';
                $id .= $get_all_sampletest->id;
                return $id . '';
            })
            ->addColumn('name', function ($get_all_sampletest) {
                $sampletest = '';
                $sampletest .= $get_all_sampletest->sampletest;
                return $sampletest . '';
            })
            ->addColumn('worktype', function ($get_all_sampletest) {
                $worktype = '';
                //$get_subworktype_id=Category::where('id',$get_all_sampletest->category_id)->pluck('subworktype_id');
                $get_worktype_id=Category::where('id',$get_all_sampletest->category_id)->pluck('worktype_id');
                $get_details=Worktype::where('id',$get_worktype_id[0])->pluck('worktype');
                $worktype .= $get_details[0];
                return $worktype . '';
            })
            ->addColumn('subworktype', function ($get_all_sampletest) {
                $subworktype = '';
                $get_subworktype_id=Category::where('id',$get_all_sampletest->category_id)->pluck('subworktype_id');
                if($get_subworktype_id[0]){
                    $get_details=Subworktype::where('id',$get_subworktype_id[0])->pluck('subworktype');
                    $subworktype .= $get_details[0];
                }else{
                    $subworktype .= '-';
                }
               
                return $subworktype . '';
            })
            ->addColumn('category', function ($get_all_sampletest) {
                $category = '';
                $get_details=Category::where('id',$get_all_sampletest->category_id)->pluck('category');
                $category .= $get_details[0];
                return $category . '';
            })
            ->addColumn('action', function ($get_all_sampletest) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-info" onClick="edit_sampletest(' . $get_all_sampletest->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_sampletest(' . $get_all_sampletest->id . ')"><i class="fa fa-trash"></i></button>
                ';
                return $action . '';
            })
            ->rawColumns(array("sampletest", "sampletest", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
    }
    public function save_sampletest(Request $request)
    {
        //dd($request->all());
        $count = Sampletest::whereRaw('LOWER(sampletest) = ?', [strtolower($request->name)])->where('category_id', $request->category)->where('deleted_status',0)->count();
        //var_dump($count);
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            // $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'sampletest' => $request->name,
                'category_id' => $request->category,
            );
            //dd($data);
            Sampletest::insert($data);
            $rdata = "success";
            $data = [
                'message' => "Sample Test Added Successfully!",
                "action" => "success"
            ];
        } else {
            $rdata = "fail";
            $data = [
                'message' => "Duplicate Sample test ",
                "action" => "warning"
            ];
        }
        return json_encode($data);
    }
    public function edit_sampletest(request $request){
        
        $get_list =DB::table('tbl_sampletest')->where('id',$request->id)->get();
        $get_worktype_id=Category::where('id',$get_list[0]->category_id)->pluck('worktype_id');
        $get_subworktype_id=Category::where('id',$get_list[0]->category_id)->pluck('subworktype_id');
        $additional_data = [];
        if ($get_subworktype_id[0]) {
            $additional_data["worktype_id"] = $get_worktype_id[0];
            $additional_data["subworktype_id"] = $get_subworktype_id[0];
        } else {
            $additional_data["worktype_id"] = $get_worktype_id[0];
            $additional_data["subworktype_id"] = '';
        }
        $get_list[0] = (object) array_merge((array) $get_list[0], $additional_data);
        return $get_list;
    }
    public function update_sampletest(Request $request)
    {
        $sampletest_count = Sampletest::whereRaw('LOWER(sampletest) = ?', [strtolower($request->name)])->where('category_id', $request->category)->where('id', '!=', $request->id)->where('deleted_status',0)->get()->count();
        // dd($request->all());
        if ($sampletest_count == 0) {
            $update_sampletest_data = [
                'sampletest' =>  $request->name,
                'category_id' =>  $request->category,
            ];
            DB::table('tbl_sampletest')->where('id', $request->id)->update($update_sampletest_data);
            //Sampletest::where('id', $request->id)->update($update_sampletest_data);
            $data = [
                'message' => "Sample Test Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            //return $update_sampletest_data;
        } else {
            $data = [
                'message' => "Duplicate Sample test",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_sampletest(Request $request)
    {
        $del_sampletest_data = [
            'deleted_status' => 1,
        ];
        
        Sampletest::where('id', $request->id)->update($del_sampletest_data);
        //$delete = DB::table('tbl_divion_master')->where('id', $request->id)->delete();
        $data = [
            'message' => "Sample Test Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
    }
    /************ Master tab sample test end*/

     /******** Master tab standard time Start *********************/

     public function get_all_standardtime(Request $req)
     {
        
         $get_all_standardtime = Standardtime::where('deleted_status', 0)->orderBy('id','desc')->get();
         return Datatables::of($get_all_standardtime)
            ->addColumn('id', function ($get_all_standardtime) {
                $id = '';
                $id .= $get_all_standardtime->id?$get_all_standardtime->id:'-';
                return $id . '';
            })
            /* ->addColumn('name', function ($get_all_standardtime) {
                $standardtime = '';
                $standardtime .= $get_all_standardtime->standardtime;
                return $standardtime . '';
            }) */
            ->addColumn('worktype', function ($get_all_standardtime) {
                $worktype = '';
                $get_details=Worktype::where('id',$get_all_standardtime->worktype)->pluck('worktype');
                $worktype .= $get_details[0]?$get_details[0]:'-';
                return $worktype . '';
            })
            ->addColumn('subworktype', function ($get_all_standardtime) {
                $subworktype = '';
                if($get_all_standardtime->subworktype){
                    $get_details=Subworktype::where('id',$get_all_standardtime->subworktype)->pluck('subworktype');
                    $subworktype .= $get_details[0]?$get_details[0]:'-';
                }else{
                    $subworktype .= '-';
                }
                return $subworktype . '';
            })
            ->addColumn('category', function ($get_all_standardtime) {
                $category = '';
                $get_details=Category::where('id',$get_all_standardtime->category)->pluck('category');
                $category .= $get_details[0]?$get_details[0]:'-';
                return $category . '';
            })
            ->addColumn('sampletest', function ($get_all_standardtime) {
                $sampletest = '';
                if($get_all_standardtime->sampletest){
                    $get_details=Sampletest::where('id',$get_all_standardtime->sampletest)->pluck('sampletest');
                    $sampletest .= $get_details[0]?$get_details[0]:'-';
                }else{
                    $sampletest .= '-';
                }
                
                return $sampletest . '';
            })
            ->addColumn('from_qty', function ($get_all_standardtime) {
                $from_qty = '';
                $from_qty .= $get_all_standardtime->from_qty?$get_all_standardtime->from_qty:'-';
                return $from_qty . '';
            })
            ->addColumn('to_qty', function ($get_all_standardtime) {
                $to_qty = '';
                $to_qty .= $get_all_standardtime->to_qty?$get_all_standardtime->to_qty:'-';
                return $to_qty . '';
            })
            ->addColumn('uom', function ($get_all_standardtime) {
                $uom = '';
                $uom .= $get_all_standardtime->uom?$get_all_standardtime->uom:'-';
                return $uom . '';
            })
            ->addColumn('single_point_time', function ($get_all_standardtime) {
                $single_point_time = '';
                $single_point_time .= $get_all_standardtime->single_point_time?$get_all_standardtime->single_point_time:'-';
                return $single_point_time . '';
            })
            ->addColumn('analysis_completion_time', function ($get_all_standardtime) {
                $analysis_completion_time = '';
                $analysis_completion_time .= $get_all_standardtime->analysis_completion_time?$get_all_standardtime->analysis_completion_time:'-';
                return $analysis_completion_time . '';
            })
            ->addColumn('max_sample_perday', function ($get_all_standardtime) {
                $max_sample_perday = '';
                $max_sample_perday .= $get_all_standardtime->max_sample_perday?$get_all_standardtime->max_sample_perday:'-';
                return $max_sample_perday . '';
            })
            ->addColumn('standard_time', function ($get_all_standardtime) {
                $standard_time = '';
                $standard_time .= $get_all_standardtime->standard_time?$get_all_standardtime->standard_time:'-';
                return $standard_time . '';
            })
            ->addColumn('action', function ($get_all_standardtime) {
                $action = '';
                $action .= ' <button class="btn btn-sm btn-info" onClick="edit_standardtime(' . $get_all_standardtime->id . ')" ><i class="fa fa-edit"></i></button>
                <button class="btn btn-sm btn-danger" onClick="delete_standardtime(' . $get_all_standardtime->id . ')"><i class="fa fa-trash"></i></button>
                ';
                return $action . '';
            })
            ->rawColumns(array( "standardtime", "action"))
            ->make(true);
        //  } catch (QueryException $e) {
        //     echo "Bad Request";
        //     dd();
        // }
     }
     public function save_standardtime(Request $request)
     {
         //dd($request->all());
         /* $count = Standardtime::where('standardtime', $request->name)->where('category_id', $request->category)->where('deleted_status',0)->count();
         if ($count == 0) {
            
             $data = array(
                 'standardtime' => $request->name,
                 'category_id' => $request->category,
             );
             //dd($data);
             Standardtime::insert($data);
             $rdata = "success";
             $data = [
                 'message' => "Standard time Added Successfully!",
                 "action" => "success"
             ];
         } else {
             $rdata = "fail";
             $data = [
                 'message' => "Duplicate Standard time ",
                 "action" => "warning"
             ];
         } */
         //var_dump($request->worktype);
         if(strtolower($request->worktype_name)=="formulation"){
            $formulation_count_condtion=0;
            foreach($request->from_qty as $key=>$value){
                //$formulation_count = Standardtime::where('standard_time',  $request->std_time_values[$key])->where('worktype', $request->worktype)->where('category', $request->category)->where('uom', $request->uom[$key])->where('from_qty', $value)->where('to_qty', $request->to_qty[$key])->where('deleted_status',0)->count();
                $formulation_count = Standardtime::where('worktype', $request->worktype)
                ->where('category', $request->category)
                ->where('uom', $request->uom[$key])
                ->where(function ($query) use ($value, $request, $key) {
                    $query->where(function ($query) use ($value, $request, $key) {
                        $query->where('from_qty', '<=', $value)
                            ->where('to_qty', '>=', $value);
                    })
                    ->orWhere(function ($query) use ($value, $request, $key) {
                        $query->where('from_qty', '<=', $request->to_qty[$key])
                            ->where('to_qty', '>=', $request->to_qty[$key]);
                    });
                })
                ->where('deleted_status', 0)
                ->count();
                if($formulation_count!=0){
                    $formulation_count_condtion=1;
                }
            }    
            if ($formulation_count_condtion!=1) {
                foreach($request->from_qty as $key=>$value){
                    $data = array(
                        'worktype' => $request->worktype,
                        'category' => $request->category,
                        'from_qty' => $value,
                        'to_qty' => $request->to_qty[$key],
                        'uom'=>$request->uom[$key],
                        'standard_time' => $request->std_time_values[$key],
                    );
                    //dd($data);
                    Standardtime::insert($data);
                }   
            } else {
                $data = [
                    'message' => "Duplicate Standardtime ",
                    "action" => "warning"
                ];
                return response()->json($data);
            }

            
         }elseif(strtolower($request->worktype_name)=="assays"){
                /*
                */
                if(strtolower($request->subworktype_name)=='measurement'){
                   
                    $measurement_count = Standardtime::where('worktype', $request->worktype)->where('category', $request->category)->where('subworktype', $request->subworktype)->where('sampletest', $request->sampletest)->where('deleted_status',0)->count();
                    if ($measurement_count==0) {
                        $data = array(
                            'worktype' => $request->worktype,
                            'category' => $request->category,
                            'subworktype' => $request->subworktype,
                            'sampletest' => $request->sampletest,
                            'single_point_time'=>$request->singlepoint,
                            'analysis_completion_time'=>$request->analysis,
                            'max_sample_perday'=>$request->maxsample,
                            //'standard_time' => $request->standardtime_micro,
                        );  
                    } else {
                        $data = [
                            'message' => "Duplicate Standardtime ",
                            "action" => "warning"
                        ];
                        return response()->json($data);
                    }

                    
                }else if(strtolower($request->subworktype_name)=='microbiology'){
                    $microbiology_count = Standardtime::where('worktype', $request->worktype)->where('category', $request->category)->where('subworktype', $request->subworktype)->where('sampletest', $request->sampletest)->where('deleted_status',0)->count();
                    if ($microbiology_count==0) {
                        $data = array(
                            'worktype' => $request->worktype,
                            'category' => $request->category,
                            'subworktype' => $request->subworktype,
                            'sampletest' => $request->sampletest,
                            //'single_point_time'=>$request->singlepoint,
                            //'analysis_completion_time'=>$request->analysis,
                            //'max_sample_perday'=>$request->maxsample,
                            'standard_time' => $request->standardtime_micro,
                        );
                    } else {
                        $data = [
                            'message' => "Duplicate Standardtime ",
                            "action" => "warning"
                        ];
                        return response()->json($data);
                    }
                    
                }
                
                //dd($data);
                Standardtime::insert($data);
         }
         
         
        //dd($data);
        $data = [
            'message' => "Standard time Added Successfully!",
            "action" => "success"
        ];
        
         return json_encode($data);
     }
     public function edit_standardtime(request $request){
        $get_list =DB::table('tbl_standardtime')->where('id',$request->id)->get();
        $get_worktype =DB::table('tbl_worktype')->where('id',$get_list[0]->worktype)->get();
        $get_list_array = (array) $get_list[0];
        $get_list_array['worktype_name'] = $get_worktype[0]->worktype;
        if($get_worktype[0]->worktype!='formulation'){
            $get_subworktype =DB::table('tbl_subworktype')->where('id',$get_list[0]->subworktype)->get();
            $get_list_array['subworktype_name'] = $get_subworktype[0]->subworktype;
        }
        $get_list[0] = $get_list_array;
        return $get_list;
     }
     public function update_standardtime(Request $request)
     {
         /* $standardtime_count = Standardtime::where('standardtime', '=', $request->name)->where('category_id', $request->category)->where('id', '!=', $request->id)->where('deleted_status',0)->get()->count();
         // dd($request->all());
         if ($standardtime_count == 0) {
             $update_standardtime_data = [
                 'standardtime' =>  $request->name,
                 'category_id' =>  $request->category,
             ];
             DB::table('tbl_standardtime')->where('id', $request->id)->update($update_standardtime_data);
             //Standardtime::where('id', $request->id)->update($update_standardtime_data);
             $data = [
                 'message' => "Standard time Updated Successfully!",
                 "action" => "success"
             ];
             return response()->json($data);
             //return $update_standardtime_data;
         } else {
             $data = [
                 'message' => "Duplicate Standard time",
                 "action" => "warning"
             ];
             return response()->json($data);
         } */

         if(strtolower($request->worktype_name)=='formulation'){
            //var_dump($request->from_qty);
            $formulation_count_condtion=0;
            foreach($request->from_qty as $key=>$value){
                //$formulation_count = Standardtime::where('standard_time',  $request->std_time_values[$key])->where('worktype', $request->worktype)->where('category', $request->category)->where('uom', $request->uom[$key])->where('from_qty', $value)->where('to_qty', $request->to_qty[$key])->where('id', '!=', $request->id)->where('deleted_status',0)->count();
                $formulation_count = Standardtime::where('worktype', $request->worktype)
                ->where('category', $request->category)
                ->where('uom', $request->uom[$key])
                ->where(function ($query) use ($value, $request, $key) {
                    $query->where(function ($query) use ($value, $request, $key) {
                        $query->where('from_qty', '<=', $value)
                            ->where('to_qty', '>=', $value);
                    })
                    ->orWhere(function ($query) use ($value, $request, $key) {
                        $query->where('from_qty', '<=', $request->to_qty[$key])
                            ->where('to_qty', '>=', $request->to_qty[$key]);
                    });
                })
                ->where('id', '!=', $request->id)
                ->where('deleted_status', 0)
                ->count();
                if($formulation_count!=0){
                    $formulation_count_condtion=1;
                }
            }    
            if ($formulation_count_condtion!=1) {
                foreach($request->from_qty as $key=>$value){
                    $data = array(
                        'worktype' => $request->worktype,
                        'category' => $request->category,
                        'from_qty' => $value,
                        'to_qty' => $request->to_qty[$key],
                        'uom'=>$request->uom[$key],
                        'standard_time' => $request->std_time_values[$key],
                    );
                    //dd($data);
                    DB::table('tbl_standardtime')->where('id', $request->id)->update($data);
                }  
            } else {
                $data = [
                    'message' => "Duplicate Standardtime ",
                    "action" => "warning"
                ];
                return response()->json($data);
            }

             
         }elseif(strtolower($request->worktype_name)=='assays'){
                /*
                */
                if(strtolower($request->subworktype_name)=='measurement'){
                    $measurement_count = Standardtime::where('worktype', $request->worktype)->where('category', $request->category)->where('subworktype', $request->subworktype)->where('sampletest', $request->sampletest)->where('id', '!=', $request->id)->where('deleted_status',0)->count();
                    if ($measurement_count==0) {
                        $data = array(
                            'worktype' => $request->worktype,
                            'category' => $request->category,
                            'subworktype' => $request->subworktype,
                            'sampletest' => $request->sampletest,
                            'single_point_time'=>$request->singlepoint,
                            'analysis_completion_time'=>$request->analysis,
                            'max_sample_perday'=>$request->maxsample,
                            //'standard_time' => $request->standardtime_micro,
                        );  
                    } else {
                        $data = [
                            'message' => "Duplicate Standardtime ",
                            "action" => "warning"
                        ];
                        return response()->json($data);
                    }
                    
                }else if(strtolower($request->subworktype_name)=="microbiology"){
                    $microbiology_count = Standardtime::where('worktype', $request->worktype)->where('category', $request->category)->where('subworktype', $request->subworktype)->where('sampletest', $request->sampletest)->where('id', '!=', $request->id)->where('deleted_status',0)->count();
                    if ($microbiology_count==0) {
                        $data = array(
                            'worktype' => $request->worktype,
                            'category' => $request->category,
                            'subworktype' => $request->subworktype,
                            'sampletest' => $request->sampletest,
                            //'single_point_time'=>$request->singlepoint,
                            //'analysis_completion_time'=>$request->analysis,
                            //'max_sample_perday'=>$request->maxsample,
                            'standard_time' => $request->standardtime_micro,
                        );
                    } else {
                        $data = [
                            'message' => "Duplicate Standardtime ",
                            "action" => "warning"
                        ];
                        return response()->json($data);
                    }
                    
                }
                
                //dd($data);
                DB::table('tbl_standardtime')->where('id', $request->id)->update($data);
         }
         $data = [
            'message' => "Standard time Updated Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
     }
     public function delete_standardtime(Request $request)
     {
         $del_standardtime_data = [
             'deleted_status' => 1,
         ];
         
         Standardtime::where('id', $request->id)->update($del_standardtime_data);
         //$delete = DB::table('tbl_divion_master')->where('id', $request->id)->delete();
         $data = [
             'message' => "Standard Time Deleted Successfully!",
             "action" => "success"
         ];
         return response()->json($data);
     }
     /************ Master tab standard time end*/
 

     /****************************************** Master User Start ***********/
     public function get_all_user(Request $req)
     {
         // try {
         /* $get_all_user = DB::table('users')
                             ->join('users', 'users.supervisor_id', '=', 'users.id')
                             ->select('users.*', 'users.name as sup_name')
                             ->orderBy('users.id', 'desc')
                             ->get(); */
         $get_all_user = User::whereIn('typeofwork',[1,2,3])->orderBy('id','desc')->get();
         //dd( $get_all_user );
         return Datatables::of($get_all_user)
             ->addColumn('user_id', function ($get_all_user) {
                 $user_id = '';
                 $user_id .= $get_all_user->id;
                 return $user_id . '';
             })
             ->addColumn('user_empid', function ($get_all_user) {
                $user_empid = '';
                $user_empid .= $get_all_user->employee_id;
                return $user_empid . '';
            })
             ->addColumn('user_name', function ($get_all_user) {
                 $user_name = '';
                 $user_name .= ucfirst($get_all_user->name);
                 return $user_name . '';
             })
             ->addColumn('user_email', function ($get_all_user) {
                 $user_email = '';
                 $user_email .= $get_all_user->email;
                 return $user_email . '';
             })
             ->addColumn('user_division', function ($get_all_user) {
                 $user_division = '';
                 $division_name = Division::where('id', $get_all_user->division_id)->first();
                 if (!empty($division_name)) {
                     $user_division .= $division_name->division_name;
                 } else {
                     $user_division .= '-';
                 }
                 return $user_division . '';
             })
             ->addColumn('user_typeofwork', function ($get_all_user) {
                 $typeofwork = '';
                 if($get_all_user->typeofwork==1){
                    $typeofwork = "formulation";
                 }else if($get_all_user->typeofwork==2){
                    $typeofwork = "assays";
                 }else if($get_all_user->typeofwork==3){
                    $typeofwork = "all";
                 }else{
                    $typeofwork = "-";
                 }
                 return $typeofwork . '';
             })
             
             ->addColumn('action', function ($get_all_user) {
                 $action = '';
                 $action .= ' <button class="btn btn-sm btn-info" onClick="edit_user(' . $get_all_user->id . ')" ><i class="fa fa-edit"></i></button>
                 
                 ';
                 //<button class="btn btn-sm btn-danger" onClick="delete_user(' . $get_all_user->id . ')"><i class="fa fa-trash"></i></button>
                 $status = '';
                 if ($get_all_user->status == '1') {
                     $status .= '<select style="border:none; color:blue;" onchange="update_user_status(' . $get_all_user->id . ',0)">
                          <option selected="selected" hidden style="border:none; color:blue;">Active</option>
                          <option style="border:none; color:red;">In-Active</option>
                          </select>';
                 } else {
                     $status .= '<select style="border:none; color:red;" onchange="update_user_status(' . $get_all_user->id . ',1)">
                          <option selected="selected" hidden style="border:none; color:red;">In-Active</option>
                          <option style="border:none; color:blue;">Active</option>
                          </select>';
                 }
                 return $action . ''.$status;
             })
             /* ->addColumn('status', function ($get_all_user) {
                 $status = '';
                 if ($get_all_user->status == '1') {
                     $status .= '<select style="border:none; color:blue;" onchange="update_user_status(' . $get_all_user->id . ',2)">
                          <option selected="selected" hidden style="border:none; color:blue;">Active</option>
                          <option style="border:none; color:red;">In-Active</option>
                          </select>';
                 } else {
                     $status .= '<select style="border:none; color:red;" onchange="update_user_status(' . $get_all_user->id . ',1)">
                          <option selected="selected" hidden style="border:none; color:blue;">In-Active</option>
                          <option style="border:none; color:blue;">Active</option>
                          </select>';
                 }
                 return $status . '';
             })  */
             ->rawColumns(array("user_id", "user_empid","user_name", "user_email", "user_division", "user_typeofwork",  "action"))
             ->make(true);
         //  } catch (QueryException $e) {
         //     echo "Bad Request";
         //     dd();
         // }
     }
     public function save_user(Request $request)
    {
        $count = User::whereRaw('LOWER(email) = ?', [strtolower($request->email)])->orWhereRaw('LOWER(employee_id) = ?', [strtolower($request->empid)])->where('status',1)->count();
        //dd($count);
        if ($count == 0) {
            // $user_id = Auth::user()->id;
            $curent_date = date('Y-m-d H:i:s');
            $data = array(
                'name' => $request->name,
                'employee_id' => $request->empid,
                'email' => $request->email,
                'division_id' => $request->division,
                'company'=>'HEPL',
                'typeofwork'=>$request->typeofwork,
                'supervisor_employee_id' =>  900001,
                'supervisor_id' =>  84,
                'is_supervisor' => 0,
                'role' => 8,
                'hepl_role'=>2,
                'password' => Hash::make('12345678'),
                'created_at' => $curent_date,
                'status' => 1,
            );
            //dd($data);
            User::insert($data);
            $rdata = "success";
            $data = [
                'message' => "User Added Successfully!",
                "action" => "success"
            ];
        } else {
            $rdata = "fail";
            $data = [
                'message' => "Duplicate User ",
                "action" => "warning"
            ];
        }
        return json_encode($data);
    }
    public function edit_user(request $request){
        $get_list =DB::table('users')->where('id',$request->id)->get();
        return $get_list;
    }
    public function update_user(Request $request)
    {
        //dd($request->all());
        $email_count = User::whereRaw('LOWER(email) = ?', [strtolower($request->email)])->orWhereRaw('LOWER(employee_id) = ?', [strtolower($request->empid)])->where('id', '!=', $request->id)->where('status',1)->get()->count();
         
        if ($email_count == 0) {
            $update_user_data = [
                'name' => $request->name,
                'employee_id' => $request->empid,
                'email' => $request->email,
                'division_id' => $request->division,
                'typeofwork'=>$request->typeofwork,
            
                
            ];
            $tett=DB::table('users')->where('id', $request->id)->update($update_user_data);
            //User::where('id', $request->user_id)->update($update_user_data);
            $data = [
                'message' => "User Updated Successfully!",
                "action" => "success"
            ];
            return response()->json($data);
            
        } else {
            $data = [
                'message' => "Duplicate User ",
                "action" => "warning"
            ];
            return response()->json($data);
        }
    }
    public function delete_user(Request $request)
    {
        // $email_count = User::where('email', '=', $request->user_email)->where('id', '!=', $request->user_id)->get()->count();
        // dd($request->all());
        $del_user_data = [
            'status' => 0,
        ];
        User::where('id', $request->user_id)->update($del_user_data);
        $data = [
            'message' => "User Deleted Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
    }
    public function update_user_status(Request $request)
    {
        $status_user_data = [
            'status' => $request->status,
        ];
        User::where('id', $request->id)->update($status_user_data);
        $data = [
            'message' => "User Status Updated Successfully!",
            "action" => "success"
        ];
        return response()->json($data);
    }
    /***************  Master tab scientist End ********/ 
    public function get_category(Request $request){
        //dd($request->all());
        $get_list='';
        if($request->workTypeId && $request->subWorkTypeId){
            $get_list =DB::table('tbl_category')->where('worktype_id',$request->workTypeId)->where('subworktype_id',$request->subWorkTypeId)->where('deleted_status',0)->get();
        }elseif($request->workTypeId){
            $get_list =DB::table('tbl_category')->where('worktype_id',$request->workTypeId)->where('deleted_status',0)->get();
        }elseif($request->subWorkTypeId){
            $get_list =DB::table('tbl_category')->where('subworktype_id',$request->subWorkTypeId)->where('deleted_status',0)->get();
        }
        return $get_list;
    }
    public function get_sampletest(Request $request){
        //dd($request->all());
        $get_list =DB::table('tbl_sampletest')->where('category_id',$request->category)->where('deleted_status',0)->get();
        return $get_list;
    }
    public function getsubwork_worktype(Request $request){
        //dd($request->all());
        $get_list='';
        
        $get_list = Worktype::where('worktype', 'assays')->where('deleted_status', 0)->get();
        return $get_list;
    }
    public function get_worktype(Request $request){
        $get_list='';
        $get_list = Worktype::where('deleted_status', 0)->get();
        return $get_list;
    }
    public function get_subworktype(Request $request){
        $get_list='';
        $workTypeId=$request->workTypeId;
        if($workTypeId){
            $get_list = Subworktype::where('worktype_id',$workTypeId)->where('deleted_status', 0)->get();
        }else{
            $get_list = Subworktype::where('deleted_status', 0)->get();
        }
        
        return $get_list;
    }
    


    
}
