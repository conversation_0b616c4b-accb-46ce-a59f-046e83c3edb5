    // for ($i = 1; $i <= $current_month; $i++) {
    //     $month_data =  DB::table('tbl_project_tasks')->join('level_status', 'tbl_project_tasks.level', '=', 'level_status.id')->where('tbl_project_tasks.deleted_status', 0)->where('level_status.month', "$academic_year_1-0$i")->where('project_name', $project_name)->count();
    //     if($month_data == 0){
    //         $start_date =
    //         $row_data[] = [
    //             $project_name,
    //             "deleted",
    //             "#B2A5A3",
    //             $start_date,
    //             $end_date,
    //             $start_year,
    //             $end_year,
    //             null,
    //             null,
    //             null,   
    //             null,
    //             null,
    //             $start_month
    //         ];
    //     }
        
    // }