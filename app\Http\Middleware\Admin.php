<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;

class Admin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
       // $roles = json_decode(Auth::user()->role);

        //if (in_array("1", $roles)) {
         if (Auth::user()->role == 1) {

           /* if(Auth::user()->EMP_ID=="EMP001"){
                return $next($request);
            }
            else{
                abort(403, "Cannot access to restricted page");
            } */
            return $next($request);
        }
        abort(403, "Cannot access to restricted page");
    }
}
