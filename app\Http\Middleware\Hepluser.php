<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
use App\Models\Modules;

class Hepluser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */

    public function handle(Request $request, Closure $next)
    {
       // $roles = json_decode(Auth::user()->role);

       // if (in_array("3", $roles)) {

         if (Auth::user()->role == 8) {

             return $next($request);
         }
         abort(403, "Cannot access to restricted page");
    }


}
