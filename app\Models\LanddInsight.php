<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LanddInsight extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'tbl_landd_insights';


    protected $fillable = [
        'insights_generated',
        'scientist',
        'insights_category',
        'remark',
        'created_date',
        'created_current_date',
        'created_exact_date',
        'finance_status',
        'created_by',
    ];


   
}
