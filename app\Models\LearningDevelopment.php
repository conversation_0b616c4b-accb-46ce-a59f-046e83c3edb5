<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LearningDevelopment extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'tbl_learning_devlopment';
    public $timestamps = false;

    protected $fillable = [
        'month',
        'no_of_hours_invested_in_iiy',
        'no_of_vendor_facility_visited',
        'no_of_conference_attended',
        'no_of_exhibitions_attended',
        'no_of_consumer_and_retail_visits',
        'topics_present',
        'topics_learnt',
        'upload_the_presentation_weekly',
        'no_of_ideas_generated_through_iiy',
        'date_range_start',
        'date_range_end',
        'created_by',
        'updated_at',
        'deleted_status',
        'created_date',
    ];

    /**
     * Get the area of development that owns the LearningDevelopment
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    // public function area_of_dev(){
    //     return $this->hasOne(ParentIdea::class, 'id','area_of_development');
    // }

    // public function source_of_learning(){
    //     return $this->hasOne(IiyDigitalLearning::class,'id','source_learning');
    // }
}
