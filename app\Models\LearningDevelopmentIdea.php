<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LearningDevelopmentIdea extends Model
{

    // use HasFactory;
    // protected $guarded = [];
    // protected $table = 'tbl_learning_devlopment_idea';
    use HasFactory;
    protected $guarded = [];
    protected $table = 'tbl_learning_devlopment_idea';
    public $timestamps = false;

    // protected $fillable = [
    //     'idea_type',
    //     'parent_idea',
    //     'child_idea',
    //     'parent_desc',
    //     'ideas_generated',
    //     'scientist',
    //     'scientist_percentage',
    //     'created_by',
    //     'idea_category',
    //     'idea_insight',
    //     'childideastatus',
    //     'idea_child_upload',
    //     'finance_status',
    //     'created_date',
    //     'created_current_date',
    //     'created_exact_date',
    //     'deleted_status',
    //     'level',
    //     'ideas_comments',
    //     'area_of_dev',
    //     'idea_txt',
    //     'potential_outcomes',	
    //     'parent_status',
    // ];
}
