"use strict";$(function(){$("#datatable-1").DataTable({responsive:!0,footerCallback:function(t,e,n,r,a){var c=this.api(),o=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0});function u(t){return"string"==typeof t?+t.replace(/[\$,]/g,""):"number"==typeof t?t:0}var i=c.column(7,{page:"current"}).data().reduce(function(t,e){return u(t)+u(e)},0);$(c.column(7).footer()).html("Total: ".concat(o.format(i)))}})});