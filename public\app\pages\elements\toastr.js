"use strict";$(function(){var m,k=-1,v=0,f=["My name is <PERSON><PERSON>. You killed my father. Prepare to die!",'<div>\n      <input class="form-control mb-2" placeholder="Input text" >\n      <a href="//google.com" target="_blank">This is a hyperlink</a>\n    </div>\n    <div>\n      <button type="button" class="btn btn-primary okBtn mr-1 mt-2">Close me</button>\n      <button type="button" class="btn btn-text-light surpriseBtn mt-2">Surprise me</button>\n    </div>',"Are you the six fingered man?","Inconceivable!","I do not think that means what you think it means.","Have fun storming the castle!"];$("#showtoast").click(function(){var t=$("#toastTypeGroup input:radio:checked").val(),o=$("#message").val()||f[k=++k===f.length?0:k],e=$("#title").val()||"",n=$("#showDuration").val(),a=$("#hideDuration").val(),s=$("#timeOut").val(),i=$("#extendedTimeOut").val(),r=$("#showEasing").val(),c=$("#hideEasing").val(),l=$("#showMethod").val(),p=$("#hideMethod").val(),u=$("#addBehaviorOnToastClick").prop("checked"),h=$("#addClear").prop("checked"),d=v++,n=(toastr.options={closeButton:$("#closeButton").prop("checked"),debug:$("#debugInfo").prop("checked"),newestOnTop:$("#newestOnTop").prop("checked"),progressBar:$("#progressBar").prop("checked"),positionClass:$("#positionGroup input:radio:checked").val()||"toast-top-right",preventDuplicates:$("#preventDuplicates").prop("checked"),onclick:null},u&&(toastr.options.onclick=function(){alert("You can perform some custom action after a toast goes away")}),n.length&&(toastr.options.showDuration=parseInt(n)),a.length&&(toastr.options.hideDuration=parseInt(a)),s.length&&(toastr.options.timeOut=h?0:parseInt(s)),i.length&&(toastr.options.extendedTimeOut=h?0:parseInt(i)),r.length&&(toastr.options.showEasing=r),c.length&&(toastr.options.hideEasing=c),l.length&&(toastr.options.showMethod=l),p.length&&(toastr.options.hideMethod=p),h&&(u=(u=o)||"Clear it self?",o=u+='<br><button type="button" class="btn btn-primary clear mt-2">Close</button>',toastr.options.tapToDismiss=!1),"toastr.options = ".concat(JSON.stringify(toastr.options,null,4))),a="toastr.".concat(t,"(`").concat(o,"`").concat(e?', "'+e+'"':"",")"),g=($("#toastrOptions").text("".concat(n,"\n\n").concat(a)),toastr[t](o,e));void 0!==(m=g)&&(g.find(".okBtn").length&&g.delegate(".okBtn","click",function(){alert("you clicked me. i was toast #".concat(d,". goodbye!")),g.remove()}),g.find(".surpriseBtn").length&&g.delegate(".surpriseBtn","click",function(){alert("Surprise! you clicked me. i was toast #".concat(d,". You could perform an action here."))}),g.find(".clear").length&&g.delegate(".clear","click",function(){toastr.clear(g,{force:!0})}))}),$("#clearlasttoast").click(function(){toastr.clear(m)}),$("#cleartoasts").click(function(){toastr.clear()})});