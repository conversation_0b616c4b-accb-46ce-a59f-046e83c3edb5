"use strict";function t(t,e){var r,o=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,r)),o}function l(o){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?t(Object(a),!0).forEach(function(e){var t,r;t=o,r=a[e=e],e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(a,e))})}return o}$(function(){var e="rtl"===$("html").attr("dir"),o=($("#widget-carousel").slick({rtl:e,asNavFor:"#widget-carousel-nav",slidesToShow:1,slidesToScroll:1,arrows:!1}),$("#widget-carousel-nav").slick({rtl:e,asNavFor:"#widget-carousel",slidesToShow:1,slidesToScroll:1,arrows:!1,centerMode:!0}),"dark"==localStorage.getItem("theme-variant")),a=o?"dark":"light",e=(new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}),"#2196f3"),s="#fff",n="#424242",i={light:{theme:{mode:"light",palette:"palette1"}},dark:{theme:{mode:"dark",palette:"palette1"}}},r=$(".widget-chart-3").map(function(){var e=$(this).data("chart-color"),t=$(this).data("chart-label"),r=$(this).data("chart-series").split(",").map(function(e){return Number(e)});return new ApexCharts(this,l(l({},i[a]),{},{series:[{name:t,data:r}],chart:{type:"area",height:50,background:"transparent",sparkline:{enabled:!0}},fill:{opacity:0,type:"solid"},stroke:{show:!0,colors:[e],lineCap:"round"},markers:{colors:[o?n:s],strokeWidth:4,strokeColors:e},tooltip:{followCursor:!0,marker:{show:!1},x:{show:!1},y:{formatter:function(e){return"".concat(e," Tests")}},fixed:{enabled:!0,position:"topLeft",offsetY:-30}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"],crosshairs:{show:!1}}}))}),c=new ApexCharts(document.querySelector("#widget-chart-5"),l(l({},i[a]),{},{series:[{name:"Sales",data:[640,400,760,620,980,640]}],chart:{type:"area",background:"transparent",height:300,toolbar:{show:!1}},dataLabels:{enabled:!1},fill:{opacity:0,type:"solid"},stroke:{show:!0,colors:[e],lineCap:"round"},markers:{colors:[o?n:s],strokeWidth:4,strokeColors:e},tooltip:{marker:{show:!1},y:{formatter:function(e){return"".concat(e," Products")}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"]}}));r.each(function(){this.render()}),c.render(),$("#theme-toggle").on("click",function(){var e="dark"==$("html").attr("data-theme"),t=e?"dark":"light";r.each(function(){this.updateOptions(l(l({},i[t]),{},{markers:{colors:[e?n:s]}}))}),c.updateOptions(l(l({},i[t]),{},{markers:{colors:[e?n:s]}}))})});