"use strict";function r(r,e){var t,a=Object.keys(r);return Object.getOwnPropertySymbols&&(t=Object.getOwnPropertySymbols(r),e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),a.push.apply(a,t)),a}function b(a){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?r(Object(o),!0).forEach(function(e){var r,t;r=a,t=o[e=e],e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(o,e))})}return a}$(function(){var o="dark"==localStorage.getItem("theme-variant"),s=o?"dark":"light",n=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}),e="#2196f3",r="#4caf50",t="#f44336",i="#fff",c="#424242",l={light:{theme:{mode:"light",palette:"palette1"}},dark:{theme:{mode:"dark",palette:"palette1"}}},a=new ApexCharts(document.querySelector("#widget-chart-1"),b(b({},l[s]),{},{series:[{name:"Revenue",data:[3100,4e3,2800,5100,4200,10900,5600,8600,7e3]}],chart:{type:"area",height:300,background:"transparent",sparkline:{enabled:!0}},fill:{type:"solid"},markers:{strokeColors:o?c:i},stroke:{show:!1},tooltip:{marker:{show:!1},y:{formatter:function(e){return n.format(e)}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep"],crosshairs:{show:!1}},responsive:[{breakpoint:576,options:{chart:{height:200}}}]})),h=new ApexCharts(document.querySelector("#widget-chart-2"),b(b({},l[s]),{},{series:[{name:"Revenue",data:[4e3,9600,4600,13600,6800,14600,11e3]},{name:"Profit",data:[3100,8e3,2800,5100,2e3,10900,1e4]}],chart:{type:"area",height:300,background:"transparent",sparkline:{enabled:!0}},fill:{opacity:.8,type:"solid"},markers:{strokeColors:o?c:i},stroke:{show:!1},tooltip:{y:{formatter:function(e){return n.format(e)}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep"],crosshairs:{show:!1}},responsive:[{breakpoint:576,options:{chart:{height:200}}}]})),p=$(".widget-chart-3").map(function(){var e=$(this).data("chart-color"),r=$(this).data("chart-label"),t=$(this).data("chart-series").split(",").map(function(e){return Number(e)});return new ApexCharts(this,b(b({},l[s]),{},{series:[{name:r,data:t}],chart:{type:"area",height:50,background:"transparent",sparkline:{enabled:!0}},fill:{opacity:0,type:"solid"},stroke:{show:!0,colors:[e],lineCap:"round"},markers:{colors:[o?c:i],strokeWidth:4,strokeColors:e},tooltip:{followCursor:!0,marker:{show:!1},x:{show:!1},y:{formatter:function(e){return"".concat(e," Tests")}},fixed:{enabled:!0,position:"topLeft",offsetY:-30}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"],crosshairs:{show:!1}}}))}),u=new ApexCharts(document.querySelector("#widget-chart-4"),b(b({},l[s]),{},{series:[2e3,7e3,1e3],labels:["Margin","Profit","Lost"],chart:{width:300,type:"donut",background:"transparent"},stroke:{colors:[o?c:i]},colors:[e,r,t],tooltip:{fillSeriesColor:!1,y:{formatter:function(e){return n.format(e)}}},legend:{show:!1},dataLabels:{enabled:!1}})),d=new ApexCharts(document.querySelector("#widget-chart-5"),b(b({},l[s]),{},{series:[{name:"Sales",data:[640,400,760,620,980,640]}],chart:{type:"area",background:"transparent",height:300,toolbar:{show:!1}},dataLabels:{enabled:!1},fill:{opacity:0,type:"solid"},stroke:{show:!0,colors:[e],lineCap:"round"},markers:{colors:[o?c:i],strokeWidth:4,strokeColors:e},tooltip:{marker:{show:!1},y:{formatter:function(e){return"".concat(e," Products")}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"]}})),m=new ApexCharts(document.querySelector("#widget-chart-6"),b(b({},l[s]),{},{series:[{name:"Unique",data:[6400,4e3,7600,6200,9800,6400,8600,7e3]}],chart:{type:"area",background:"transparent",height:300,sparkline:{enabled:!0}},markers:{strokeColors:o?c:i},fill:{type:"gradient",gradient:{shade:s,type:"vertical",opacityFrom:1,opacityTo:0,stops:[0,100]}},tooltip:{marker:{show:!1},y:{formatter:function(e){return"".concat(e," Visited")}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug"],crosshairs:{show:!1}}})),f=$(".widget-chart-7").map(function(){var e=$(this).data("chart-color"),r=$(this).data("chart-label"),t=$(this).data("chart-series").split(",").map(function(e){return Number(e)}),a=$(this).data("chart-currency");return new ApexCharts(this,b(b({},l[s]),{},{series:[{name:r,data:t}],chart:{type:"area",height:200,background:"transparent",sparkline:{enabled:!0}},fill:{type:"solid",colors:[e],opacity:.1},stroke:{show:!0,colors:[e]},markers:{colors:[o?c:i],strokeWidth:4,strokeColors:e},tooltip:{marker:{show:!1},y:{formatter:function(e){return Boolean(a)?n.format(e):e}}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"],crosshairs:{show:!1}}}))}),k=new ApexCharts(document.querySelector("#widget-chart-8"),b(b({},l[s]),{},{series:[{name:"Profit",data:[4400,5500,5700,5600,6100,5800,6300,6e3]},{name:"Revenue",data:[7600,8500,10100,9800,8700,10500,9100,11400]},{name:"Free Cash",data:[3500,4100,3600,2600,4500,4800,5200,5300]}],chart:{type:"bar",height:200,background:"transparent",sparkline:{enabled:!0}},fill:{opacity:1},stroke:{show:!0,width:2,colors:["transparent"]},plotOptions:{bar:{horizontal:!1}},dataLabels:{enabled:!1},xaxis:{categories:["Feb","Mar","Apr","May","Jun","Jul","Aug","Sep"]},tooltip:{y:{formatter:function(e){return n.format(e)}}}})),y=$(".widget-chart-9").map(function(){var e=$(this).data("chart-color"),r=$(this).data("chart-label"),t=$(this).data("chart-series").split(",").map(function(e){return Number(e)});return new ApexCharts(this,b(b({},l[s]),{},{series:[{name:r,data:t}],chart:{type:"area",height:150,background:"transparent",sparkline:{enabled:!0}},fill:{type:"solid",opacity:0},markers:{strokeColors:o?c:i},stroke:{show:!0,colors:[e],lineCap:"round"},tooltip:{marker:{show:!1}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun"],crosshairs:{show:!1}}}))});a.render(),h.render(),p.each(function(){this.render()}),u.render(),d.render(),m.render(),f.each(function(){this.render()}),k.render(),y.each(function(){this.render()}),$("#theme-toggle").on("click",function(){var e="dark"==$("html").attr("data-theme"),r=e?"dark":"light";a.updateOptions(b(b({},l[r]),{},{markers:{strokeColors:e?c:i}})),h.updateOptions(b(b({},l[r]),{},{markers:{strokeColors:e?c:i}})),p.each(function(){this.updateOptions(b(b({},l[r]),{},{markers:{colors:[e?c:i]}}))}),u.updateOptions(b(b({},l[r]),{},{stroke:{colors:[e?c:i]}})),d.updateOptions(b(b({},l[r]),{},{markers:{colors:[e?c:i]}})),m.updateOptions(b(b({},l[r]),{},{markers:{strokeColors:e?c:i},fill:{gradient:{shade:r}}})),f.each(function(){this.updateOptions(b(b({},l[r]),{},{markers:{colors:[e?c:i]}}))}),k.updateOptions(l[r]),y.each(function(){this.updateOptions(b(b({},l[r]),{},{markers:{strokeColors:e?c:i}}))})})});