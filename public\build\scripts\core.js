var t,e;t=this,e=function(t){"use strict";function M(t){if(t&&t.__esModule)return t;const e=Object.create(null);if(t)for(const s in t){var i;"default"!==s&&(i=Object.getOwnPropertyDescriptor(t,s),Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>t[s]}))}return e.default=t,Object.freeze(e)}const H=M(t),q=1e3,B="transitionend",z=e=>{let i=e.getAttribute("data-bs-target");if(!i||"#"===i){let t=e.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t="#"+t.split("#")[1]),i=t&&"#"!==t?t.trim():null}return i},R=t=>{t=z(t);return t&&document.querySelector(t)?t:null},o=t=>{t=z(t);return t?document.querySelector(t):null},F=t=>{t.dispatchEvent(new Event(B))},a=t=>!(!t||"object"!=typeof t)&&void 0!==(t=void 0!==t.jquery?t[0]:t).nodeType,s=t=>a(t)?t.jquery?t[0]:t:"string"==typeof t&&0<t.length?document.querySelector(t):null,i=(s,n,o)=>{Object.keys(o).forEach(t=>{var e=o[t],i=n[t],i=i&&a(i)?"element":null==(i=i)?""+i:{}.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(e).test(i))throw new TypeError(s.toUpperCase()+`: Option "${t}" provided type "${i}" but expected type "${e}".`)})},n=t=>!(!a(t)||0===t.getClientRects().length)&&"visible"===getComputedStyle(t).getPropertyValue("visibility"),r=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),W=t=>{return document.documentElement.attachShadow?"function"==typeof t.getRootNode?(e=t.getRootNode())instanceof ShadowRoot?e:null:t instanceof ShadowRoot?t:t.parentNode?W(t.parentNode):null:null;var e},$=()=>{},d=t=>{t.offsetHeight},U=()=>{var t=window["jQuery"];return t&&!document.body.hasAttribute("data-bs-no-jquery")?t:null},Q=[],l=()=>"rtl"===document.documentElement.dir;t=s=>{var t;t=()=>{const t=U();if(t){const e=s.NAME,i=t.fn[e];t.fn[e]=s.jQueryInterface,t.fn[e].Constructor=s,t.fn[e].noConflict=()=>(t.fn[e]=i,s.jQueryInterface)}},"loading"===document.readyState?(Q.length||document.addEventListener("DOMContentLoaded",()=>{Q.forEach(t=>t())}),Q.push(t)):t()};const c=t=>{"function"==typeof t&&t()},K=(i,s,t=!0)=>{if(t){t=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);var t=Number.parseFloat(e),s=Number.parseFloat(i);return t||s?(e=e.split(",")[0],i=i.split(",")[0],(Number.parseFloat(e)+Number.parseFloat(i))*q):0})(s)+5;let e=!1;const n=({target:t})=>{t===s&&(e=!0,s.removeEventListener(B,n),c(i))};s.addEventListener(B,n),setTimeout(()=>{e||F(s)},t)}else c(i)},V=(t,e,i,s)=>{let n=t.indexOf(e);if(-1===n)return t[!i&&s?t.length-1:0];e=t.length;return n+=i?1:-1,s&&(n=(n+e)%e),t[Math.max(0,Math.min(n,e-1))]},X=/[^.]*(?=\..*)\.|.*/,Y=/\..*/,G=/::\d+$/,Z={};let J=1;const tt={mouseenter:"mouseover",mouseleave:"mouseout"},et=/^(mouseenter|mouseleave)/i,it=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function st(t,e){return e&&e+"::"+J++||t.uidEvent||J++}function nt(t){var e=st(t);return t.uidEvent=e,Z[e]=Z[e]||{},Z[e]}function ot(i,s,n=null){var o=Object.keys(i);for(let t=0,e=o.length;t<e;t++){var a=i[o[t]];if(a.originalHandler===s&&a.delegationSelector===n)return a}return null}function at(t,e,i){var s="string"==typeof e,i=s?i:e;let n=ct(t);e=it.has(n);return[s,i,n=e?n:t]}function rt(t,e,i,s,n){if("string"==typeof e&&t){i||(i=s,s=null),et.test(e)&&(o=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)},s?s=o(s):i=o(i));var[o,a,r]=at(e,i,s);const f=nt(t),m=f[r]||(f[r]={}),g=ot(m,a,o?i:null);if(g)g.oneOff=g.oneOff&&n;else{var l,c,h,d,u,e=st(a,e.replace(X,""));const p=o?(h=t,d=i,u=s,function i(s){var n=h.querySelectorAll(d);for(let e=s["target"];e&&e!==this;e=e.parentNode)for(let t=n.length;t--;)if(n[t]===e)return s.delegateTarget=e,i.oneOff&&_.off(h,s.type,d,u),u.apply(e,[s]);return null}):(l=t,c=i,function t(e){return e.delegateTarget=l,t.oneOff&&_.off(l,e.type,c),c.apply(l,[e])});p.delegationSelector=o?i:null,p.originalHandler=a,p.oneOff=n,p.uidEvent=e,m[e]=p,t.addEventListener(r,p,o)}}}function lt(t,e,i,s,n){s=ot(e[i],s,n);s&&(t.removeEventListener(i,s,Boolean(n)),delete e[i][s.uidEvent])}function ct(t){return t=t.replace(Y,""),tt[t]||t}const _={on(t,e,i,s){rt(t,e,i,s,!1)},one(t,e,i,s){rt(t,e,i,s,!0)},off(a,r,t,e){if("string"==typeof r&&a){const[i,s,n]=at(r,t,e),o=n!==r,l=nt(a);e=r.startsWith(".");if(void 0!==s)return l&&l[n]?void lt(a,l,n,s,i?t:null):void 0;e&&Object.keys(l).forEach(t=>{{var e=a,i=l,s=t,n=r.slice(1);const o=i[s]||{};return void Object.keys(o).forEach(t=>{t.includes(n)&&(t=o[t],lt(e,i,s,t.originalHandler,t.delegationSelector))})}});const c=l[n]||{};Object.keys(c).forEach(t=>{var e=t.replace(G,"");o&&!r.includes(e)||(e=c[t],lt(a,l,n,e.originalHandler,e.delegationSelector))})}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const s=U();var n=ct(e),o=e!==n,a=it.has(n);let r,l=!0,c=!0,h=!1,d=null;return o&&s&&(r=s.Event(e,i),s(t).trigger(r),l=!r.isPropagationStopped(),c=!r.isImmediatePropagationStopped(),h=r.isDefaultPrevented()),a?(d=document.createEvent("HTMLEvents")).initEvent(n,l,!0):d=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==i&&Object.keys(i).forEach(t=>{Object.defineProperty(d,t,{get(){return i[t]}})}),h&&d.preventDefault(),c&&t.dispatchEvent(d),d.defaultPrevented&&void 0!==r&&r.preventDefault(),d}},h=new Map,u={set(t,e,i){h.has(t)||h.set(t,new Map);const s=h.get(t);s.has(e)||0===s.size?s.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get(t,e){return h.has(t)&&h.get(t).get(e)||null},remove(t,e){if(h.has(t)){const i=h.get(t);i.delete(e),0===i.size&&h.delete(t)}}};class e{constructor(t){(t=s(t))&&(this._element=t,u.set(this._element,this.constructor.DATA_KEY,this))}dispose(){u.remove(this._element,this.constructor.DATA_KEY),_.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this).forEach(t=>{this[t]=null})}_queueCallback(t,e,i=!0){K(t,e,i)}static getInstance(t){return u.get(s(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.1.3"}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}static get DATA_KEY(){return"bs."+this.NAME}static get EVENT_KEY(){return"."+this.DATA_KEY}}var ht=(i,s="hide")=>{var t="click.dismiss"+i.EVENT_KEY;const n=i.NAME;_.on(document,t,`[data-bs-dismiss="${n}"]`,function(t){if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),!r(this)){t=o(this)||this.closest("."+n);const e=i.getOrCreateInstance(t);e[s]()}})};class dt extends e{static get NAME(){return"alert"}close(){var t;_.trigger(this._element,"close.bs.alert").defaultPrevented||(this._element.classList.remove("show"),t=this._element.classList.contains("fade"),this._queueCallback(()=>this._destroyElement(),this._element,t))}_destroyElement(){this._element.remove(),_.trigger(this._element,"closed.bs.alert"),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=dt.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}ht(dt,"close"),t(dt);const ut='[data-bs-toggle="button"]';class ft extends e{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=ft.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}function mt(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function gt(t){return t.replace(/[A-Z]/g,t=>"-"+t.toLowerCase())}_.on(document,"click.bs.button.data-api",ut,t=>{t.preventDefault();t=t.target.closest(ut);const e=ft.getOrCreateInstance(t);e.toggle()}),t(ft);const f={setDataAttribute(t,e,i){t.setAttribute("data-bs-"+gt(e),i)},removeDataAttribute(t,e){t.removeAttribute("data-bs-"+gt(e))},getDataAttributes(i){if(!i)return{};const s={};return Object.keys(i.dataset).filter(t=>t.startsWith("bs")).forEach(t=>{let e=t.replace(/^bs/,"");e=e.charAt(0).toLowerCase()+e.slice(1,e.length),s[e]=mt(i.dataset[t])}),s},getDataAttribute(t,e){return mt(t.getAttribute("data-bs-"+gt(e)))},offset(t){t=t.getBoundingClientRect();return{top:t.top+window.pageYOffset,left:t.left+window.pageXOffset}},position(t){return{top:t.offsetTop,left:t.offsetLeft}}},m={find(t,e=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(e,t))},findOne(t,e=document.documentElement){return Element.prototype.querySelector.call(e,t)},children(t,e){return[].concat(...t.children).filter(t=>t.matches(e))},parents(t,e){const i=[];let s=t.parentNode;for(;s&&s.nodeType===Node.ELEMENT_NODE&&3!==s.nodeType;)s.matches(e)&&i.push(s),s=s.parentNode;return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){var e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>t+':not([tabindex^="-"])').join(", ");return this.find(e,t).filter(t=>!r(t)&&n(t))}},pt="carousel";var g=".bs.carousel";const _t={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},vt={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},p="next",v="prev",b="left",y="right",bt={ArrowLeft:y,ArrowRight:b},yt="slid"+g;const E="active",Et=".active.carousel-item";class w extends e{constructor(t,e){super(t),this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._indicatorsElement=m.findOne(".carousel-indicators",this._element),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners()}static get Default(){return _t}static get NAME(){return pt}next(){this._slide(p)}nextWhenVisible(){!document.hidden&&n(this._element)&&this.next()}prev(){this._slide(v)}pause(t){t||(this._isPaused=!0),m.findOne(".carousel-item-next, .carousel-item-prev",this._element)&&(F(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null}cycle(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))}to(t){this._activeElement=m.findOne(Et,this._element);var e=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)_.one(this._element,yt,()=>this.to(t));else{if(e===t)return this.pause(),void this.cycle();e=e<t?p:v;this._slide(e,this._items[t])}}_getConfig(t){return t={..._t,...f.getDataAttributes(this._element),..."object"==typeof t?t:{}},i(pt,t,vt),t}_handleSwipe(){var t=Math.abs(this.touchDeltaX);t<=40||(t=t/this.touchDeltaX,this.touchDeltaX=0,t&&this._slide(0<t?y:b))}_addEventListeners(){this._config.keyboard&&_.on(this._element,"keydown.bs.carousel",t=>this._keydown(t)),"hover"===this._config.pause&&(_.on(this._element,"mouseenter.bs.carousel",t=>this.pause(t)),_.on(this._element,"mouseleave.bs.carousel",t=>this.cycle(t))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()}_addTouchEventListeners(){const e=t=>this._pointerEvent&&("pen"===t.pointerType||"touch"===t.pointerType),i=t=>{e(t)?this.touchStartX=t.clientX:this._pointerEvent||(this.touchStartX=t.touches[0].clientX)},s=t=>{this.touchDeltaX=t.touches&&1<t.touches.length?0:t.touches[0].clientX-this.touchStartX},n=t=>{e(t)&&(this.touchDeltaX=t.clientX-this.touchStartX),this._handleSwipe(),"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(t=>this.cycle(t),500+this._config.interval))};m.find(".carousel-item img",this._element).forEach(t=>{_.on(t,"dragstart.bs.carousel",t=>t.preventDefault())}),this._pointerEvent?(_.on(this._element,"pointerdown.bs.carousel",t=>i(t)),_.on(this._element,"pointerup.bs.carousel",t=>n(t)),this._element.classList.add("pointer-event")):(_.on(this._element,"touchstart.bs.carousel",t=>i(t)),_.on(this._element,"touchmove.bs.carousel",t=>s(t)),_.on(this._element,"touchend.bs.carousel",t=>n(t)))}_keydown(t){var e;/input|textarea/i.test(t.target.tagName)||(e=bt[t.key])&&(t.preventDefault(),this._slide(e))}_getItemIndex(t){return this._items=t&&t.parentNode?m.find(".carousel-item",t.parentNode):[],this._items.indexOf(t)}_getItemByOrder(t,e){t=t===p;return V(this._items,e,t,this._config.wrap)}_triggerSlideEvent(t,e){var i=this._getItemIndex(t),s=this._getItemIndex(m.findOne(Et,this._element));return _.trigger(this._element,"slide.bs.carousel",{relatedTarget:t,direction:e,from:s,to:i})}_setActiveIndicatorElement(e){if(this._indicatorsElement){const t=m.findOne(".active",this._indicatorsElement),i=(t.classList.remove(E),t.removeAttribute("aria-current"),m.find("[data-bs-target]",this._indicatorsElement));for(let t=0;t<i.length;t++)if(Number.parseInt(i[t].getAttribute("data-bs-slide-to"),10)===this._getItemIndex(e)){i[t].classList.add(E),i[t].setAttribute("aria-current","true");break}}}_updateInterval(){const t=this._activeElement||m.findOne(Et,this._element);var e;t&&((e=Number.parseInt(t.getAttribute("data-bs-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval)}_slide(t,e){t=this._directionToOrder(t);const i=m.findOne(Et,this._element),s=this._getItemIndex(i),n=e||this._getItemByOrder(t,i),o=this._getItemIndex(n);var e=Boolean(this._interval),a=t===p;const r=a?"carousel-item-start":"carousel-item-end",l=a?"carousel-item-next":"carousel-item-prev",c=this._orderToDirection(t);if(n&&n.classList.contains(E))this._isSliding=!1;else if(!this._isSliding){a=this._triggerSlideEvent(n,c);if(!a.defaultPrevented&&i&&n){this._isSliding=!0,e&&this.pause(),this._setActiveIndicatorElement(n),this._activeElement=n;const h=()=>{_.trigger(this._element,yt,{relatedTarget:n,direction:c,from:s,to:o})};this._element.classList.contains("slide")?(n.classList.add(l),d(n),i.classList.add(r),n.classList.add(r),this._queueCallback(()=>{n.classList.remove(r,l),n.classList.add(E),i.classList.remove(E,l,r),this._isSliding=!1,setTimeout(h,0)},i,!0)):(i.classList.remove(E),n.classList.add(E),this._isSliding=!1,h()),e&&this.cycle()}}}_directionToOrder(t){return[y,b].includes(t)?l()?t===b?v:p:t===b?p:v:t}_orderToDirection(t){return[p,v].includes(t)?l()?t===v?b:y:t===v?y:b:t}static carouselInterface(t,e){const i=w.getOrCreateInstance(t,e);let s=i["_config"];"object"==typeof e&&(s={...s,...e});t="string"==typeof e?e:s.slide;if("number"==typeof e)i.to(e);else if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}else s.interval&&s.ride&&(i.pause(),i.cycle())}static jQueryInterface(t){return this.each(function(){w.carouselInterface(this,t)})}static dataApiClickHandler(t){const e=o(this);if(e&&e.classList.contains("carousel")){const s={...f.getDataAttributes(e),...f.getDataAttributes(this)};var i=this.getAttribute("data-bs-slide-to");i&&(s.interval=!1),w.carouselInterface(e,s),i&&w.getInstance(e).to(i),t.preventDefault()}}}_.on(document,"click.bs.carousel.data-api","[data-bs-slide], [data-bs-slide-to]",w.dataApiClickHandler),_.on(window,"load.bs.carousel.data-api",()=>{var i=m.find('[data-bs-ride="carousel"]');for(let t=0,e=i.length;t<e;t++)w.carouselInterface(i[t],w.getInstance(i[t]))}),t(w);const wt="collapse",Ct="bs.collapse";Ct;const Tt={toggle:!0,parent:null},At={toggle:"boolean",parent:"(null|element)"};const kt="show",C="collapse",Lt="collapsing",St="collapsed",Dt=`:scope .${C} .`+C,Nt='[data-bs-toggle="collapse"]';class T extends e{constructor(t,e){super(t),this._isTransitioning=!1,this._config=this._getConfig(e),this._triggerArray=[];var i=m.find(Nt);for(let t=0,e=i.length;t<e;t++){var s=i[t],n=R(s),o=m.find(n).filter(t=>t===this._element);null!==n&&o.length&&(this._selector=n,this._triggerArray.push(s))}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Tt}static get NAME(){return wt}toggle(){this._isShown()?this.hide():this.show()}show(){if(!this._isTransitioning&&!this._isShown()){let t=[],e;if(this._config.parent){const n=m.find(Dt,this._config.parent);t=m.find(".collapse.show, .collapse.collapsing",this._config.parent).filter(t=>!n.includes(t))}const s=m.findOne(this._selector);if(t.length){var i=t.find(t=>s!==t);if((e=i?T.getInstance(i):null)&&e._isTransitioning)return}i=_.trigger(this._element,"show.bs.collapse");if(!i.defaultPrevented){t.forEach(t=>{s!==t&&T.getOrCreateInstance(t,{toggle:!1}).hide(),e||u.set(t,Ct,null)});const o=this._getDimension();this._element.classList.remove(C),this._element.classList.add(Lt),this._element.style[o]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;i="scroll"+(o[0].toUpperCase()+o.slice(1));this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Lt),this._element.classList.add(C,kt),this._element.style[o]="",_.trigger(this._element,"shown.bs.collapse")},this._element,!0),this._element.style[o]=this._element[i]+"px"}}}hide(){if(!this._isTransitioning&&this._isShown()){var t=_.trigger(this._element,"hide.bs.collapse");if(!t.defaultPrevented){var t=this._getDimension(),e=(this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",d(this._element),this._element.classList.add(Lt),this._element.classList.remove(C,kt),this._triggerArray.length);for(let t=0;t<e;t++){var i=this._triggerArray[t],s=o(i);s&&!this._isShown(s)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Lt),this._element.classList.add(C),_.trigger(this._element,"hidden.bs.collapse")},this._element,!0)}}}_isShown(t=this._element){return t.classList.contains(kt)}_getConfig(t){return(t={...Tt,...f.getDataAttributes(this._element),...t}).toggle=Boolean(t.toggle),t.parent=s(t.parent),i(wt,t,At),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent){const e=m.find(Dt,this._config.parent);m.find(Nt,this._config.parent).filter(t=>!e.includes(t)).forEach(t=>{var e=o(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))})}}_addAriaAndCollapsedClass(t,e){t.length&&t.forEach(t=>{e?t.classList.remove(St):t.classList.add(St),t.setAttribute("aria-expanded",e)})}static jQueryInterface(i){return this.each(function(){const t={},e=("string"==typeof i&&/show|hide/.test(i)&&(t.toggle=!1),T.getOrCreateInstance(this,t));if("string"==typeof i){if(void 0===e[i])throw new TypeError(`No method named "${i}"`);e[i]()}})}}_.on(document,"click.bs.collapse.data-api",Nt,function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();t=R(this);const e=m.find(t);e.forEach(t=>{T.getOrCreateInstance(t,{toggle:!1}).toggle()})}),t(T);const Ot="dropdown";var g=".bs.dropdown",A=".data-api";const It="Escape",jt="ArrowUp",xt="ArrowDown",Pt=new RegExp(jt+`|${xt}|`+It);var k="click"+g+A,g="keydown"+g+A;const L="show",S='[data-bs-toggle="dropdown"]',Mt=".dropdown-menu",Ht=l()?"top-end":"top-start",qt=l()?"top-start":"top-end",Bt=l()?"bottom-end":"bottom-start",zt=l()?"bottom-start":"bottom-end",Rt=l()?"left-start":"right-start",Ft=l()?"right-start":"left-start",Wt={offset:[0,2],boundary:"clippingParents",reference:"toggle",display:"dynamic",popperConfig:null,autoClose:!0},$t={offset:"(array|string|function)",boundary:"(string|element)",reference:"(string|element|object)",display:"string",popperConfig:"(null|object|function)",autoClose:"(boolean|string)"};class D extends e{constructor(t,e){super(t),this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar()}static get Default(){return Wt}static get DefaultType(){return $t}static get NAME(){return Ot}toggle(){return this._isShown()?this.hide():this.show()}show(){if(!r(this._element)&&!this._isShown(this._menu)){var t={relatedTarget:this._element},e=_.trigger(this._element,"show.bs.dropdown",t);if(!e.defaultPrevented){const i=D.getParentFromElement(this._element);this._inNavbar?f.setDataAttribute(this._menu,"popper","none"):this._createPopper(i),"ontouchstart"in document.documentElement&&!i.closest(".navbar-nav")&&[].concat(...document.body.children).forEach(t=>_.on(t,"mouseover",$)),this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(L),this._element.classList.add(L),_.trigger(this._element,"shown.bs.dropdown",t)}}}hide(){var t;!r(this._element)&&this._isShown(this._menu)&&(t={relatedTarget:this._element},this._completeHide(t))}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){_.trigger(this._element,"hide.bs.dropdown",t).defaultPrevented||("ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>_.off(t,"mouseover",$)),this._popper&&this._popper.destroy(),this._menu.classList.remove(L),this._element.classList.remove(L),this._element.setAttribute("aria-expanded","false"),f.removeDataAttribute(this._menu,"popper"),_.trigger(this._element,"hidden.bs.dropdown",t))}_getConfig(t){if(t={...this.constructor.Default,...f.getDataAttributes(this._element),...t},i(Ot,t,this.constructor.DefaultType),"object"!=typeof t.reference||a(t.reference)||"function"==typeof t.reference.getBoundingClientRect)return t;throw new TypeError(Ot.toUpperCase()+': Option "reference" provided type "object" without a required "getBoundingClientRect" method.')}_createPopper(t){if(void 0===H)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=t:a(this._config.reference)?e=s(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const i=this._getPopperConfig();t=i.modifiers.find(t=>"applyStyles"===t.name&&!1===t.enabled);this._popper=H.createPopper(e,this._menu,i),t&&f.setDataAttribute(this._menu,"popper","static")}_isShown(t=this._element){return t.classList.contains(L)}_getMenuElement(){return m.next(this._element,Mt)[0]}_getPlacement(){const t=this._element.parentNode;if(t.classList.contains("dropend"))return Rt;if(t.classList.contains("dropstart"))return Ft;var e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?qt:Ht:e?zt:Bt}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const e=this._config["offset"];return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return"static"===this._config.display&&(t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,..."function"==typeof this._config.popperConfig?this._config.popperConfig(t):this._config.popperConfig}}_selectMenuItem({key:t,target:e}){const i=m.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(n);i.length&&V(i,e,t===xt,!i.includes(e)).focus()}static jQueryInterface(e){return this.each(function(){const t=D.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(i){if(!i||2!==i.button&&("keyup"!==i.type||"Tab"===i.key)){var s=m.find(S);for(let t=0,e=s.length;t<e;t++){const o=D.getInstance(s[t]);if(o&&!1!==o._config.autoClose&&o._isShown()){const a={relatedTarget:o._element};if(i){const r=i.composedPath();var n=r.includes(o._menu);if(r.includes(o._element)||"inside"===o._config.autoClose&&!n||"outside"===o._config.autoClose&&n)continue;if(o._menu.contains(i.target)&&("keyup"===i.type&&"Tab"===i.key||/input|select|option|textarea|form/i.test(i.target.tagName)))continue;"click"===i.type&&(a.clickEvent=i)}o._completeHide(a)}}}}static getParentFromElement(t){return o(t)||t.parentNode}static dataApiKeydownHandler(t){if(/input|textarea/i.test(t.target.tagName)?!("Space"===t.key||t.key!==It&&(t.key!==xt&&t.key!==jt||t.target.closest(Mt))):Pt.test(t.key)){var e=this.classList.contains(L);if((e||t.key!==It)&&(t.preventDefault(),t.stopPropagation(),!r(this))){var i=this.matches(S)?this:m.prev(this,S)[0];const s=D.getOrCreateInstance(i);if(t.key!==It)return t.key===jt||t.key===xt?(e||s.show(),void s._selectMenuItem(t)):void(e&&"Space"!==t.key||D.clearMenus());s.hide()}}}}_.on(document,g,S,D.dataApiKeydownHandler),_.on(document,g,Mt,D.dataApiKeydownHandler),_.on(document,k,D.clearMenus),_.on(document,"keyup.bs.dropdown.data-api",D.clearMenus),_.on(document,k,S,function(t){t.preventDefault(),D.getOrCreateInstance(this).toggle()}),t(D);const Ut=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Qt=".sticky-top";class Kt{constructor(){this._element=document.body}getWidth(){var t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,"paddingRight",t=>t+e),this._setElementAttributes(Ut,"paddingRight",t=>t+e),this._setElementAttributes(Qt,"marginRight",t=>t-e)}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,i,s){const n=this.getWidth();this._applyManipulationCallback(t,t=>{var e;t!==this._element&&window.innerWidth>t.clientWidth+n||(this._saveInitialAttribute(t,i),e=window.getComputedStyle(t)[i],t.style[i]=s(Number.parseFloat(e))+"px")})}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,"paddingRight"),this._resetElementAttributes(Ut,"paddingRight"),this._resetElementAttributes(Qt,"marginRight")}_saveInitialAttribute(t,e){var i=t.style[e];i&&f.setDataAttribute(t,e,i)}_resetElementAttributes(t,i){this._applyManipulationCallback(t,t=>{var e=f.getDataAttribute(t,i);void 0===e?t.style.removeProperty(i):(f.removeDataAttribute(t,i),t.style[i]=e)})}_applyManipulationCallback(t,e){a(t)?e(t):m.find(t,this._element).forEach(e)}isOverflowing(){return 0<this.getWidth()}}const Vt={className:"modal-backdrop",isVisible:!0,isAnimated:!1,rootElement:"body",clickCallback:null},Xt={className:"string",isVisible:"boolean",isAnimated:"boolean",rootElement:"(element|string)",clickCallback:"(function|null)"},Yt="backdrop",Gt="mousedown.bs."+Yt;class Zt{constructor(t){this._config=this._getConfig(t),this._isAppended=!1,this._element=null}show(t){this._config.isVisible?(this._append(),this._config.isAnimated&&d(this._getElement()),this._getElement().classList.add("show"),this._emulateAnimation(()=>{c(t)})):c(t)}hide(t){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation(()=>{this.dispose(),c(t)})):c(t)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_getConfig(t){return(t={...Vt,..."object"==typeof t?t:{}}).rootElement=s(t.rootElement),i(Yt,t,Xt),t}_append(){this._isAppended||(this._config.rootElement.append(this._getElement()),_.on(this._getElement(),Gt,()=>{c(this._config.clickCallback)}),this._isAppended=!0)}dispose(){this._isAppended&&(_.off(this._element,Gt),this._element.remove(),this._isAppended=!1)}_emulateAnimation(t){K(t,this._getElement(),this._config.isAnimated)}}const Jt={trapElement:null,autofocus:!0},te={trapElement:"element",autofocus:"boolean"};const ee=".bs.focustrap",ie=(ee,ee,"backward");class se{constructor(t){this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}activate(){const{trapElement:t,autofocus:e}=this._config;this._isActive||(e&&t.focus(),_.off(document,ee),_.on(document,"focusin.bs.focustrap",t=>this._handleFocusin(t)),_.on(document,"keydown.tab.bs.focustrap",t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,_.off(document,ee))}_handleFocusin(t){t=t.target;const e=this._config["trapElement"];if(t!==document&&t!==e&&!e.contains(t)){const i=m.focusableChildren(e);(0===i.length?e:this._lastTabNavDirection===ie?i[i.length-1]:i[0]).focus()}}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?ie:"forward")}_getConfig(t){return t={...Jt,..."object"==typeof t?t:{}},i("focustrap",t,te),t}}const N=".bs.modal";const ne={backdrop:!0,keyboard:!0,focus:!0},oe={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean"},ae=(N,N,"hidden"+N),re="show"+N,le=(N,"resize"+N),ce="click.dismiss"+N,he="keydown.dismiss"+N,de=(N,"mousedown.dismiss"+N);N;const ue="modal-open",fe="modal-static";class O extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._dialog=m.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollBar=new Kt}static get Default(){return ne}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||_.trigger(this._element,re,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isAnimated()&&(this._isTransitioning=!0),this._scrollBar.hide(),document.body.classList.add(ue),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),_.on(this._dialog,de,()=>{_.one(this._element,"mouseup.dismiss.bs.modal",t=>{t.target===this._element&&(this._ignoreBackdropClick=!0)})}),this._showBackdrop(()=>this._showElement(t)))}hide(){var t;!this._isShown||this._isTransitioning||_.trigger(this._element,"hide.bs.modal").defaultPrevented||(this._isShown=!1,(t=this._isAnimated())&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),this._focustrap.deactivate(),this._element.classList.remove("show"),_.off(this._element,ce),_.off(this._dialog,de),this._queueCallback(()=>this._hideModal(),this._element,t))}dispose(){[window,this._dialog].forEach(t=>_.off(t,N)),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Zt({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new se({trapElement:this._element})}_getConfig(t){return t={...ne,...f.getDataAttributes(this._element),..."object"==typeof t?t:{}},i("modal",t,oe),t}_showElement(t){var e=this._isAnimated();const i=m.findOne(".modal-body",this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),e&&d(this._element),this._element.classList.add("show");this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,_.trigger(this._element,"shown.bs.modal",{relatedTarget:t})},this._dialog,e)}_setEscapeEvent(){this._isShown?_.on(this._element,he,t=>{this._config.keyboard&&"Escape"===t.key?(t.preventDefault(),this.hide()):this._config.keyboard||"Escape"!==t.key||this._triggerBackdropTransition()}):_.off(this._element,he)}_setResizeEvent(){this._isShown?_.on(window,le,()=>this._adjustDialog()):_.off(window,le)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(ue),this._resetAdjustments(),this._scrollBar.reset(),_.trigger(this._element,ae)})}_showBackdrop(t){_.on(this._element,ce,t=>{this._ignoreBackdropClick?this._ignoreBackdropClick=!1:t.target===t.currentTarget&&(!0===this._config.backdrop?this.hide():"static"===this._config.backdrop&&this._triggerBackdropTransition())}),this._backdrop.show(t)}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){var t=_.trigger(this._element,"hidePrevented.bs.modal");if(!t.defaultPrevented){const{classList:e,scrollHeight:i,style:s}=this._element,n=i>document.documentElement.clientHeight;!n&&"hidden"===s.overflowY||e.contains(fe)||(n||(s.overflowY="hidden"),e.add(fe),this._queueCallback(()=>{e.remove(fe),n||this._queueCallback(()=>{s.overflowY=""},this._dialog)},this._dialog),this._element.focus())}}_adjustDialog(){var t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=0<e;(!i&&t&&!l()||i&&!t&&l())&&(this._element.style.paddingLeft=e+"px"),(i&&!t&&!l()||!i&&t&&l())&&(this._element.style.paddingRight=e+"px")}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,i){return this.each(function(){const t=O.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](i)}})}}_.on(document,"click.bs.modal.data-api",'[data-bs-toggle="modal"]',function(t){const e=o(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),_.one(e,re,t=>{t.defaultPrevented||_.one(e,ae,()=>{n(this)&&this.focus()})});t=m.findOne(".modal.show");t&&O.getInstance(t).hide();const i=O.getOrCreateInstance(e);i.toggle(this)}),ht(O),t(O);const me="offcanvas";A=".bs.offcanvas";const ge={backdrop:!0,keyboard:!0,scroll:!1},pe={backdrop:"boolean",keyboard:"boolean",scroll:"boolean"},_e=".offcanvas.show",ve="hidden"+A;class I extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get NAME(){return me}static get Default(){return ge}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||_.trigger(this._element,"show.bs.offcanvas",{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._element.style.visibility="visible",this._backdrop.show(),this._config.scroll||(new Kt).hide(),this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add("show"),this._queueCallback(()=>{this._config.scroll||this._focustrap.activate(),_.trigger(this._element,"shown.bs.offcanvas",{relatedTarget:t})},this._element,!0))}hide(){this._isShown&&!_.trigger(this._element,"hide.bs.offcanvas").defaultPrevented&&(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.remove("show"),this._backdrop.hide(),this._queueCallback(()=>{this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._element.style.visibility="hidden",this._config.scroll||(new Kt).reset(),_.trigger(this._element,ve)},this._element,!0))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_getConfig(t){return t={...ge,...f.getDataAttributes(this._element),..."object"==typeof t?t:{}},i(me,t,pe),t}_initializeBackDrop(){return new Zt({className:"offcanvas-backdrop",isVisible:this._config.backdrop,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:()=>this.hide()})}_initializeFocusTrap(){return new se({trapElement:this._element})}_addEventListeners(){_.on(this._element,"keydown.dismiss.bs.offcanvas",t=>{this._config.keyboard&&"Escape"===t.key&&this.hide()})}static jQueryInterface(e){return this.each(function(){const t=I.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}_.on(document,"click.bs.offcanvas.data-api",'[data-bs-toggle="offcanvas"]',function(t){var e=o(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),!r(this)){_.one(e,ve,()=>{n(this)&&this.focus()});t=m.findOne(_e);t&&t!==e&&I.getInstance(t).hide();const i=I.getOrCreateInstance(e);i.toggle(this)}}),_.on(window,"load.bs.offcanvas.data-api",()=>m.find(_e).forEach(t=>I.getOrCreateInstance(t).show())),ht(I),t(I);const be=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]);const ye=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,Ee=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;g={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function we(t,i,e){if(!t.length)return t;if(e&&"function"==typeof e)return e(t);const s=new window.DOMParser,n=s.parseFromString(t,"text/html");var o=[].concat(...n.body.querySelectorAll("*"));for(let t=0,e=o.length;t<e;t++){const r=o[t];var a=r.nodeName.toLowerCase();if(Object.keys(i).includes(a)){const l=[].concat(...r.attributes),c=[].concat(i["*"]||[],i[a]||[]);l.forEach(t=>{((t,e)=>{var i=t.nodeName.toLowerCase();if(e.includes(i))return!be.has(i)||Boolean(ye.test(t.nodeValue)||Ee.test(t.nodeValue));const s=e.filter(t=>t instanceof RegExp);for(let t=0,e=s.length;t<e;t++)if(s[t].test(i))return!0;return!1})(t,c)||r.removeAttribute(t.nodeName)})}else r.remove()}return n.body.innerHTML}const Ce="tooltip";k=".bs.tooltip";const Te=new Set(["sanitize","allowList","sanitizeFn"]),Ae={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(array|string|function)",container:"(string|element|boolean)",fallbackPlacements:"array",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object|function)"},ke={AUTO:"auto",TOP:"top",RIGHT:l()?"left":"right",BOTTOM:"bottom",LEFT:l()?"right":"left"},Le={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:[0,0],container:!1,fallbackPlacements:["top","right","bottom","left"],boundary:"clippingParents",customClass:"",sanitize:!0,sanitizeFn:null,allowList:g,popperConfig:null},Se={HIDE:"hide"+k,HIDDEN:"hidden"+k,SHOW:"show"+k,SHOWN:"shown"+k,INSERTED:"inserted"+k,CLICK:"click"+k,FOCUSIN:"focusin"+k,FOCUSOUT:"focusout"+k,MOUSEENTER:"mouseenter"+k,MOUSELEAVE:"mouseleave"+k},De="fade";const j="show",Ne="show",Oe=".tooltip-inner",Ie="hide.bs.modal",je="hover",xe="focus";class x extends e{constructor(t,e){if(void 0===H)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t),this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this._config=this._getConfig(e),this.tip=null,this._setListeners()}static get Default(){return Le}static get NAME(){return Ce}static get Event(){return Se}static get DefaultType(){return Ae}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(t){if(this._isEnabled)if(t){const e=this._initializeOnDelegatedTarget(t);e._activeTrigger.click=!e._activeTrigger.click,e._isWithActiveTrigger()?e._enter(null,e):e._leave(null,e)}else this.getTipElement().classList.contains(j)?this._leave(null,this):this._enter(null,this)}dispose(){clearTimeout(this._timeout),_.off(this._element.closest(".modal"),Ie,this._hideModalHandler),this.tip&&this.tip.remove(),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var t=_.trigger(this._element,this.constructor.Event.SHOW);const i=W(this._element);var e=(null===i?this._element.ownerDocument.documentElement:i).contains(this._element);if(!t.defaultPrevented&&e){"tooltip"===this.constructor.NAME&&this.tip&&this.getTitle()!==this.tip.querySelector(Oe).innerHTML&&(this._disposePopper(),this.tip.remove(),this.tip=null);const s=this.getTipElement();t=(t=>{for(;t+=Math.floor(1e6*Math.random()),document.getElementById(t););return t})(this.constructor.NAME),e=(s.setAttribute("id",t),this._element.setAttribute("aria-describedby",t),this._config.animation&&s.classList.add(De),"function"==typeof this._config.placement?this._config.placement.call(this,s,this._element):this._config.placement),t=this._getAttachment(e);this._addAttachmentClass(t);const n=this._config["container"],o=(u.set(s,this.constructor.DATA_KEY,this),this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(s),_.trigger(this._element,this.constructor.Event.INSERTED)),this._popper?this._popper.update():this._popper=H.createPopper(this._element,s,this._getPopperConfig(t)),s.classList.add(j),this._resolvePossibleFunction(this._config.customClass));o&&s.classList.add(...o.split(" ")),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>{_.on(t,"mouseover",$)});e=this.tip.classList.contains(De);this._queueCallback(()=>{var t=this._hoverState;this._hoverState=null,_.trigger(this._element,this.constructor.Event.SHOWN),"out"===t&&this._leave(null,this)},this.tip,e)}}}hide(){if(this._popper){const e=this.getTipElement();var t;_.trigger(this._element,this.constructor.Event.HIDE).defaultPrevented||(e.classList.remove(j),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>_.off(t,"mouseover",$)),this._activeTrigger.click=!1,this._activeTrigger[xe]=!1,this._activeTrigger[je]=!1,t=this.tip.classList.contains(De),this._queueCallback(()=>{this._isWithActiveTrigger()||(this._hoverState!==Ne&&e.remove(),this._cleanTipClass(),this._element.removeAttribute("aria-describedby"),_.trigger(this._element,this.constructor.Event.HIDDEN),this._disposePopper())},this.tip,t),this._hoverState="")}}update(){null!==this._popper&&this._popper.update()}isWithContent(){return Boolean(this.getTitle())}getTipElement(){if(this.tip)return this.tip;const t=document.createElement("div"),e=(t.innerHTML=this._config.template,t.children[0]);return this.setContent(e),e.classList.remove(De,j),this.tip=e,this.tip}setContent(t){this._sanitizeAndSetContent(t,this.getTitle(),Oe)}_sanitizeAndSetContent(t,e,i){const s=m.findOne(i,t);!e&&s?s.remove():this.setElementContent(s,e)}setElementContent(t,e){if(null!==t)return a(e)?(e=s(e),void(this._config.html?e.parentNode!==t&&(t.innerHTML="",t.append(e)):t.textContent=e.textContent)):void(this._config.html?(this._config.sanitize&&(e=we(e,this._config.allowList,this._config.sanitizeFn)),t.innerHTML=e):t.textContent=e)}getTitle(){var t=this._element.getAttribute("data-bs-original-title")||this._config.title;return this._resolvePossibleFunction(t)}updateAttachment(t){return"right"===t?"end":"left"===t?"start":t}_initializeOnDelegatedTarget(t,e){return e||this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_getOffset(){const e=this._config["offset"];return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(t){return"function"==typeof t?t.call(this._element):t}_getPopperConfig(t){t={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"onChange",enabled:!0,phase:"afterWrite",fn:t=>this._handlePopperPlacementChange(t)}],onFirstUpdate:t=>{t.options.placement!==t.placement&&this._handlePopperPlacementChange(t)}};return{...t,..."function"==typeof this._config.popperConfig?this._config.popperConfig(t):this._config.popperConfig}}_addAttachmentClass(t){this.getTipElement().classList.add(this._getBasicClassPrefix()+"-"+this.updateAttachment(t))}_getAttachment(t){return ke[t.toUpperCase()]}_setListeners(){const t=this._config.trigger.split(" ");t.forEach(t=>{var e;"click"===t?_.on(this._element,this.constructor.Event.CLICK,this._config.selector,t=>this.toggle(t)):"manual"!==t&&(e=t===je?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,t=t===je?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT,_.on(this._element,e,this._config.selector,t=>this._enter(t)),_.on(this._element,t,this._config.selector,t=>this._leave(t)))}),this._hideModalHandler=()=>{this._element&&this.hide()},_.on(this._element.closest(".modal"),Ie,this._hideModalHandler),this._config.selector?this._config={...this._config,trigger:"manual",selector:""}:this._fixTitle()}_fixTitle(){var t=this._element.getAttribute("title"),e=typeof this._element.getAttribute("data-bs-original-title");!t&&"string"==e||(this._element.setAttribute("data-bs-original-title",t||""),!t||this._element.getAttribute("aria-label")||this._element.textContent||this._element.setAttribute("aria-label",t),this._element.setAttribute("title",""))}_enter(t,e){e=this._initializeOnDelegatedTarget(t,e),t&&(e._activeTrigger["focusin"===t.type?xe:je]=!0),e.getTipElement().classList.contains(j)||e._hoverState===Ne?e._hoverState=Ne:(clearTimeout(e._timeout),e._hoverState=Ne,e._config.delay&&e._config.delay.show?e._timeout=setTimeout(()=>{e._hoverState===Ne&&e.show()},e._config.delay.show):e.show())}_leave(t,e){e=this._initializeOnDelegatedTarget(t,e),t&&(e._activeTrigger["focusout"===t.type?xe:je]=e._element.contains(t.relatedTarget)),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e._config.delay&&e._config.delay.hide?e._timeout=setTimeout(()=>{"out"===e._hoverState&&e.hide()},e._config.delay.hide):e.hide())}_isWithActiveTrigger(){for(const t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1}_getConfig(t){const e=f.getDataAttributes(this._element);return Object.keys(e).forEach(t=>{Te.has(t)&&delete e[t]}),(t={...this.constructor.Default,...e,..."object"==typeof t&&t?t:{}}).container=!1===t.container?document.body:s(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),i(Ce,t,this.constructor.DefaultType),t.sanitize&&(t.template=we(t.template,t.allowList,t.sanitizeFn)),t}_getDelegateConfig(){const t={};for(const e in this._config)this.constructor.Default[e]!==this._config[e]&&(t[e]=this._config[e]);return t}_cleanTipClass(){const e=this.getTipElement();var t=new RegExp(`(^|\\s)${this._getBasicClassPrefix()}\\S+`,"g");const i=e.getAttribute("class").match(t);null!==i&&0<i.length&&i.map(t=>t.trim()).forEach(t=>e.classList.remove(t))}_getBasicClassPrefix(){return"bs-tooltip"}_handlePopperPlacementChange(t){t=t.state;t&&(this.tip=t.elements.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(t.placement)))}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null)}static jQueryInterface(e){return this.each(function(){const t=x.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(x);A=".bs.popover";const Pe={...x.Default,placement:"right",offset:[0,8],trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'},Me={...x.DefaultType,content:"(string|element|function)"},He={HIDE:"hide"+A,HIDDEN:"hidden"+A,SHOW:"show"+A,SHOWN:"shown"+A,INSERTED:"inserted"+A,CLICK:"click"+A,FOCUSIN:"focusin"+A,FOCUSOUT:"focusout"+A,MOUSEENTER:"mouseenter"+A,MOUSELEAVE:"mouseleave"+A};class qe extends x{static get Default(){return Pe}static get NAME(){return"popover"}static get Event(){return He}static get DefaultType(){return Me}isWithContent(){return this.getTitle()||this._getContent()}setContent(t){this._sanitizeAndSetContent(t,this.getTitle(),".popover-header"),this._sanitizeAndSetContent(t,this._getContent(),".popover-body")}_getContent(){return this._resolvePossibleFunction(this._config.content)}_getBasicClassPrefix(){return"bs-popover"}static jQueryInterface(e){return this.each(function(){const t=qe.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(qe);const Be="scrollspy";const ze=".bs.scrollspy";const Re={offset:10,method:"auto",target:""},Fe={offset:"number",method:"string",target:"(string|element)"};ze,ze;ze;const We="dropdown-item",P="active",$e=".nav-link",Ue=".list-group-item",Qe=$e+`, ${Ue}, .`+We,Ke="position";class Ve extends e{constructor(t,e){super(t),this._scrollElement="BODY"===this._element.tagName?window:this._element,this._config=this._getConfig(e),this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,_.on(this._scrollElement,"scroll.bs.scrollspy",()=>this._process()),this.refresh(),this._process()}static get Default(){return Re}static get NAME(){return Be}refresh(){var t=this._scrollElement===this._scrollElement.window?"offset":Ke;const s="auto"===this._config.method?t:this._config.method,n=s===Ke?this._getScrollTop():0,e=(this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),m.find(Qe,this._config.target));e.map(t=>{t=R(t);const e=t?m.findOne(t):null;if(e){var i=e.getBoundingClientRect();if(i.width||i.height)return[f[s](e).top+n,t]}return null}).filter(t=>t).sort((t,e)=>t[0]-e[0]).forEach(t=>{this._offsets.push(t[0]),this._targets.push(t[1])})}dispose(){_.off(this._scrollElement,ze),super.dispose()}_getConfig(t){return(t={...Re,...f.getDataAttributes(this._element),..."object"==typeof t&&t?t:{}}).target=s(t.target)||document.documentElement,i(Be,t,Fe),t}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),i=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),i<=e)return t=this._targets[this._targets.length-1],void(this._activeTarget!==t&&this._activate(t));if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(let t=this._offsets.length;t--;)this._activeTarget!==this._targets[t]&&e>=this._offsets[t]&&(void 0===this._offsets[t+1]||e<this._offsets[t+1])&&this._activate(this._targets[t])}_activate(e){this._activeTarget=e,this._clear();const t=Qe.split(",").map(t=>t+`[data-bs-target="${e}"],${t}[href="${e}"]`),i=m.findOne(t.join(","),this._config.target);i.classList.add(P),i.classList.contains(We)?m.findOne(".dropdown-toggle",i.closest(".dropdown")).classList.add(P):m.parents(i,".nav, .list-group").forEach(t=>{m.prev(t,$e+", "+Ue).forEach(t=>t.classList.add(P)),m.prev(t,".nav-item").forEach(t=>{m.children(t,$e).forEach(t=>t.classList.add(P))})}),_.trigger(this._scrollElement,"activate.bs.scrollspy",{relatedTarget:e})}_clear(){m.find(Qe,this._config.target).filter(t=>t.classList.contains(P)).forEach(t=>t.classList.remove(P))}static jQueryInterface(e){return this.each(function(){const t=Ve.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}_.on(window,"load.bs.scrollspy.data-api",()=>{m.find('[data-bs-spy="scroll"]').forEach(t=>new Ve(t))}),t(Ve);const Xe="active",Ye=".active",Ge=":scope > li > .active";class Ze extends e{static get NAME(){return"tab"}show(){if(!this._element.parentNode||this._element.parentNode.nodeType!==Node.ELEMENT_NODE||!this._element.classList.contains(Xe)){let t;var e=o(this._element),i=this._element.closest(".nav, .list-group"),s=(i&&(s="UL"===i.nodeName||"OL"===i.nodeName?Ge:Ye,t=(t=m.find(s,i))[t.length-1]),t?_.trigger(t,"hide.bs.tab",{relatedTarget:this._element}):null);_.trigger(this._element,"show.bs.tab",{relatedTarget:t}).defaultPrevented||null!==s&&s.defaultPrevented||(this._activate(this._element,i),s=()=>{_.trigger(t,"hidden.bs.tab",{relatedTarget:this._element}),_.trigger(this._element,"shown.bs.tab",{relatedTarget:t})},e?this._activate(e,e.parentNode,s):s())}}_activate(t,e,i){const s=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?m.children(e,Ye):m.find(Ge,e))[0];var e=i&&s&&s.classList.contains("fade"),n=()=>this._transitionComplete(t,s,i);s&&e?(s.classList.remove("show"),this._queueCallback(n,t,!0)):n()}_transitionComplete(t,e,i){if(e){e.classList.remove(Xe);const n=m.findOne(":scope > .dropdown-menu .active",e.parentNode);n&&n.classList.remove(Xe),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}t.classList.add(Xe),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),d(t),t.classList.contains("fade")&&t.classList.add("show");let s=t.parentNode;(s=s&&"LI"===s.nodeName?s.parentNode:s)&&s.classList.contains("dropdown-menu")&&((e=t.closest(".dropdown"))&&m.find(".dropdown-toggle",e).forEach(t=>t.classList.add(Xe)),t.setAttribute("aria-expanded",!0)),i&&i()}static jQueryInterface(e){return this.each(function(){const t=Ze.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}_.on(document,"click.bs.tab.data-api",'[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',function(t){if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),!r(this)){const e=Ze.getOrCreateInstance(this);e.show()}}),t(Ze);const Je="show",ti="showing",ei={animation:"boolean",autohide:"boolean",delay:"number"},ii={animation:!0,autohide:!0,delay:5e3};class si extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get DefaultType(){return ei}static get Default(){return ii}static get NAME(){return"toast"}show(){_.trigger(this._element,"show.bs.toast").defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),d(this._element),this._element.classList.add(Je),this._element.classList.add(ti),this._queueCallback(()=>{this._element.classList.remove(ti),_.trigger(this._element,"shown.bs.toast"),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this._element.classList.contains(Je)&&!_.trigger(this._element,"hide.bs.toast").defaultPrevented&&(this._element.classList.add(ti),this._queueCallback(()=>{this._element.classList.add("hide"),this._element.classList.remove(ti),this._element.classList.remove(Je),_.trigger(this._element,"hidden.bs.toast")},this._element,this._config.animation))}dispose(){this._clearTimeout(),this._element.classList.contains(Je)&&this._element.classList.remove(Je),super.dispose()}_getConfig(t){return t={...ii,...f.getDataAttributes(this._element),..."object"==typeof t&&t?t:{}},i("toast",t,this.constructor.DefaultType),t}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}e?this._clearTimeout():(t=t.relatedTarget,this._element===t||this._element.contains(t)||this._maybeScheduleHide())}_setListeners(){_.on(this._element,"mouseover.bs.toast",t=>this._onInteraction(t,!0)),_.on(this._element,"mouseout.bs.toast",t=>this._onInteraction(t,!1)),_.on(this._element,"focusin.bs.toast",t=>this._onInteraction(t,!0)),_.on(this._element,"focusout.bs.toast",t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=si.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}})}}return ht(si),t(si),{Alert:dt,Button:ft,Carousel:w,Collapse:T,Dropdown:D,Modal:O,Offcanvas:I,Popover:qe,ScrollSpy:Ve,Tab:Ze,Toast:si,Tooltip:x}},"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e(t.Popper),e=function(r){"use strict";r.fn.menu=function(e){var n=r.extend({},{element:{main:".menu-item",toggle:".menu-item-toggle",link:".menu-item-link",menu:".menu-submenu"},data:{activated:"menu-activated",numParent:"menu-num-parent",height:"menu-height",path:"menu-path"},activeClass:"active"},r.fn.menu.defaults),t=[{event:"init",action:function(){var t=r("body").hasClass("aside-minimized"),e=window.location.pathname,i=0;t&&r("body").removeClass("aside-minimized"),r(n.element.main).each(function(){var t=r(this).parents(n.element.menu).length;i<t&&(i=t),r(this).data(n.data.numParent,t)}),r(n.element.link).each(function(){r(this).data(n.data.path)==e&&(r(this).addClass(n.activeClass),r(this).parents(n.element.main).each(function(){var t=r(this).children(n.element.menu);t.length&&o(t)}))});for(var s=i;0<=s;s--)r(n.element.main).each(function(){var t,e=r(this).data(n.data.numParent),i=r(this).children(n.element.menu);e==s&&(r(this).data(n.data.activated,!0),0!=i.length&&(t=(e=i).outerHeight(),e.data(n.data.height,t),(r(this).children(n.element.toggle).hasClass(n.activeClass)?o:a)(i)))});t&&r("body").addClass("aside-minimized"),r(n.element.toggle).on("click",function(){var t=r(this).siblings(n.element.menu);(t.data(n.data.activated)?a:o)(t)})}},{event:"show",action:function(t){o(t)}},{event:"hide",action:function(t){a(t)}}];function o(t){var e;(t=t.first()).hasClass(n.element.menu.substr(1))&&(e=t.data(n.data.height),t.parent(n.element.main).data(n.data.numParent),t.css("height",e),t.parents(n.element.menu).each(function(){var t=r(this).data(n.data.height)+e;r(this).css("height",t),r(this).data(n.data.height,t)}),t.siblings(n.element.toggle).addClass(n.activeClass),t.data(n.data.activated,!0))}function a(t){var e;(t=t.first()).hasClass(n.element.menu.substr(1))&&(e=t.data(n.data.height),t.css("height",0),t.parents(n.element.menu).each(function(){var t=r(this).data(n.data.height)-e;r(this).data(n.data.height,t),r(this).css("height",t)}),t.siblings(n.element.toggle).removeClass(n.activeClass),t.data(n.data.activated,!1))}var i=r(this);return"string"==typeof e&&t.forEach(function(t){e==t.event&&t.action(i)}),this},r(function(){r().menu("init")})},"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery),t=function(a){"use strict";a.fn.portlet=function(e){var i=a.extend({},{element:{main:".portlet",body:".portlet-body"},data:{hidden:"portlet-hidden"},collapsedClass:"portlet-collapsed",destroyMethod:"fade",easing:"linear",transitionDuration:200},a.fn.portlet.defaults),t=[{event:"collapse",action:function(t){s(t)}},{event:"uncollapse",action:function(t){n(t)}},{event:"toggleCollapse",action:function(t){((t=t).data(i.data.hidden)?n:s)(t)}},{event:"destroy",action:function(t){var e;t.hasClass(i.element.main.substr(1))&&("fade"!==(e=i.destroyMethod)&&"slide"===e?t.slideUp(i.transitionDuration):t.fadeOut(i.transitionDuration))}}];function s(t){t.hasClass(i.element.main.substr(1))&&t.find(i.element.body).slideUp({duration:i.transitionDuration,easing:i.easing,complete:function(){t.data(i.data.hidden,!0),t.addClass(i.collapsedClass)}})}function n(t){t.hasClass(i.element.main.substr(1))&&t.find(i.element.body).slideDown({duration:i.transitionDuration,easing:i.easing,complete:function(){t.data(i.data.hidden,!1)}}).removeClass(i.collapsedClass)}var o=a(this);return"string"==typeof e&&t.forEach(function(t){e==t.event&&t.action(o)}),this}},"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery),e=function(i){"use strict";i.scrollToTop=function(){var t=i.extend({},{element:".scrolltop",activeClass:"active",scrollHeight:200,transitionDuration:500},i.scrollToTop.defaults);function e(){i(t.element).addClass(t.activeClass)}i(window).scrollTop()>=t.scrollHeight&&e(),i(window).scroll(function(){i(this).scrollTop()>=t.scrollHeight?e():i(t.element).removeClass(t.activeClass)}),i(t.element).on("click",function(){i("html").animate({scrollTop:0},t.transitionDuration)})},i(function(){i.scrollToTop()})},"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery),t=function(o){"use strict";o.aside=function(e,i){var s=o.extend({},{breakpoint:1025,toggleElement:'[data-toggle="aside"]',backdropElement:"#aside-backdrop",transitionDuration:200,transitionEasing:"linear",activeClass:"aside-show"},o.aside.defaults);function n(){o(window).width()<s.breakpoint&&(o("body").removeClass(s.activeClass),o(s.backdropElement).animate({opacity:0},{duration:s.transitionDuration,easing:s.transitionEasing,complete:function(){o(this).remove()}}).off())}function t(){var t;o(window).width()<s.breakpoint&&(o("body").addClass(s.activeClass),t='<div id="'+s.backdropElement.substr(1)+'"></div>',o(t).appendTo("body").animate({opacity:1},{duration:s.transitionDuration,easing:s.transitionEasing,complete:function(){o(this).on("click",function(){n()})}}))}"string"==typeof e&&[{event:"init",action:function(){o(s.toggleElement).on("click",function(){(o("body").hasClass(s.activeClass)?n:t)()})}},{event:"show",action:function(){t()}},{event:"hide",action:function(){n()}}].forEach(function(t){e==t.event&&t.action(i)})},o(function(){o.aside("init")})},"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery),e=function(o){"use strict";o.sidebar=function(e,i){var s=o.extend({},{breakpoint:1025,toggleElement:'[data-toggle="sidebar"]',backdropElement:"#sidebar-backdrop",transitionDuration:200,transitionEasing:"linear",activeClass:"sidebar-show"},o.sidebar.defaults);function n(){o(window).width()<s.breakpoint&&(o("body").removeClass(s.activeClass),o(s.backdropElement).animate({opacity:0},{duration:s.transitionDuration,easing:s.transitionEasing,complete:function(){o(this).remove()}}).off())}function t(){var t;o(window).width()<s.breakpoint&&(o("body").addClass(s.activeClass),t='<div id="'+s.backdropElement.substr(1)+'"></div>',o(t).appendTo("body").animate({opacity:1},{duration:s.transitionDuration,easing:s.transitionEasing,complete:function(){o(this).on("click",function(){n()})}}))}"string"==typeof e&&[{event:"init",action:function(){o(s.toggleElement).on("click",function(){(o("body").hasClass(s.activeClass)?n:t)()})}},{event:"show",action:function(){t()}},{event:"hide",action:function(){n()}}].forEach(function(t){e==t.event&&t.action(i)})},o(function(){o.sidebar("init")})},"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery),t=function(n){"use strict";n.headerNav=function(){var i=n.extend({},{headerNavElement:'[data-toggle*="header-nav"]',headerTabElement:'[data-toggle*="header-tab"]',headerLinkElement:"[data-href]",navLinkElement:".nav-link",activeClass:"active"},n.headerNav.defaults),s=!1;n(i.headerNavElement).find(i.headerTabElement).each(function(){var t=n(this),e=window.location.pathname;if(t.find(i.headerLinkElement).each(function(){if(n(this).data("href")==e)return n(i.headerTabElement).removeClass(i.activeClass),t.find(i.navLinkElement).addClass(i.activeClass),!(s=!0)}),s)return!1})},n(function(){n.headerNav()})},"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery),e=function(n){"use strict";n.preload=function(e){var t=n.extend({},{bodyHideClass:"preload-hide",bodyActiveClass:"preload-active"},n.preload.defaults),i=[{event:"show",action:function(){n("body").removeClass(t.bodyHideClass),n("body").addClass(t.bodyActiveClass)}},{event:"hide",action:function(){n("body").addClass(t.bodyHideClass),n("body").removeClass(t.bodyActiveClass)}}];var s=n(this);return"string"==typeof e&&i.forEach(function(t){e==t.event&&t.action(s)}),this},setTimeout(function(){n.preload("hide")},6e3),n(function(){n.preload("hide")})},"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery);