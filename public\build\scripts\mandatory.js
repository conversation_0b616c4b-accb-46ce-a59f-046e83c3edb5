var t,e;!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)}("undefined"!=typeof window?window:this,function(_,B){"use strict";function v(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item}function m(t){return null!=t&&t===t.window}var e=[],R=Object.getPrototypeOf,a=e.slice,I=e.flat?function(t){return e.flat.call(t)}:function(t){return e.concat.apply([],t)},H=e.push,Y=e.indexOf,F={},W=F.toString,q=F.hasOwnProperty,V=q.toString,$=V.call(Object),g={},w=_.document,U={type:!0,src:!0,nonce:!0,noModule:!0};function z(t,e,n){var r,i,o=(n=n||w).createElement("script");if(o.text=t,e)for(r in U)(i=e[r]||e.getAttribute&&e.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function d(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?F[W.call(t)]||"object":typeof t}var t="3.6.0",k=function(t,e){return new k.fn.init(t,e)};function G(t){var e=!!t&&"length"in t&&t.length,n=d(t);return!v(t)&&!m(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}k.fn=k.prototype={jquery:t,constructor:k,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){t=k.merge(this.constructor(),t);return t.prevObject=this,t},each:function(t){return k.each(this,t)},map:function(n){return this.pushStack(k.map(this,function(t,e){return n.call(t,e,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(k.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(k.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,t=+t+(t<0?e:0);return this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:H,sort:e.sort,splice:e.splice},k.extend=k.fn.extend=function(){var t,e,n,r,i,o=arguments[0]||{},s=1,a=arguments.length,l=!1;for("boolean"==typeof o&&(l=o,o=arguments[s]||{},s++),"object"==typeof o||v(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(t=arguments[s]))for(e in t)n=t[e],"__proto__"!==e&&o!==n&&(l&&n&&(k.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[e],i=r&&!Array.isArray(i)?[]:r||k.isPlainObject(i)?i:{},r=!1,o[e]=k.extend(l,i,n)):void 0!==n&&(o[e]=n));return o},k.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){return!(!t||"[object Object]"!==W.call(t))&&(!(t=R(t))||"function"==typeof(t=q.call(t,"constructor")&&t.constructor)&&V.call(t)===$)},isEmptyObject:function(t){for(var e in t)return!1;return!0},globalEval:function(t,e,n){z(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,r=0;if(G(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},makeArray:function(t,e){e=e||[];return null!=t&&(G(Object(t))?k.merge(e,"string"==typeof t?[t]:t):H.call(e,t)),e},inArray:function(t,e,n){return null==e?-1:Y.call(e,t,n)},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!=s&&r.push(t[i]);return r},map:function(t,e,n){var r,i,o=0,s=[];if(G(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&s.push(i);else for(o in t)null!=(i=e(t[o],o,n))&&s.push(i);return I(s)},guid:1,support:g}),"function"==typeof Symbol&&(k.fn[Symbol.iterator]=e[Symbol.iterator]),k.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){F["[object "+e+"]"]=e.toLowerCase()});function r(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&k(t).is(n))break;r.push(t)}return r}function X(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n}var t=function(B){function h(t,e){return t="0x"+t.slice(1)-65536,e||(t<0?String.fromCharCode(65536+t):String.fromCharCode(t>>10|55296,1023&t|56320))}function R(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}function I(){w()}var t,f,b,o,H,p,Y,F,_,l,u,w,k,n,S,d,r,i,m,C="sizzle"+ +new Date,c=B.document,P=0,W=0,q=O(),V=O(),$=O(),g=O(),U=function(t,e){return t===e&&(u=!0),0},z={}.hasOwnProperty,e=[],G=e.pop,X=e.push,T=e.push,Z=e.slice,v=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},K="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",s="[\\x20\\t\\r\\n\\f]",a="(?:\\\\[\\da-fA-F]{1,6}"+s+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",J="\\["+s+"*("+a+")(?:"+s+"*([*^$|!~]?=)"+s+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+a+"))|)"+s+"*\\]",Q=":("+a+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+J+")*)|.*)\\)|)",tt=new RegExp(s+"+","g"),y=new RegExp("^"+s+"+|((?:^|[^\\\\])(?:\\\\.)*)"+s+"+$","g"),et=new RegExp("^"+s+"*,"+s+"*"),nt=new RegExp("^"+s+"*([>+~]|"+s+")"+s+"*"),rt=new RegExp(s+"|>"),it=new RegExp(Q),ot=new RegExp("^"+a+"$"),x={ID:new RegExp("^#("+a+")"),CLASS:new RegExp("^\\.("+a+")"),TAG:new RegExp("^("+a+"|[*])"),ATTR:new RegExp("^"+J),PSEUDO:new RegExp("^"+Q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+s+"*(even|odd|(([+-]|)(\\d*)n|)"+s+"*(?:([+-]|)"+s+"*(\\d+)|))"+s+"*\\)|)","i"),bool:new RegExp("^(?:"+K+")$","i"),needsContext:new RegExp("^"+s+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+s+"*((?:-\\d)?\\d*)"+s+"*\\)|)(?=[^-]|$)","i")},st=/HTML$/i,at=/^(?:input|select|textarea|button)$/i,lt=/^h\d$/i,D=/^[^{]+\{\s*\[native \w/,ut=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ct=/[+~]/,M=new RegExp("\\\\[\\da-fA-F]{1,6}"+s+"?|\\\\([^\\r\\n\\f])","g"),ht=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ft=vt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{T.apply(e=Z.call(c.childNodes),c.childNodes),e[c.childNodes.length].nodeType}catch(t){T={apply:e.length?function(t,e){X.apply(t,Z.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function E(e,t,n,r){var i,o,s,a,l,u,c=t&&t.ownerDocument,h=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==h&&9!==h&&11!==h)return n;if(!r&&(w(t),t=t||k,S)){if(11!==h&&(a=ut.exec(e)))if(i=a[1]){if(9===h){if(!(u=t.getElementById(i)))return n;if(u.id===i)return n.push(u),n}else if(c&&(u=c.getElementById(i))&&m(t,u)&&u.id===i)return n.push(u),n}else{if(a[2])return T.apply(n,t.getElementsByTagName(e)),n;if((i=a[3])&&f.getElementsByClassName&&t.getElementsByClassName)return T.apply(n,t.getElementsByClassName(i)),n}if(f.qsa&&!g[e+" "]&&(!d||!d.test(e))&&(1!==h||"object"!==t.nodeName.toLowerCase())){if(u=e,c=t,1===h&&(rt.test(e)||nt.test(e))){for((c=ct.test(e)&&gt(t.parentNode)||t)===t&&f.scope||((s=t.getAttribute("id"))?s=s.replace(ht,R):t.setAttribute("id",s=C)),o=(l=p(e)).length;o--;)l[o]=(s?"#"+s:":scope")+" "+j(l[o]);u=l.join(",")}try{return T.apply(n,c.querySelectorAll(u)),n}catch(t){g(e,!0)}finally{s===C&&t.removeAttribute("id")}}}return F(e.replace(y,"$1"),t,n,r)}function O(){var n=[];function r(t,e){return n.push(t+" ")>b.cacheLength&&delete r[n.shift()],r[t+" "]=e}return r}function N(t){return t[C]=!0,t}function A(t){var e=k.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function pt(t,e){for(var n=t.split("|"),r=n.length;r--;)b.attrHandle[n[r]]=e}function dt(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function mt(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&ft(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function L(s){return N(function(o){return o=+o,N(function(t,e){for(var n,r=s([],t.length,o),i=r.length;i--;)t[n=r[i]]&&(t[n]=!(e[n]=t[n]))})})}function gt(t){return t&&void 0!==t.getElementsByTagName&&t}for(t in f=E.support={},H=E.isXML=function(t){var e=t&&t.namespaceURI,t=t&&(t.ownerDocument||t).documentElement;return!st.test(e||t&&t.nodeName||"HTML")},w=E.setDocument=function(t){var t=t?t.ownerDocument||t:c;return t!=k&&9===t.nodeType&&t.documentElement&&(n=(k=t).documentElement,S=!H(k),c!=k&&(t=k.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",I,!1):t.attachEvent&&t.attachEvent("onunload",I)),f.scope=A(function(t){return n.appendChild(t).appendChild(k.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),f.attributes=A(function(t){return t.className="i",!t.getAttribute("className")}),f.getElementsByTagName=A(function(t){return t.appendChild(k.createComment("")),!t.getElementsByTagName("*").length}),f.getElementsByClassName=D.test(k.getElementsByClassName),f.getById=A(function(t){return n.appendChild(t).id=C,!k.getElementsByName||!k.getElementsByName(C).length}),f.getById?(b.filter.ID=function(t){var e=t.replace(M,h);return function(t){return t.getAttribute("id")===e}},b.find.ID=function(t,e){if(void 0!==e.getElementById&&S)return(e=e.getElementById(t))?[e]:[]}):(b.filter.ID=function(t){var e=t.replace(M,h);return function(t){t=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return t&&t.value===e}},b.find.ID=function(t,e){if(void 0!==e.getElementById&&S){var n,r,i,o=e.getElementById(t);if(o){if((n=o.getAttributeNode("id"))&&n.value===t)return[o];for(i=e.getElementsByName(t),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===t)return[o]}return[]}}),b.find.TAG=f.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):f.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"!==t)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},b.find.CLASS=f.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&S)return e.getElementsByClassName(t)},r=[],d=[],(f.qsa=D.test(k.querySelectorAll))&&(A(function(t){var e;n.appendChild(t).innerHTML="<a id='"+C+"'></a><select id='"+C+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&d.push("[*^$]="+s+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||d.push("\\["+s+"*(?:value|"+K+")"),t.querySelectorAll("[id~="+C+"-]").length||d.push("~="),(e=k.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||d.push("\\["+s+"*name"+s+"*="+s+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||d.push(":checked"),t.querySelectorAll("a#"+C+"+*").length||d.push(".#.+[+~]"),t.querySelectorAll("\\\f"),d.push("[\\r\\n\\f]")}),A(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=k.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&d.push("name"+s+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&d.push(":enabled",":disabled"),n.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),d.push(",.*:")})),(f.matchesSelector=D.test(i=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.oMatchesSelector||n.msMatchesSelector))&&A(function(t){f.disconnectedMatch=i.call(t,"*"),i.call(t,"[s!='']:x"),r.push("!=",Q)}),d=d.length&&new RegExp(d.join("|")),r=r.length&&new RegExp(r.join("|")),t=D.test(n.compareDocumentPosition),m=t||D.test(n.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,e=e&&e.parentNode;return t===e||!(!e||1!==e.nodeType||!(n.contains?n.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},U=t?function(t,e){if(t===e)return u=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!f.sortDetached&&e.compareDocumentPosition(t)===n?t==k||t.ownerDocument==c&&m(c,t)?-1:e==k||e.ownerDocument==c&&m(c,e)?1:l?v(l,t)-v(l,e):0:4&n?-1:1)}:function(t,e){if(t===e)return u=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,s=[t],a=[e];if(!i||!o)return t==k?-1:e==k?1:i?-1:o?1:l?v(l,t)-v(l,e):0;if(i===o)return dt(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?dt(s[r],a[r]):s[r]==c?-1:a[r]==c?1:0}),k},E.matches=function(t,e){return E(t,null,null,e)},E.matchesSelector=function(t,e){if(w(t),f.matchesSelector&&S&&!g[e+" "]&&(!r||!r.test(e))&&(!d||!d.test(e)))try{var n=i.call(t,e);if(n||f.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){g(e,!0)}return 0<E(e,k,null,[t]).length},E.contains=function(t,e){return(t.ownerDocument||t)!=k&&w(t),m(t,e)},E.attr=function(t,e){(t.ownerDocument||t)!=k&&w(t);var n=b.attrHandle[e.toLowerCase()],n=n&&z.call(b.attrHandle,e.toLowerCase())?n(t,e,!S):void 0;return void 0!==n?n:f.attributes||!S?t.getAttribute(e):(n=t.getAttributeNode(e))&&n.specified?n.value:null},E.escape=function(t){return(t+"").replace(ht,R)},E.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},E.uniqueSort=function(t){var e,n=[],r=0,i=0;if(u=!f.detectDuplicates,l=!f.sortStable&&t.slice(0),t.sort(U),u){for(;e=t[i++];)e===t[i]&&(r=n.push(i));for(;r--;)t.splice(n[r],1)}return l=null,t},o=E.getText=function(t){var e,n="",r=0,i=t.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===i||4===i)return t.nodeValue}else for(;e=t[r++];)n+=o(e);return n},(b=E.selectors={cacheLength:50,createPseudo:N,match:x,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(M,h),t[3]=(t[3]||t[4]||t[5]||"").replace(M,h),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||E.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&E.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return x.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&it.test(n)&&(e=p(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(M,h).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=q[t+" "];return e||(e=new RegExp("(^|"+s+")"+t+"("+s+"|$)"))&&q(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(t){t=E.attr(t,e);return null==t?"!="===n:!n||(t+="","="===n?t===r:"!="===n?t!==r:"^="===n?r&&0===t.indexOf(r):"*="===n?r&&-1<t.indexOf(r):"$="===n?r&&t.slice(-r.length)===r:"~="===n?-1<(" "+t.replace(tt," ")+" ").indexOf(r):"|="===n&&(t===r||t.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,t,e,m,g){var y="nth"!==d.slice(0,3),v="last"!==d.slice(-4),x="of-type"===t;return 1===m&&0===g?function(t){return!!t.parentNode}:function(t,e,n){var r,i,o,s,a,l,u=y!=v?"nextSibling":"previousSibling",c=t.parentNode,h=x&&t.nodeName.toLowerCase(),f=!n&&!x,p=!1;if(c){if(y){for(;u;){for(s=t;s=s[u];)if(x?s.nodeName.toLowerCase()===h:1===s.nodeType)return!1;l=u="only"===d&&!l&&"nextSibling"}return!0}if(l=[v?c.firstChild:c.lastChild],v&&f){for(p=(a=(r=(i=(o=(s=c)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===P&&r[1])&&r[2],s=a&&c.childNodes[a];s=++a&&s&&s[u]||(p=a=0)||l.pop();)if(1===s.nodeType&&++p&&s===t){i[d]=[P,a,p];break}}else if(!1===(p=f?a=(r=(i=(o=(s=t)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===P&&r[1]:p))for(;(s=++a&&s&&s[u]||(p=a=0)||l.pop())&&((x?s.nodeName.toLowerCase()!==h:1!==s.nodeType)||!++p||(f&&((i=(o=s[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]=[P,p]),s!==t)););return(p-=g)===m||p%m==0&&0<=p/m}}},PSEUDO:function(t,o){var e,s=b.pseudos[t]||b.setFilters[t.toLowerCase()]||E.error("unsupported pseudo: "+t);return s[C]?s(o):1<s.length?(e=[t,t,"",o],b.setFilters.hasOwnProperty(t.toLowerCase())?N(function(t,e){for(var n,r=s(t,o),i=r.length;i--;)t[n=v(t,r[i])]=!(e[n]=r[i])}):function(t){return s(t,0,e)}):s}},pseudos:{not:N(function(t){var r=[],i=[],a=Y(t.replace(y,"$1"));return a[C]?N(function(t,e,n,r){for(var i,o=a(t,null,r,[]),s=t.length;s--;)(i=o[s])&&(t[s]=!(e[s]=i))}):function(t,e,n){return r[0]=t,a(r,null,n,i),r[0]=null,!i.pop()}}),has:N(function(e){return function(t){return 0<E(e,t).length}}),contains:N(function(e){return e=e.replace(M,h),function(t){return-1<(t.textContent||o(t)).indexOf(e)}}),lang:N(function(n){return ot.test(n||"")||E.error("unsupported lang: "+n),n=n.replace(M,h).toLowerCase(),function(t){var e;do{if(e=S?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===n||0===e.indexOf(n+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var e=B.location&&B.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===n},focus:function(t){return t===k.activeElement&&(!k.hasFocus||k.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:mt(!1),disabled:mt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!b.pseudos.empty(t)},header:function(t){return lt.test(t.nodeName)},input:function(t){return at.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:L(function(){return[0]}),last:L(function(t,e){return[e-1]}),eq:L(function(t,e,n){return[n<0?n+e:n]}),even:L(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:L(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:L(function(t,e,n){for(var r=n<0?n+e:e<n?e:n;0<=--r;)t.push(r);return t}),gt:L(function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[t]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(t);for(t in{submit:!0,reset:!0})b.pseudos[t]=function(n){return function(t){var e=t.nodeName.toLowerCase();return("input"===e||"button"===e)&&t.type===n}}(t);function yt(){}function j(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function vt(s,t,e){var a=t.dir,l=t.next,u=l||a,c=e&&"parentNode"===u,h=W++;return t.first?function(t,e,n){for(;t=t[a];)if(1===t.nodeType||c)return s(t,e,n);return!1}:function(t,e,n){var r,i,o=[P,h];if(n){for(;t=t[a];)if((1===t.nodeType||c)&&s(t,e,n))return!0}else for(;t=t[a];)if(1===t.nodeType||c)if(i=(i=t[C]||(t[C]={}))[t.uniqueID]||(i[t.uniqueID]={}),l&&l===t.nodeName.toLowerCase())t=t[a]||t;else{if((r=i[u])&&r[0]===P&&r[1]===h)return o[2]=r[2];if((i[u]=o)[2]=s(t,e,n))return!0}return!1}}function xt(i){return 1<i.length?function(t,e,n){for(var r=i.length;r--;)if(!i[r](t,e,n))return!1;return!0}:i[0]}function bt(t,e,n,r,i){for(var o,s=[],a=0,l=t.length,u=null!=e;a<l;a++)!(o=t[a])||n&&!n(o,r,i)||(s.push(o),u&&e.push(a));return s}function _t(p,d,m,g,y,t){return g&&!g[C]&&(g=_t(g)),y&&!y[C]&&(y=_t(y,t)),N(function(t,e,n,r){var i,o,s,a=[],l=[],u=e.length,c=t||function(t,e,n){for(var r=0,i=e.length;r<i;r++)E(t,e[r],n);return n}(d||"*",n.nodeType?[n]:n,[]),h=!p||!t&&d?c:bt(c,a,p,n,r),f=m?y||(t?p:u||g)?[]:e:h;if(m&&m(h,f,n,r),g)for(i=bt(f,l),g(i,[],n,r),o=i.length;o--;)(s=i[o])&&(f[l[o]]=!(h[l[o]]=s));if(t){if(y||p){if(y){for(i=[],o=f.length;o--;)(s=f[o])&&i.push(h[o]=s);y(null,f=[],i,r)}for(o=f.length;o--;)(s=f[o])&&-1<(i=y?v(t,s):a[o])&&(t[i]=!(e[i]=s))}}else f=bt(f===e?f.splice(u,f.length):f),y?y(null,e,f,r):T.apply(e,f)})}function wt(g,y){function t(t,e,n,r,i){var o,s,a,l=0,u="0",c=t&&[],h=[],f=_,p=t||x&&b.find.TAG("*",i),d=P+=null==f?1:Math.random()||.1,m=p.length;for(i&&(_=e==k||e||i);u!==m&&null!=(o=p[u]);u++){if(x&&o){for(s=0,e||o.ownerDocument==k||(w(o),n=!S);a=g[s++];)if(a(o,e||k,n)){r.push(o);break}i&&(P=d)}v&&((o=!a&&o)&&l--,t&&c.push(o))}if(l+=u,v&&u!==l){for(s=0;a=y[s++];)a(c,h,e,n);if(t){if(0<l)for(;u--;)c[u]||h[u]||(h[u]=G.call(r));h=bt(h)}T.apply(r,h),i&&!t&&0<h.length&&1<l+y.length&&E.uniqueSort(r)}return i&&(P=d,_=f),c}var v=0<y.length,x=0<g.length;return v?N(t):t}return yt.prototype=b.filters=b.pseudos,b.setFilters=new yt,p=E.tokenize=function(t,e){var n,r,i,o,s,a,l,u=V[t+" "];if(u)return e?0:u.slice(0);for(s=t,a=[],l=b.preFilter;s;){for(o in n&&!(r=et.exec(s))||(r&&(s=s.slice(r[0].length)||s),a.push(i=[])),n=!1,(r=nt.exec(s))&&(n=r.shift(),i.push({value:n,type:r[0].replace(y," ")}),s=s.slice(n.length)),b.filter)!(r=x[o].exec(s))||l[o]&&!(r=l[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),s=s.slice(n.length));if(!n)break}return e?s.length:s?E.error(t):V(t,a).slice(0)},Y=E.compile=function(t,e){var n,r=[],i=[],o=$[t+" "];if(!o){for(n=(e=e||p(t)).length;n--;)((o=function t(e){for(var r,n,i,o=e.length,s=b.relative[e[0].type],a=s||b.relative[" "],l=s?1:0,u=vt(function(t){return t===r},a,!0),c=vt(function(t){return-1<v(r,t)},a,!0),h=[function(t,e,n){return t=!s&&(n||e!==_)||((r=e).nodeType?u:c)(t,e,n),r=null,t}];l<o;l++)if(n=b.relative[e[l].type])h=[vt(xt(h),n)];else{if((n=b.filter[e[l].type].apply(null,e[l].matches))[C]){for(i=++l;i<o&&!b.relative[e[i].type];i++);return _t(1<l&&xt(h),1<l&&j(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(y,"$1"),n,l<i&&t(e.slice(l,i)),i<o&&t(e=e.slice(i)),i<o&&j(e))}h.push(n)}return xt(h)}(e[n]))[C]?r:i).push(o);(o=$(t,wt(i,r))).selector=t}return o},F=E.select=function(t,e,n,r){var i,o,s,a,l,u="function"==typeof t&&t,c=!r&&p(t=u.selector||t);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(s=o[0]).type&&9===e.nodeType&&S&&b.relative[o[1].type]){if(!(e=(b.find.ID(s.matches[0].replace(M,h),e)||[])[0]))return n;u&&(e=e.parentNode),t=t.slice(o.shift().value.length)}for(i=x.needsContext.test(t)?0:o.length;i--&&(s=o[i],!b.relative[a=s.type]);)if((l=b.find[a])&&(r=l(s.matches[0].replace(M,h),ct.test(o[0].type)&&gt(e.parentNode)||e))){if(o.splice(i,1),t=r.length&&j(o))break;return T.apply(n,r),n}}return(u||Y(t,c))(r,e,!S,n,!e||ct.test(t)&&gt(e.parentNode)||e),n},f.sortStable=C.split("").sort(U).join("")===C,f.detectDuplicates=!!u,w(),f.sortDetached=A(function(t){return 1&t.compareDocumentPosition(k.createElement("fieldset"))}),A(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||pt("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),f.attributes&&A(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||pt("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),A(function(t){return null==t.getAttribute("disabled")})||pt(K,function(t,e,n){if(!n)return!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),E}(_),Z=(k.find=t,k.expr=t.selectors,k.expr[":"]=k.expr.pseudos,k.uniqueSort=k.unique=t.uniqueSort,k.text=t.getText,k.isXMLDoc=t.isXML,k.contains=t.contains,k.escapeSelector=t.escape,k.expr.match.needsContext);function l(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var K=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function J(t,n,r){return v(n)?k.grep(t,function(t,e){return!!n.call(t,e,t)!==r}):n.nodeType?k.grep(t,function(t){return t===n!==r}):"string"!=typeof n?k.grep(t,function(t){return-1<Y.call(n,t)!==r}):k.filter(n,t,r)}k.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?k.find.matchesSelector(r,t)?[r]:[]:k.find.matches(t,k.grep(e,function(t){return 1===t.nodeType}))},k.fn.extend({find:function(t){var e,n,r=this.length,i=this;if("string"!=typeof t)return this.pushStack(k(t).filter(function(){for(e=0;e<r;e++)if(k.contains(i[e],this))return!0}));for(n=this.pushStack([]),e=0;e<r;e++)k.find(t,i[e],n);return 1<r?k.uniqueSort(n):n},filter:function(t){return this.pushStack(J(this,t||[],!1))},not:function(t){return this.pushStack(J(this,t||[],!0))},is:function(t){return!!J(this,"string"==typeof t&&Z.test(t)?k(t):t||[],!1).length}});var Q,tt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,et=((k.fn.init=function(t,e,n){if(!t)return this;if(n=n||Q,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==n.ready?n.ready(t):t(k):k.makeArray(t,this);if(!(r="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:tt.exec(t))||!r[1]&&e)return(!e||e.jquery?e||n:this.constructor(e)).find(t);if(r[1]){if(e=e instanceof k?e[0]:e,k.merge(this,k.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:w,!0)),K.test(r[1])&&k.isPlainObject(e))for(var r in e)v(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}return(n=w.getElementById(r[2]))&&(this[0]=n,this.length=1),this}).prototype=k.fn,Q=k(w),/^(?:parents|prev(?:Until|All))/),nt={children:!0,contents:!0,next:!0,prev:!0};function rt(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}k.fn.extend({has:function(t){var e=k(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(k.contains(this,e[t]))return!0})},closest:function(t,e){var n,r=0,i=this.length,o=[],s="string"!=typeof t&&k(t);if(!Z.test(t))for(;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&k.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(1<o.length?k.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?Y.call(k(t),this[0]):Y.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(k.uniqueSort(k.merge(this.get(),k(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),k.each({parent:function(t){t=t.parentNode;return t&&11!==t.nodeType?t:null},parents:function(t){return r(t,"parentNode")},parentsUntil:function(t,e,n){return r(t,"parentNode",n)},next:function(t){return rt(t,"nextSibling")},prev:function(t){return rt(t,"previousSibling")},nextAll:function(t){return r(t,"nextSibling")},prevAll:function(t){return r(t,"previousSibling")},nextUntil:function(t,e,n){return r(t,"nextSibling",n)},prevUntil:function(t,e,n){return r(t,"previousSibling",n)},siblings:function(t){return X((t.parentNode||{}).firstChild,t)},children:function(t){return X(t.firstChild)},contents:function(t){return null!=t.contentDocument&&R(t.contentDocument)?t.contentDocument:(l(t,"template")&&(t=t.content||t),k.merge([],t.childNodes))}},function(r,i){k.fn[r]=function(t,e){var n=k.map(this,i,t);return(e="Until"!==r.slice(-5)?t:e)&&"string"==typeof e&&(n=k.filter(e,n)),1<this.length&&(nt[r]||k.uniqueSort(n),et.test(r)&&n.reverse()),this.pushStack(n)}});var S=/[^\x20\t\r\n\f]+/g;function c(t){return t}function it(t){throw t}function ot(t,e,n,r){var i;try{t&&v(i=t.promise)?i.call(t).done(e).fail(n):t&&v(i=t.then)?i.call(t,e,n):e.apply(void 0,[t].slice(r))}catch(t){n.apply(void 0,[t])}}k.Callbacks=function(r){var t,n;r="string"==typeof r?(t=r,n={},k.each(t.match(S)||[],function(t,e){n[e]=!0}),n):k.extend({},r);function i(){for(a=a||r.once,s=o=!0;u.length;c=-1)for(e=u.shift();++c<l.length;)!1===l[c].apply(e[0],e[1])&&r.stopOnFalse&&(c=l.length,e=!1);r.memory||(e=!1),o=!1,a&&(l=e?[]:"")}var o,e,s,a,l=[],u=[],c=-1,h={add:function(){return l&&(e&&!o&&(c=l.length-1,u.push(e)),function n(t){k.each(t,function(t,e){v(e)?r.unique&&h.has(e)||l.push(e):e&&e.length&&"string"!==d(e)&&n(e)})}(arguments),e&&!o&&i()),this},remove:function(){return k.each(arguments,function(t,e){for(var n;-1<(n=k.inArray(e,l,n));)l.splice(n,1),n<=c&&c--}),this},has:function(t){return t?-1<k.inArray(t,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return a=u=[],l=e="",this},disabled:function(){return!l},lock:function(){return a=u=[],e||o||(l=e=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],u.push(e),o||i()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!s}};return h},k.extend({Deferred:function(t){var o=[["notify","progress",k.Callbacks("memory"),k.Callbacks("memory"),2],["resolve","done",k.Callbacks("once memory"),k.Callbacks("once memory"),0,"resolved"],["reject","fail",k.Callbacks("once memory"),k.Callbacks("once memory"),1,"rejected"]],i="pending",s={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return s.then(null,t)},pipe:function(){var i=arguments;return k.Deferred(function(r){k.each(o,function(t,e){var n=v(i[e[4]])&&i[e[4]];a[e[1]](function(){var t=n&&n.apply(this,arguments);t&&v(t.promise)?t.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[e[0]+"With"](this,n?[t]:arguments)})}),i=null}).promise()},then:function(e,n,r){var l=0;function u(i,o,s,a){return function(){function t(){var t,e;if(!(i<l)){if((t=s.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");e=t&&("object"==typeof t||"function"==typeof t)&&t.then,v(e)?a?e.call(t,u(l,o,c,a),u(l,o,it,a)):(l++,e.call(t,u(l,o,c,a),u(l,o,it,a),u(l,o,c,o.notifyWith))):(s!==c&&(n=void 0,r=[t]),(a||o.resolveWith)(n,r))}}var n=this,r=arguments,e=a?t:function(){try{t()}catch(t){k.Deferred.exceptionHook&&k.Deferred.exceptionHook(t,e.stackTrace),l<=i+1&&(s!==it&&(n=void 0,r=[t]),o.rejectWith(n,r))}};i?e():(k.Deferred.getStackHook&&(e.stackTrace=k.Deferred.getStackHook()),_.setTimeout(e))}}return k.Deferred(function(t){o[0][3].add(u(0,t,v(r)?r:c,t.notifyWith)),o[1][3].add(u(0,t,v(e)?e:c)),o[2][3].add(u(0,t,v(n)?n:it))}).promise()},promise:function(t){return null!=t?k.extend(t,s):s}},a={};return k.each(o,function(t,e){var n=e[2],r=e[5];s[e[1]]=n.add,r&&n.add(function(){i=r},o[3-t][2].disable,o[3-t][3].disable,o[0][2].lock,o[0][3].lock),n.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=n.fireWith}),s.promise(a),t&&t.call(a,a),a},when:function(t){function e(e){return function(t){i[e]=this,o[e]=1<arguments.length?a.call(arguments):t,--n||s.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=a.call(arguments),s=k.Deferred();if(n<=1&&(ot(t,s.done(e(r)).resolve,s.reject,!n),"pending"===s.state()||v(o[r]&&o[r].then)))return s.then();for(;r--;)ot(o[r],e(r),s.reject);return s.promise()}});var st=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,at=(k.Deferred.exceptionHook=function(t,e){_.console&&_.console.warn&&t&&st.test(t.name)&&_.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},k.readyException=function(t){_.setTimeout(function(){throw t})},k.Deferred());function lt(){w.removeEventListener("DOMContentLoaded",lt),_.removeEventListener("load",lt),k.ready()}k.fn.ready=function(t){return at.then(t).catch(function(t){k.readyException(t)}),this},k.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--k.readyWait:k.isReady)||(k.isReady=!0)!==t&&0<--k.readyWait||at.resolveWith(w,[k])}}),k.ready.then=at.then,"complete"===w.readyState||"loading"!==w.readyState&&!w.documentElement.doScroll?_.setTimeout(k.ready):(w.addEventListener("DOMContentLoaded",lt),_.addEventListener("load",lt));function h(t,e,n,r,i,o,s){var a=0,l=t.length,u=null==n;if("object"===d(n))for(a in i=!0,n)h(t,e,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,v(r)||(s=!0),e=u?s?(e.call(t,r),null):(u=e,function(t,e,n){return u.call(k(t),n)}):e))for(;a<l;a++)e(t[a],n,s?r:r.call(t[a],a,e(t[a],n)));return i?t:u?e.call(t):l?e(t[0],n):o}var ut=/^-ms-/,ct=/-([a-z])/g;function ht(t,e){return e.toUpperCase()}function x(t){return t.replace(ut,"ms-").replace(ct,ht)}function y(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function ft(){this.expando=k.expando+ft.uid++}ft.uid=1,ft.prototype={cache:function(t){var e=t[this.expando];return e||(e={},y(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[x(e)]=n;else for(r in e)i[x(r)]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][x(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r=t[this.expando];if(void 0!==r){if(void 0!==e){n=(e=Array.isArray(e)?e.map(x):(e=x(e))in r?[e]:e.match(S)||[]).length;for(;n--;)delete r[e[n]]}void 0!==e&&!k.isEmptyObject(r)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){t=t[this.expando];return void 0!==t&&!k.isEmptyObject(t)}};var b=new ft,u=new ft,pt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,dt=/[A-Z]/g;function mt(t,e,n){var r,i;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(dt,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:pt.test(i)?JSON.parse(i):i)}catch(t){}u.set(t,e,n)}else n=void 0;return n}k.extend({hasData:function(t){return u.hasData(t)||b.hasData(t)},data:function(t,e,n){return u.access(t,e,n)},removeData:function(t,e){u.remove(t,e)},_data:function(t,e,n){return b.access(t,e,n)},_removeData:function(t,e){b.remove(t,e)}}),k.fn.extend({data:function(n,t){var e,r,i,o=this[0],s=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){u.set(this,n)}):h(this,function(t){var e;if(o&&void 0===t)return void 0!==(e=u.get(o,n))||void 0!==(e=mt(o,n))?e:void 0;this.each(function(){u.set(this,n,t)})},null,t,1<arguments.length,null,!0);if(this.length&&(i=u.get(o),1===o.nodeType&&!b.get(o,"hasDataAttrs"))){for(e=s.length;e--;)s[e]&&0===(r=s[e].name).indexOf("data-")&&(r=x(r.slice(5)),mt(o,r,i[r]));b.set(o,"hasDataAttrs",!0)}return i},removeData:function(t){return this.each(function(){u.remove(this,t)})}}),k.extend({queue:function(t,e,n){var r;if(t)return r=b.get(t,e=(e||"fx")+"queue"),n&&(!r||Array.isArray(n)?r=b.access(t,e,k.makeArray(n)):r.push(n)),r||[]},dequeue:function(t,e){e=e||"fx";var n=k.queue(t,e),r=n.length,i=n.shift(),o=k._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,function(){k.dequeue(t,e)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return b.get(t,n)||b.access(t,n,{empty:k.Callbacks("once memory").add(function(){b.remove(t,[e+"queue",n])})})}}),k.fn.extend({queue:function(e,n){var t=2;return"string"!=typeof e&&(n=e,e="fx",t--),arguments.length<t?k.queue(this[0],e):void 0===n?this:this.each(function(){var t=k.queue(this,e,n);k._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&k.dequeue(this,e)})},dequeue:function(t){return this.each(function(){k.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=k.Deferred(),s=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(r=b.get(s[a],t+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(e)}});function gt(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&P(t)&&"none"===k.css(t,"display")}var t=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,yt=new RegExp("^(?:([+-])=|)("+t+")([a-z%]*)$","i"),f=["Top","Right","Bottom","Left"],C=w.documentElement,P=function(t){return k.contains(t.ownerDocument,t)},vt={composed:!0};C.getRootNode&&(P=function(t){return k.contains(t.ownerDocument,t)||t.getRootNode(vt)===t.ownerDocument});function xt(t,e,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return k.css(t,e,"")},l=a(),u=n&&n[3]||(k.cssNumber[e]?"":"px"),c=t.nodeType&&(k.cssNumber[e]||"px"!==u&&+l)&&yt.exec(k.css(t,e));if(c&&c[3]!==u){for(u=u||c[3],c=+(l/=2)||1;s--;)k.style(t,e,c+u),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),c/=o;k.style(t,e,(c*=2)+u),n=n||[]}return n&&(c=+c||+l||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=c,r.end=i)),i}var bt={};function T(t,e){for(var n,r,i,o,s,a=[],l=0,u=t.length;l<u;l++)(r=t[l]).style&&(n=r.style.display,e?("none"===n&&(a[l]=b.get(r,"display")||null,a[l]||(r.style.display="")),""===r.style.display&&gt(r)&&(a[l]=(s=o=void 0,o=(i=r).ownerDocument,i=i.nodeName,(s=bt[i])||(o=o.body.appendChild(o.createElement(i)),s=k.css(o,"display"),o.parentNode.removeChild(o),bt[i]=s="none"===s?"block":s)))):"none"!==n&&(a[l]="none",b.set(r,"display",n)));for(l=0;l<u;l++)null!=a[l]&&(t[l].style.display=a[l]);return t}k.fn.extend({show:function(){return T(this,!0)},hide:function(){return T(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){gt(this)?k(this).show():k(this).hide()})}});var _t=/^(?:checkbox|radio)$/i,wt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,kt=/^$|^module$|\/(?:java|ecma)script/i,D=(N=w.createDocumentFragment().appendChild(w.createElement("div")),(s=w.createElement("input")).setAttribute("type","radio"),s.setAttribute("checked","checked"),s.setAttribute("name","t"),N.appendChild(s),g.checkClone=N.cloneNode(!0).cloneNode(!0).lastChild.checked,N.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!N.cloneNode(!0).lastChild.defaultValue,N.innerHTML="<option></option>",g.option=!!N.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function M(t,e){var n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&l(t,e)?k.merge([t],n):n}function St(t,e){for(var n=0,r=t.length;n<r;n++)b.set(t[n],"globalEval",!e||b.get(e[n],"globalEval"))}D.tbody=D.tfoot=D.colgroup=D.caption=D.thead,D.th=D.td,g.option||(D.optgroup=D.option=[1,"<select multiple='multiple'>","</select>"]);var Ct=/<|&#?\w+;/;function Pt(t,e,n,r,i){for(var o,s,a,l,u,c=e.createDocumentFragment(),h=[],f=0,p=t.length;f<p;f++)if((o=t[f])||0===o)if("object"===d(o))k.merge(h,o.nodeType?[o]:o);else if(Ct.test(o)){for(s=s||c.appendChild(e.createElement("div")),a=(wt.exec(o)||["",""])[1].toLowerCase(),a=D[a]||D._default,s.innerHTML=a[1]+k.htmlPrefilter(o)+a[2],u=a[0];u--;)s=s.lastChild;k.merge(h,s.childNodes),(s=c.firstChild).textContent=""}else h.push(e.createTextNode(o));for(c.textContent="",f=0;o=h[f++];)if(r&&-1<k.inArray(o,r))i&&i.push(o);else if(l=P(o),s=M(c.appendChild(o),"script"),l&&St(s),n)for(u=0;o=s[u++];)kt.test(o.type||"")&&n.push(o);return c}var Tt=/^([^.]*)(?:\.(.+)|)/;function n(){return!0}function p(){return!1}function Dt(t,e){return t===function(){try{return w.activeElement}catch(t){}}()==("focus"===e)}function Mt(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)Mt(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=p;else if(!i)return t;return 1===o&&(s=i,(i=function(t){return k().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=k.guid++)),t.each(function(){k.event.add(this,e,i,r,n)})}function Et(t,i,o){o?(b.set(t,i,!1),k.event.add(t,i,{namespace:!1,handler:function(t){var e,n,r=b.get(this,i);if(1&t.isTrigger&&this[i]){if(r.length)(k.event.special[i]||{}).delegateType&&t.stopPropagation();else if(r=a.call(arguments),b.set(this,i,r),e=o(this,i),this[i](),r!==(n=b.get(this,i))||e?b.set(this,i,!1):n={},r!==n)return t.stopImmediatePropagation(),t.preventDefault(),n&&n.value}else r.length&&(b.set(this,i,{value:k.event.trigger(k.extend(r[0],k.Event.prototype),r.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===b.get(t,i)&&k.event.add(t,i,n)}k.event={global:{},add:function(e,t,n,r,i){var o,s,a,l,u,c,h,f,p,d=b.get(e);if(y(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&k.find.matchesSelector(C,i),n.guid||(n.guid=k.guid++),(a=d.events)||(a=d.events=Object.create(null)),(s=d.handle)||(s=d.handle=function(t){return void 0!==k&&k.event.triggered!==t.type?k.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(S)||[""]).length;l--;)h=p=(f=Tt.exec(t[l])||[])[1],f=(f[2]||"").split(".").sort(),h&&(u=k.event.special[h]||{},h=(i?u.delegateType:u.bindType)||h,u=k.event.special[h]||{},p=k.extend({type:h,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&k.expr.match.needsContext.test(i),namespace:f.join(".")},o),(c=a[h])||((c=a[h]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(e,r,f,s)||e.addEventListener&&e.addEventListener(h,s)),u.add&&(u.add.call(e,p),p.handler.guid||(p.handler.guid=n.guid)),i?c.splice(c.delegateCount++,0,p):c.push(p),k.event.global[h]=!0)},remove:function(t,e,n,r,i){var o,s,a,l,u,c,h,f,p,d,m,g=b.hasData(t)&&b.get(t);if(g&&(l=g.events)){for(u=(e=(e||"").match(S)||[""]).length;u--;)if(p=m=(a=Tt.exec(e[u])||[])[1],d=(a[2]||"").split(".").sort(),p){for(h=k.event.special[p]||{},f=l[p=(r?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)c=f[o],!i&&m!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,h.remove&&h.remove.call(t,c));s&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,d,g.handle)||k.removeEvent(t,p,g.handle),delete l[p])}else for(p in l)k.event.remove(t,p+e[u],n,r,!0);k.isEmptyObject(l)&&b.remove(t,"handle events")}},dispatch:function(t){var e,n,r,i,o,s=new Array(arguments.length),a=k.event.fix(t),t=(b.get(this,"events")||Object.create(null))[a.type]||[],l=k.event.special[a.type]||{};for(s[0]=a,e=1;e<arguments.length;e++)s[e]=arguments[e];if(a.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,a)){for(o=k.event.handlers.call(this,a,t),e=0;(r=o[e++])&&!a.isPropagationStopped();)for(a.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==i.namespace&&!a.rnamespace.test(i.namespace)||(a.handleObj=i,a.data=i.data,void 0!==(i=((k.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s))&&!1===(a.result=i)&&(a.preventDefault(),a.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,a),a.result}},handlers:function(t,e){var n,r,i,o,s,a=[],l=e.delegateCount,u=t.target;if(l&&u.nodeType&&!("click"===t.type&&1<=t.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==t.type||!0!==u.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[i=(r=e[n]).selector+" "]&&(s[i]=r.needsContext?-1<k(i,this).index(u):k.find(i,this,null,[u]).length),s[i]&&o.push(r);o.length&&a.push({elem:u,handlers:o})}return u=this,l<e.length&&a.push({elem:u,handlers:e.slice(l)}),a},addProp:function(e,t){Object.defineProperty(k.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(t){return t[k.expando]?t:new k.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){t=this||t;return _t.test(t.type)&&t.click&&l(t,"input")&&Et(t,"click",n),!1},trigger:function(t){t=this||t;return _t.test(t.type)&&t.click&&l(t,"input")&&Et(t,"click"),!0},_default:function(t){t=t.target;return _t.test(t.type)&&t.click&&l(t,"input")&&b.get(t,"click")||l(t,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},k.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},k.Event=function(t,e){if(!(this instanceof k.Event))return new k.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?n:p,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&k.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[k.expando]=!0},k.Event.prototype={constructor:k.Event,isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=n,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=n,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=n,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},k.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},k.event.addProp),k.each({focus:"focusin",blur:"focusout"},function(t,e){k.event.special[t]={setup:function(){return Et(this,t,Dt),!1},trigger:function(){return Et(this,t),!0},_default:function(){return!0},delegateType:e}}),k.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,i){k.event.special[t]={delegateType:i,bindType:i,handle:function(t){var e,n=t.relatedTarget,r=t.handleObj;return n&&(n===this||k.contains(this,n))||(t.type=r.origType,e=r.handler.apply(this,arguments),t.type=i),e}}}),k.fn.extend({on:function(t,e,n,r){return Mt(this,t,e,n,r)},one:function(t,e,n,r){return Mt(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,k(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=p),this.each(function(){k.event.remove(this,t,n,e)});for(i in t)this.off(i,e,t[i]);return this}});var Ot=/<script|<style|<link/i,Nt=/checked\s*(?:[^=]|=\s*.checked.)/i,At=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Lt(t,e){return l(t,"table")&&l(11!==e.nodeType?e:e.firstChild,"tr")&&k(t).children("tbody")[0]||t}function jt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Bt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Rt(t,e){var n,r,i,o;if(1===e.nodeType){if(b.hasData(t)&&(o=b.get(t).events))for(i in b.remove(e,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)k.event.add(e,i,o[i][n]);u.hasData(t)&&(t=u.access(t),t=k.extend({},t),u.set(e,t))}}function E(n,r,i,o){r=I(r);var t,e,s,a,l,u,c=0,h=n.length,f=h-1,p=r[0],d=v(p);if(d||1<h&&"string"==typeof p&&!g.checkClone&&Nt.test(p))return n.each(function(t){var e=n.eq(t);d&&(r[0]=p.call(this,t,e.html())),E(e,r,i,o)});if(h&&(e=(t=Pt(r,n[0].ownerDocument,!1,n,o)).firstChild,1===t.childNodes.length&&(t=e),e||o)){for(a=(s=k.map(M(t,"script"),jt)).length;c<h;c++)l=t,c!==f&&(l=k.clone(l,!0,!0),a&&k.merge(s,M(l,"script"))),i.call(n[c],l,c);if(a)for(u=s[s.length-1].ownerDocument,k.map(s,Bt),c=0;c<a;c++)l=s[c],kt.test(l.type||"")&&!b.access(l,"globalEval")&&k.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?k._evalUrl&&!l.noModule&&k._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):z(l.textContent.replace(At,""),l,u))}return n}function It(t,e,n){for(var r,i=e?k.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||k.cleanData(M(r)),r.parentNode&&(n&&P(r)&&St(M(r,"script")),r.parentNode.removeChild(r));return t}k.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var r,i,o,s,a,l,u,c=t.cloneNode(!0),h=P(t);if(!(g.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||k.isXMLDoc(t)))for(s=M(c),r=0,i=(o=M(t)).length;r<i;r++)a=o[r],l=s[r],u=void 0,"input"===(u=l.nodeName.toLowerCase())&&_t.test(a.type)?l.checked=a.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=a.defaultValue);if(e)if(n)for(o=o||M(t),s=s||M(c),r=0,i=o.length;r<i;r++)Rt(o[r],s[r]);else Rt(t,c);return 0<(s=M(c,"script")).length&&St(s,!h&&M(t,"script")),c},cleanData:function(t){for(var e,n,r,i=k.event.special,o=0;void 0!==(n=t[o]);o++)if(y(n)){if(e=n[b.expando]){if(e.events)for(r in e.events)i[r]?k.event.remove(n,r):k.removeEvent(n,r,e.handle);n[b.expando]=void 0}n[u.expando]&&(n[u.expando]=void 0)}}}),k.fn.extend({detach:function(t){return It(this,t,!0)},remove:function(t){return It(this,t)},text:function(t){return h(this,function(t){return void 0===t?k.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return E(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Lt(this,t).appendChild(t)})},prepend:function(){return E(this,arguments,function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=Lt(this,t)).insertBefore(t,e.firstChild)})},before:function(){return E(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return E(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(k.cleanData(M(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return k.clone(this,t,e)})},html:function(t){return h(this,function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Ot.test(t)&&!D[(wt.exec(t)||["",""])[1].toLowerCase()]){t=k.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(k.cleanData(M(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var n=[];return E(this,arguments,function(t){var e=this.parentNode;k.inArray(this,n)<0&&(k.cleanData(M(this)),e&&e.replaceChild(t,this))},n)}}),k.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,s){k.fn[t]=function(t){for(var e,n=[],r=k(t),i=r.length-1,o=0;o<=i;o++)e=o===i?this:this.clone(!0),k(r[o])[s](e),H.apply(n,e.get());return this.pushStack(n)}});function Ht(t){var e=t.ownerDocument.defaultView;return(e=e&&e.opener?e:_).getComputedStyle(t)}function Yt(t,e,n){var r,i={};for(r in e)i[r]=t.style[r],t.style[r]=e[r];for(r in n=n.call(t),e)t.style[r]=i[r];return n}var Ft,Wt,qt,Vt,$t,Ut,zt,i,Gt=new RegExp("^("+t+")(?!px)[a-z%]+$","i"),Xt=new RegExp(f.join("|"),"i");function Zt(){var t;i&&(zt.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",i.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",C.appendChild(zt).appendChild(i),t=_.getComputedStyle(i),Ft="1%"!==t.top,Ut=12===Kt(t.marginLeft),i.style.right="60%",Vt=36===Kt(t.right),Wt=36===Kt(t.width),i.style.position="absolute",qt=12===Kt(i.offsetWidth/3),C.removeChild(zt),i=null)}function Kt(t){return Math.round(parseFloat(t))}function Jt(t,e,n){var r,i,o=t.style;return(n=n||Ht(t))&&(""!==(i=n.getPropertyValue(e)||n[e])||P(t)||(i=k.style(t,e)),!g.pixelBoxStyles()&&Gt.test(i)&&Xt.test(e)&&(t=o.width,e=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=t,o.minWidth=e,o.maxWidth=r)),void 0!==i?i+"":i}function Qt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}zt=w.createElement("div"),(i=w.createElement("div")).style&&(i.style.backgroundClip="content-box",i.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===i.style.backgroundClip,k.extend(g,{boxSizingReliable:function(){return Zt(),Wt},pixelBoxStyles:function(){return Zt(),Vt},pixelPosition:function(){return Zt(),Ft},reliableMarginLeft:function(){return Zt(),Ut},scrollboxSize:function(){return Zt(),qt},reliableTrDimensions:function(){var t,e,n;return null==$t&&(t=w.createElement("table"),e=w.createElement("tr"),n=w.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="border:1px solid",e.style.height="1px",n.style.height="9px",n.style.display="block",C.appendChild(t).appendChild(e).appendChild(n),n=_.getComputedStyle(e),$t=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===e.offsetHeight,C.removeChild(t)),$t}}));var te=["Webkit","Moz","ms"],ee=w.createElement("div").style,ne={};function re(t){var e=k.cssProps[t]||ne[t];return e||(t in ee?t:ne[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=te.length;n--;)if((t=te[n]+e)in ee)return t}(t)||t)}var ie=/^(none|table(?!-c[ea]).+)/,oe=/^--/,se={position:"absolute",visibility:"hidden",display:"block"},ae={letterSpacing:"0",fontWeight:"400"};function le(t,e,n){var r=yt.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function ue(t,e,n,r,i,o){var s="width"===e?1:0,a=0,l=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=k.css(t,n+f[s],!0,i)),r?("content"===n&&(l-=k.css(t,"padding"+f[s],!0,i)),"margin"!==n&&(l-=k.css(t,"border"+f[s]+"Width",!0,i))):(l+=k.css(t,"padding"+f[s],!0,i),"padding"!==n?l+=k.css(t,"border"+f[s]+"Width",!0,i):a+=k.css(t,"border"+f[s]+"Width",!0,i));return!r&&0<=o&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l}function ce(t,e,n){var r=Ht(t),i=(!g.boxSizingReliable()||n)&&"border-box"===k.css(t,"boxSizing",!1,r),o=i,s=Jt(t,e,r),a="offset"+e[0].toUpperCase()+e.slice(1);if(Gt.test(s)){if(!n)return s;s="auto"}return(!g.boxSizingReliable()&&i||!g.reliableTrDimensions()&&l(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===k.css(t,"display",!1,r))&&t.getClientRects().length&&(i="border-box"===k.css(t,"boxSizing",!1,r),(o=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+ue(t,e,n||(i?"border":"content"),o,r,s)+"px"}function o(t,e,n,r,i){return new o.prototype.init(t,e,n,r,i)}k.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=Jt(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,o,s,a=x(e),l=oe.test(e),u=t.style;if(l||(e=re(a)),s=k.cssHooks[e]||k.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:u[e];"string"===(o=typeof n)&&(i=yt.exec(n))&&i[1]&&(n=xt(t,e,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(k.cssNumber[a]?"":"px")),g.clearCloneStyle||""!==n||0!==e.indexOf("background")||(u[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,r))||(l?u.setProperty(e,n):u[e]=n))}},css:function(t,e,n,r){var i,o=x(e);return oe.test(e)||(e=re(o)),"normal"===(i=void 0===(i=(o=k.cssHooks[e]||k.cssHooks[o])&&"get"in o?o.get(t,!0,n):i)?Jt(t,e,r):i)&&e in ae&&(i=ae[e]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),k.each(["height","width"],function(t,s){k.cssHooks[s]={get:function(t,e,n){if(e)return!ie.test(k.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ce(t,s,n):Yt(t,se,function(){return ce(t,s,n)})},set:function(t,e,n){var r=Ht(t),i=!g.scrollboxSize()&&"absolute"===r.position,o=(i||n)&&"border-box"===k.css(t,"boxSizing",!1,r),n=n?ue(t,s,n,o,r):0;return o&&i&&(n-=Math.ceil(t["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(r[s])-ue(t,s,"border",!1,r)-.5)),n&&(o=yt.exec(e))&&"px"!==(o[3]||"px")&&(t.style[s]=e,e=k.css(t,s)),le(0,e,n)}}}),k.cssHooks.marginLeft=Qt(g.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Jt(t,"marginLeft"))||t.getBoundingClientRect().left-Yt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),k.each({margin:"",padding:"",border:"Width"},function(i,o){k.cssHooks[i+o]={expand:function(t){for(var e=0,n={},r="string"==typeof t?t.split(" "):[t];e<4;e++)n[i+f[e]+o]=r[e]||r[e-2]||r[0];return n}},"margin"!==i&&(k.cssHooks[i+o].set=le)}),k.fn.extend({css:function(t,e){return h(this,function(t,e,n){var r,i,o={},s=0;if(Array.isArray(e)){for(r=Ht(t),i=e.length;s<i;s++)o[e[s]]=k.css(t,e[s],!1,r);return o}return void 0!==n?k.style(t,e,n):k.css(t,e)},t,e,1<arguments.length)}}),((k.Tween=o).prototype={constructor:o,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||k.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(k.cssNumber[n]?"":"px")},cur:function(){var t=o.propHooks[this.prop];return(t&&t.get?t:o.propHooks._default).get(this)},run:function(t){var e,n=o.propHooks[this.prop];return this.options.duration?this.pos=e=k.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:o.propHooks._default).set(this),this}}).init.prototype=o.prototype,(o.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=k.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){k.fx.step[t.prop]?k.fx.step[t.prop](t):1!==t.elem.nodeType||!k.cssHooks[t.prop]&&null==t.elem.style[re(t.prop)]?t.elem[t.prop]=t.now:k.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=o.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},k.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},k.fx=o.prototype.init,k.fx.step={};var O,he,s,N,fe=/^(?:toggle|show|hide)$/,pe=/queueHooks$/;function de(){he&&(!1===w.hidden&&_.requestAnimationFrame?_.requestAnimationFrame(de):_.setTimeout(de,k.fx.interval),k.fx.tick())}function me(){return _.setTimeout(function(){O=void 0}),O=Date.now()}function ge(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=f[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function ye(t,e,n){for(var r,i=(A.tweeners[e]||[]).concat(A.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function A(i,t,e){var n,o,r,s,a,l,u,c=0,h=A.prefilters.length,f=k.Deferred().always(function(){delete p.elem}),p=function(){if(o)return!1;for(var t=O||me(),t=Math.max(0,d.startTime+d.duration-t),e=1-(t/d.duration||0),n=0,r=d.tweens.length;n<r;n++)d.tweens[n].run(e);return f.notifyWith(i,[d,e,t]),e<1&&r?t:(r||f.notifyWith(i,[d,1,0]),f.resolveWith(i,[d]),!1)},d=f.promise({elem:i,props:k.extend({},t),opts:k.extend(!0,{specialEasing:{},easing:k.easing._default},e),originalProperties:t,originalOptions:e,startTime:O||me(),duration:e.duration,tweens:[],createTween:function(t,e){e=k.Tween(i,d.opts,t,e,d.opts.specialEasing[t]||d.opts.easing);return d.tweens.push(e),e},stop:function(t){var e=0,n=t?d.tweens.length:0;if(o)return this;for(o=!0;e<n;e++)d.tweens[e].run(1);return t?(f.notifyWith(i,[d,1,0]),f.resolveWith(i,[d,t])):f.rejectWith(i,[d,t]),this}}),m=d.props,g=m,y=d.opts.specialEasing;for(r in g)if(a=y[s=x(r)],l=g[r],Array.isArray(l)&&(a=l[1],l=g[r]=l[0]),r!==s&&(g[s]=l,delete g[r]),(u=k.cssHooks[s])&&"expand"in u)for(r in l=u.expand(l),delete g[s],l)r in g||(g[r]=l[r],y[r]=a);else y[s]=a;for(;c<h;c++)if(n=A.prefilters[c].call(d,i,m,d.opts))return v(n.stop)&&(k._queueHooks(d.elem,d.opts.queue).stop=n.stop.bind(n)),n;return k.map(m,ye,d),v(d.opts.start)&&d.opts.start.call(i,d),d.progress(d.opts.progress).done(d.opts.done,d.opts.complete).fail(d.opts.fail).always(d.opts.always),k.fx.timer(k.extend(p,{elem:i,anim:d,queue:d.opts.queue})),d}k.Animation=k.extend(A,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return xt(n.elem,t,yt.exec(e),n),n}]},tweener:function(t,e){for(var n,r=0,i=(t=v(t)?(e=t,["*"]):t.match(S)).length;r<i;r++)n=t[r],A.tweeners[n]=A.tweeners[n]||[],A.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var r,i,o,s,a,l,u,c="width"in e||"height"in e,h=this,f={},p=t.style,d=t.nodeType&&gt(t),m=b.get(t,"fxshow");for(r in n.queue||(null==(s=k._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,h.always(function(){h.always(function(){s.unqueued--,k.queue(t,"fx").length||s.empty.fire()})})),e)if(i=e[r],fe.test(i)){if(delete e[r],o=o||"toggle"===i,i===(d?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;d=!0}f[r]=m&&m[r]||k.style(t,r)}if((l=!k.isEmptyObject(e))||!k.isEmptyObject(f))for(r in c&&1===t.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=b.get(t,"display")),"none"===(c=k.css(t,"display"))&&(u?c=u:(T([t],!0),u=t.style.display||u,c=k.css(t,"display"),T([t]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===k.css(t,"float")&&(l||(h.done(function(){p.display=u}),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",h.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,f)l||(m?"hidden"in m&&(d=m.hidden):m=b.access(t,"fxshow",{display:u}),o&&(m.hidden=!d),d&&T([t],!0),h.done(function(){for(r in d||T([t]),b.remove(t,"fxshow"),f)k.style(t,r,f[r])})),l=ye(d?m[r]:0,r,h),r in m||(m[r]=l.start,d&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?A.prefilters.unshift(t):A.prefilters.push(t)}}),k.speed=function(t,e,n){var r=t&&"object"==typeof t?k.extend({},t):{complete:n||!n&&e||v(t)&&t,duration:t,easing:n&&e||e&&!v(e)&&e};return k.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in k.fx.speeds?r.duration=k.fx.speeds[r.duration]:r.duration=k.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&k.dequeue(this,r.queue)},r},k.fn.extend({fadeTo:function(t,e,n,r){return this.filter(gt).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(e,t,n,r){function i(){var t=A(this,k.extend({},e),s);(o||b.get(this,"finish"))&&t.stop(!0)}var o=k.isEmptyObject(e),s=k.speed(t,n,r);return i.finish=i,o||!1===s.queue?this.each(i):this.queue(s.queue,i)},stop:function(i,t,o){function s(t){var e=t.stop;delete t.stop,e(o)}return"string"!=typeof i&&(o=t,t=i,i=void 0),t&&this.queue(i||"fx",[]),this.each(function(){var t=!0,e=null!=i&&i+"queueHooks",n=k.timers,r=b.get(this);if(e)r[e]&&r[e].stop&&s(r[e]);else for(e in r)r[e]&&r[e].stop&&pe.test(e)&&s(r[e]);for(e=n.length;e--;)n[e].elem!==this||null!=i&&n[e].queue!==i||(n[e].anim.stop(o),t=!1,n.splice(e,1));!t&&o||k.dequeue(this,i)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var t,e=b.get(this),n=e[s+"queue"],r=e[s+"queueHooks"],i=k.timers,o=n?n.length:0;for(e.finish=!0,k.queue(this,s,[]),r&&r.stop&&r.stop.call(this,!0),t=i.length;t--;)i[t].elem===this&&i[t].queue===s&&(i[t].anim.stop(!0),i.splice(t,1));for(t=0;t<o;t++)n[t]&&n[t].finish&&n[t].finish.call(this);delete e.finish})}}),k.each(["toggle","show","hide"],function(t,r){var i=k.fn[r];k.fn[r]=function(t,e,n){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(ge(r,!0),t,e,n)}}),k.each({slideDown:ge("show"),slideUp:ge("hide"),slideToggle:ge("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,r){k.fn[t]=function(t,e,n){return this.animate(r,t,e,n)}}),k.timers=[],k.fx.tick=function(){var t,e=0,n=k.timers;for(O=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||k.fx.stop(),O=void 0},k.fx.timer=function(t){k.timers.push(t),k.fx.start()},k.fx.interval=13,k.fx.start=function(){he||(he=!0,de())},k.fx.stop=function(){he=null},k.fx.speeds={slow:600,fast:200,_default:400},k.fn.delay=function(r,t){return r=k.fx&&k.fx.speeds[r]||r,this.queue(t=t||"fx",function(t,e){var n=_.setTimeout(t,r);e.stop=function(){_.clearTimeout(n)}})},s=w.createElement("input"),N=w.createElement("select").appendChild(w.createElement("option")),s.type="checkbox",g.checkOn=""!==s.value,g.optSelected=N.selected,(s=w.createElement("input")).value="t",s.type="radio",g.radioValue="t"===s.value;var ve,xe=k.expr.attrHandle,be=(k.fn.extend({attr:function(t,e){return h(this,k.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){k.removeAttr(this,t)})}}),k.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?k.prop(t,e,n):(1===o&&k.isXMLDoc(t)||(i=k.attrHooks[e.toLowerCase()]||(k.expr.match.bool.test(e)?ve:void 0)),void 0!==n?null===n?void k.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):!(i&&"get"in i&&null!==(r=i.get(t,e)))&&null==(r=k.find.attr(t,e))?void 0:r)},attrHooks:{type:{set:function(t,e){var n;if(!g.radioValue&&"radio"===e&&l(t,"input"))return n=t.value,t.setAttribute("type",e),n&&(t.value=n),e}}},removeAttr:function(t,e){var n,r=0,i=e&&e.match(S);if(i&&1===t.nodeType)for(;n=i[r++];)t.removeAttribute(n)}}),ve={set:function(t,e,n){return!1===e?k.removeAttr(t,n):t.setAttribute(n,n),n}},k.each(k.expr.match.bool.source.match(/\w+/g),function(t,e){var s=xe[e]||k.find.attr;xe[e]=function(t,e,n){var r,i,o=e.toLowerCase();return n||(i=xe[o],xe[o]=r,r=null!=s(t,e,n)?o:null,xe[o]=i),r}}),/^(?:input|select|textarea|button)$/i),_e=/^(?:a|area)$/i;function L(t){return(t.match(S)||[]).join(" ")}function j(t){return t.getAttribute&&t.getAttribute("class")||""}function we(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(S)||[]}k.fn.extend({prop:function(t,e){return h(this,k.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[k.propFix[t]||t]})}}),k.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&k.isXMLDoc(t)||(e=k.propFix[e]||e,i=k.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=k.find.attr(t,"tabindex");return e?parseInt(e,10):be.test(t.nodeName)||_e.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(k.propHooks.selected={get:function(t){t=t.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){t=t.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),k.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){k.propFix[this.toLowerCase()]=this}),k.fn.extend({addClass:function(e){var t,n,r,i,o,s,a=0;if(v(e))return this.each(function(t){k(this).addClass(e.call(this,t,j(this)))});if((t=we(e)).length)for(;n=this[a++];)if(s=j(n),r=1===n.nodeType&&" "+L(s)+" "){for(o=0;i=t[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s!==(s=L(r))&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,i,o,s,a=0;if(v(e))return this.each(function(t){k(this).removeClass(e.call(this,t,j(this)))});if(!arguments.length)return this.attr("class","");if((t=we(e)).length)for(;n=this[a++];)if(s=j(n),r=1===n.nodeType&&" "+L(s)+" "){for(o=0;i=t[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");s!==(s=L(r))&&n.setAttribute("class",s)}return this},toggleClass:function(i,e){var o=typeof i,s="string"==o||Array.isArray(i);return"boolean"==typeof e&&s?e?this.addClass(i):this.removeClass(i):v(i)?this.each(function(t){k(this).toggleClass(i.call(this,t,j(this),e),e)}):this.each(function(){var t,e,n,r;if(s)for(e=0,n=k(this),r=we(i);t=r[e++];)n.hasClass(t)?n.removeClass(t):n.addClass(t);else void 0!==i&&"boolean"!=o||((t=j(this))&&b.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",!t&&!1!==i&&b.get(this,"__className__")||""))})},hasClass:function(t){for(var e,n=0,r=" "+t+" ";e=this[n++];)if(1===e.nodeType&&-1<(" "+L(j(e))+" ").indexOf(r))return!0;return!1}});function ke(t){t.stopPropagation()}var Se=/\r/g,Ce=(k.fn.extend({val:function(e){var n,t,r,i=this[0];return arguments.length?(r=v(e),this.each(function(t){1===this.nodeType&&(null==(t=r?e.call(this,t,k(this).val()):e)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=k.map(t,function(t){return null==t?"":t+""})),(n=k.valHooks[this.type]||k.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,t,"value")||(this.value=t))})):i?(n=k.valHooks[i.type]||k.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(t=n.get(i,"value"))?t:"string"==typeof(t=i.value)?t.replace(Se,""):null==t?"":t:void 0}}),k.extend({valHooks:{option:{get:function(t){var e=k.find.attr(t,"value");return null!=e?e:L(k.text(t))}},select:{get:function(t){for(var e,n=t.options,r=t.selectedIndex,i="select-one"===t.type,o=i?null:[],s=i?r+1:n.length,a=r<0?s:i?r:0;a<s;a++)if(((e=n[a]).selected||a===r)&&!e.disabled&&(!e.parentNode.disabled||!l(e.parentNode,"optgroup"))){if(e=k(e).val(),i)return e;o.push(e)}return o},set:function(t,e){for(var n,r,i=t.options,o=k.makeArray(e),s=i.length;s--;)((r=i[s]).selected=-1<k.inArray(k.valHooks.option.get(r),o))&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),k.each(["radio","checkbox"],function(){k.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<k.inArray(k(t).val(),e)}},g.checkOn||(k.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),g.focusin="onfocusin"in _,/^(?:focusinfocus|focusoutblur)$/),Pe=(k.extend(k.event,{trigger:function(t,e,n,r){var i,o,s,a,l,u,c,h=[n||w],f=q.call(t,"type")?t.type:t,p=q.call(t,"namespace")?t.namespace.split("."):[],d=c=o=n=n||w;if(3!==n.nodeType&&8!==n.nodeType&&!Ce.test(f+k.event.triggered)&&(-1<f.indexOf(".")&&(f=(p=f.split(".")).shift(),p.sort()),a=f.indexOf(":")<0&&"on"+f,(t=t[k.expando]?t:new k.Event(f,"object"==typeof t&&t)).isTrigger=r?2:3,t.namespace=p.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:k.makeArray(e,[t]),u=k.event.special[f]||{},r||!u.trigger||!1!==u.trigger.apply(n,e))){if(!r&&!u.noBubble&&!m(n)){for(s=u.delegateType||f,Ce.test(s+f)||(d=d.parentNode);d;d=d.parentNode)h.push(d),o=d;o===(n.ownerDocument||w)&&h.push(o.defaultView||o.parentWindow||_)}for(i=0;(d=h[i++])&&!t.isPropagationStopped();)c=d,t.type=1<i?s:u.bindType||f,(l=(b.get(d,"events")||Object.create(null))[t.type]&&b.get(d,"handle"))&&l.apply(d,e),(l=a&&d[a])&&l.apply&&y(d)&&(t.result=l.apply(d,e),!1===t.result&&t.preventDefault());return t.type=f,r||t.isDefaultPrevented()||u._default&&!1!==u._default.apply(h.pop(),e)||!y(n)||a&&v(n[f])&&!m(n)&&((o=n[a])&&(n[a]=null),k.event.triggered=f,t.isPropagationStopped()&&c.addEventListener(f,ke),n[f](),t.isPropagationStopped()&&c.removeEventListener(f,ke),k.event.triggered=void 0,o&&(n[a]=o)),t.result}},simulate:function(t,e,n){n=k.extend(new k.Event,n,{type:t,isSimulated:!0});k.event.trigger(n,null,e)}}),k.fn.extend({trigger:function(t,e){return this.each(function(){k.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return k.event.trigger(t,e,n,!0)}}),g.focusin||k.each({focus:"focusin",blur:"focusout"},function(n,r){function i(t){k.event.simulate(r,t.target,k.event.fix(t))}k.event.special[r]={setup:function(){var t=this.ownerDocument||this.document||this,e=b.access(t,r);e||t.addEventListener(n,i,!0),b.access(t,r,(e||0)+1)},teardown:function(){var t=this.ownerDocument||this.document||this,e=b.access(t,r)-1;e?b.access(t,r,e):(t.removeEventListener(n,i,!0),b.remove(t,r))}}}),_.location),Te={guid:Date.now()},De=/\?/,Me=(k.parseXML=function(t){var e,n;if(!t||"string"!=typeof t)return null;try{e=(new _.DOMParser).parseFromString(t,"text/xml")}catch(t){}return n=e&&e.getElementsByTagName("parsererror")[0],e&&!n||k.error("Invalid XML: "+(n?k.map(n.childNodes,function(t){return t.textContent}).join("\n"):t)),e},/\[\]$/),Ee=/\r?\n/g,Oe=/^(?:submit|button|image|reset|file)$/i,Ne=/^(?:input|select|textarea|keygen)/i;k.param=function(t,e){function n(t,e){e=v(e)?e():e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==e?"":e)}var r,i=[];if(null==t)return"";if(Array.isArray(t)||t.jquery&&!k.isPlainObject(t))k.each(t,function(){n(this.name,this.value)});else for(r in t)!function n(r,t,i,o){if(Array.isArray(t))k.each(t,function(t,e){i||Me.test(r)?o(r,e):n(r+"["+("object"==typeof e&&null!=e?t:"")+"]",e,i,o)});else if(i||"object"!==d(t))o(r,t);else for(var e in t)n(r+"["+e+"]",t[e],i,o)}(r,t[r],e,n);return i.join("&")},k.fn.extend({serialize:function(){return k.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=k.prop(this,"elements");return t?k.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!k(this).is(":disabled")&&Ne.test(this.nodeName)&&!Oe.test(t)&&(this.checked||!_t.test(t))}).map(function(t,e){var n=k(this).val();return null==n?null:Array.isArray(n)?k.map(n,function(t){return{name:e.name,value:t.replace(Ee,"\r\n")}}):{name:e.name,value:n.replace(Ee,"\r\n")}}).get()}});var Ae=/%20/g,Le=/#.*$/,je=/([?&])_=[^&]*/,Be=/^(.*?):[ \t]*([^\r\n]*)$/gm,Re=/^(?:GET|HEAD)$/,Ie=/^\/\//,He={},Ye={},Fe="*/".concat("*"),We=w.createElement("a");function qe(o){return function(t,e){"string"!=typeof t&&(e=t,t="*");var n,r=0,i=t.toLowerCase().match(S)||[];if(v(e))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(e)):(o[n]=o[n]||[]).push(e)}}function Ve(e,r,i,o){var s={},a=e===Ye;function l(t){var n;return s[t]=!0,k.each(e[t]||[],function(t,e){e=e(r,i,o);return"string"!=typeof e||a||s[e]?a?!(n=e):void 0:(r.dataTypes.unshift(e),l(e),!1)}),n}return l(r.dataTypes[0])||!s["*"]&&l("*")}function $e(t,e){var n,r,i=k.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r=r||{})[n]=e[n]);return r&&k.extend(!0,t,r),t}We.href=Pe.href,k.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Pe.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Pe.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Fe,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":k.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?$e($e(t,k.ajaxSettings),e):$e(k.ajaxSettings,t)},ajaxPrefilter:qe(He),ajaxTransport:qe(Ye),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0);var l,u,c,n,h,f,p,r,d=k.ajaxSetup({},e=e||{}),m=d.context||d,g=d.context&&(m.nodeType||m.jquery)?k(m):k.event,y=k.Deferred(),v=k.Callbacks("once memory"),x=d.statusCode||{},i={},o={},s="canceled",b={readyState:0,getResponseHeader:function(t){var e;if(f){if(!n)for(n={};e=Be.exec(c);)n[e[1].toLowerCase()+" "]=(n[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=n[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return f?c:null},setRequestHeader:function(t,e){return null==f&&(t=o[t.toLowerCase()]=o[t.toLowerCase()]||t,i[t]=e),this},overrideMimeType:function(t){return null==f&&(d.mimeType=t),this},statusCode:function(t){if(t)if(f)b.always(t[b.status]);else for(var e in t)x[e]=[x[e],t[e]];return this},abort:function(t){t=t||s;return l&&l.abort(t),a(0,t),this}};if(y.promise(b),d.url=((t||d.url||Pe.href)+"").replace(Ie,Pe.protocol+"//"),d.type=e.method||e.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(S)||[""],null==d.crossDomain){t=w.createElement("a");try{t.href=d.url,t.href=t.href,d.crossDomain=We.protocol+"//"+We.host!=t.protocol+"//"+t.host}catch(t){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=k.param(d.data,d.traditional)),Ve(He,d,e,b),f)return b;for(r in(p=k.event&&d.global)&&0==k.active++&&k.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Re.test(d.type),u=d.url.replace(Le,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Ae,"+")):(t=d.url.slice(u.length),d.data&&(d.processData||"string"==typeof d.data)&&(u+=(De.test(u)?"&":"?")+d.data,delete d.data),!1===d.cache&&(u=u.replace(je,"$1"),t=(De.test(u)?"&":"?")+"_="+Te.guid+++t),d.url=u+t),d.ifModified&&(k.lastModified[u]&&b.setRequestHeader("If-Modified-Since",k.lastModified[u]),k.etag[u]&&b.setRequestHeader("If-None-Match",k.etag[u])),(d.data&&d.hasContent&&!1!==d.contentType||e.contentType)&&b.setRequestHeader("Content-Type",d.contentType),b.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Fe+"; q=0.01":""):d.accepts["*"]),d.headers)b.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(!1===d.beforeSend.call(m,b,d)||f))return b.abort();if(s="abort",v.add(d.complete),b.done(d.success),b.fail(d.error),l=Ve(Ye,d,e,b)){if(b.readyState=1,p&&g.trigger("ajaxSend",[b,d]),f)return b;d.async&&0<d.timeout&&(h=_.setTimeout(function(){b.abort("timeout")},d.timeout));try{f=!1,l.send(i,a)}catch(t){if(f)throw t;a(-1,t)}}else a(-1,"No Transport");function a(t,e,n,r){var i,o,s,a=e;f||(f=!0,h&&_.clearTimeout(h),l=void 0,c=r||"",b.readyState=0<t?4:0,r=200<=t&&t<300||304===t,n&&(s=function(t,e,n){for(var r,i,o,s,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||t.converters[i+" "+l[0]]){o=i;break}s=s||i}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(d,b,n)),!r&&-1<k.inArray("script",d.dataTypes)&&k.inArray("json",d.dataTypes)<0&&(d.converters["text script"]=function(){}),s=function(t,e,n,r){var i,o,s,a,l,u={},c=t.dataTypes.slice();if(c[1])for(s in t.converters)u[s.toLowerCase()]=t.converters[s];for(o=c.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!l&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=u[l+" "+o]||u["* "+o]))for(i in u)if((a=i.split(" "))[1]===o&&(s=u[l+" "+a[0]]||u["* "+a[0]])){!0===s?s=u[i]:!0!==u[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(d,s,b,r),r?(d.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(k.lastModified[u]=n),(n=b.getResponseHeader("etag"))&&(k.etag[u]=n)),204===t||"HEAD"===d.type?a="nocontent":304===t?a="notmodified":(a=s.state,i=s.data,r=!(o=s.error))):(o=a,!t&&a||(a="error",t<0&&(t=0))),b.status=t,b.statusText=(e||a)+"",r?y.resolveWith(m,[i,a,b]):y.rejectWith(m,[b,a,o]),b.statusCode(x),x=void 0,p&&g.trigger(r?"ajaxSuccess":"ajaxError",[b,d,r?i:o]),v.fireWith(m,[b,a]),p&&(g.trigger("ajaxComplete",[b,d]),--k.active||k.event.trigger("ajaxStop")))}return b},getJSON:function(t,e,n){return k.get(t,e,n,"json")},getScript:function(t,e){return k.get(t,void 0,e,"script")}}),k.each(["get","post"],function(t,i){k[i]=function(t,e,n,r){return v(e)&&(r=r||n,n=e,e=void 0),k.ajax(k.extend({url:t,type:i,dataType:r,data:e,success:n},k.isPlainObject(t)&&t))}}),k.ajaxPrefilter(function(t){for(var e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),k._evalUrl=function(t,e,n){return k.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){k.globalEval(t,e,n)}})},k.fn.extend({wrapAll:function(t){return this[0]&&(v(t)&&(t=t.call(this[0])),t=k(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(n){return v(n)?this.each(function(t){k(this).wrapInner(n.call(this,t))}):this.each(function(){var t=k(this),e=t.contents();e.length?e.wrapAll(n):t.append(n)})},wrap:function(e){var n=v(e);return this.each(function(t){k(this).wrapAll(n?e.call(this,t):e)})},unwrap:function(t){return this.parent(t).not("body").each(function(){k(this).replaceWith(this.childNodes)}),this}}),k.expr.pseudos.hidden=function(t){return!k.expr.pseudos.visible(t)},k.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},k.ajaxSettings.xhr=function(){try{return new _.XMLHttpRequest}catch(t){}};var Ue={0:200,1223:204},ze=k.ajaxSettings.xhr(),Ge=(g.cors=!!ze&&"withCredentials"in ze,g.ajax=ze=!!ze,k.ajaxTransport(function(i){var o,s;if(g.cors||ze&&!i.crossDomain)return{send:function(t,e){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)r.setRequestHeader(n,t[n]);o=function(t){return function(){o&&(o=s=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===t?r.abort():"error"===t?"number"!=typeof r.status?e(0,"error"):e(r.status,r.statusText):e(Ue[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),s=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=s:r.onreadystatechange=function(){4===r.readyState&&_.setTimeout(function(){o&&s()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(t){if(o)throw t}},abort:function(){o&&o()}}}),k.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),k.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return k.globalEval(t),t}}}),k.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),k.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(t,e){r=k("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(t){r.remove(),i=null,t&&e("error"===t.type?404:200,t.type)}),w.head.appendChild(r[0])},abort:function(){i&&i()}}}),[]),Xe=/(=)\?(?=&|$)|\?\?/,Ze=(k.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ge.pop()||k.expando+"_"+Te.guid++;return this[t]=!0,t}}),k.ajaxPrefilter("json jsonp",function(t,e,n){var r,i,o,s=!1!==t.jsonp&&(Xe.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Xe.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return r=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Xe,"$1"+r):!1!==t.jsonp&&(t.url+=(De.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return o||k.error(r+" was not called"),o[0]},t.dataTypes[0]="json",i=_[r],_[r]=function(){o=arguments},n.always(function(){void 0===i?k(_).removeProp(r):_[r]=i,t[r]&&(t.jsonpCallback=e.jsonpCallback,Ge.push(r)),o&&v(i)&&i(o[0]),o=i=void 0}),"script"}),g.createHTMLDocument=((t=w.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===t.childNodes.length),k.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(g.createHTMLDocument?((r=(e=w.implementation.createHTMLDocument("")).createElement("base")).href=w.location.href,e.head.appendChild(r)):e=w),r=!n&&[],(n=K.exec(t))?[e.createElement(n[1])]:(n=Pt([t],e,r),r&&r.length&&k(r).remove(),k.merge([],n.childNodes)));var r},k.fn.load=function(t,e,n){var r,i,o,s=this,a=t.indexOf(" ");return-1<a&&(r=L(t.slice(a)),t=t.slice(0,a)),v(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),0<s.length&&k.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done(function(t){o=arguments,s.html(r?k("<div>").append(k.parseHTML(t)).find(r):t)}).always(n&&function(t,e){s.each(function(){n.apply(this,o||[t.responseText,e,t])})}),this},k.expr.pseudos.animated=function(e){return k.grep(k.timers,function(t){return e===t.elem}).length},k.offset={setOffset:function(t,e,n){var r,i,o,s,a=k.css(t,"position"),l=k(t),u={};"static"===a&&(t.style.position="relative"),o=l.offset(),r=k.css(t,"top"),s=k.css(t,"left"),a=("absolute"===a||"fixed"===a)&&-1<(r+s).indexOf("auto")?(i=(a=l.position()).top,a.left):(i=parseFloat(r)||0,parseFloat(s)||0),null!=(e=v(e)?e.call(t,n,k.extend({},o)):e).top&&(u.top=e.top-o.top+i),null!=e.left&&(u.left=e.left-o.left+a),"using"in e?e.using.call(t,u):l.css(u)}},k.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){k.offset.setOffset(this,e,t)});var t,n=this[0];return n?n.getClientRects().length?(t=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,r=this[0],i={top:0,left:0};if("fixed"===k.css(r,"position"))e=r.getBoundingClientRect();else{for(e=this.offset(),n=r.ownerDocument,t=r.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===k.css(t,"position");)t=t.parentNode;t&&t!==r&&1===t.nodeType&&((i=k(t).offset()).top+=k.css(t,"borderTopWidth",!0),i.left+=k.css(t,"borderLeftWidth",!0))}return{top:e.top-i.top-k.css(r,"marginTop",!0),left:e.left-i.left-k.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===k.css(t,"position");)t=t.offsetParent;return t||C})}}),k.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,i){var o="pageYOffset"===i;k.fn[e]=function(t){return h(this,function(t,e,n){var r;if(m(t)?r=t:9===t.nodeType&&(r=t.defaultView),void 0===n)return r?r[i]:t[e];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):t[e]=n},e,t,arguments.length)}}),k.each(["top","left"],function(t,n){k.cssHooks[n]=Qt(g.pixelPosition,function(t,e){if(e)return e=Jt(t,n),Gt.test(e)?k(t).position()[n]+"px":e})}),k.each({Height:"height",Width:"width"},function(s,a){k.each({padding:"inner"+s,content:a,"":"outer"+s},function(r,o){k.fn[o]=function(t,e){var n=arguments.length&&(r||"boolean"!=typeof t),i=r||(!0===t||!0===e?"margin":"border");return h(this,function(t,e,n){var r;return m(t)?0===o.indexOf("outer")?t["inner"+s]:t.document.documentElement["client"+s]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+s],r["scroll"+s],t.body["offset"+s],r["offset"+s],r["client"+s])):void 0===n?k.css(t,e,i):k.style(t,e,n,i)},a,n?t:void 0,n)}})}),k.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){k.fn[e]=function(t){return this.on(e,t)}}),k.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),k.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,n){k.fn[n]=function(t,e){return 0<arguments.length?this.on(n,null,t,e):this.trigger(n)}}),/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g),Ke=(k.proxy=function(t,e){var n,r;if("string"==typeof e&&(r=t[e],e=t,t=r),v(t))return n=a.call(arguments,2),(r=function(){return t.apply(e||this,n.concat(a.call(arguments)))}).guid=t.guid=t.guid||k.guid++,r},k.holdReady=function(t){t?k.readyWait++:k.ready(!0)},k.isArray=Array.isArray,k.parseJSON=JSON.parse,k.nodeName=l,k.isFunction=v,k.isWindow=m,k.camelCase=x,k.type=d,k.now=Date.now,k.isNumeric=function(t){var e=k.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},k.trim=function(t){return null==t?"":(t+"").replace(Ze,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return k}),_.jQuery),Je=_.$;return k.noConflict=function(t){return _.$===k&&(_.$=Je),t&&_.jQuery===k&&(_.jQuery=Ke),k},void 0===B&&(_.jQuery=_.$=k),k}),e=this,t=function(t){"use strict";function x(t){return null==t?window:"[object Window]"!==t.toString()?(e=t.ownerDocument)&&e.defaultView||window:t;var e}function m(t){return t instanceof x(t).Element||t instanceof Element}function l(t){return t instanceof x(t).HTMLElement||t instanceof HTMLElement}function o(t){if("undefined"!=typeof ShadowRoot)return t instanceof x(t).ShadowRoot||t instanceof ShadowRoot}var P=Math.max,T=Math.min,b=Math.round;function c(t,e){void 0===e&&(e=!1);var n=t.getBoundingClientRect(),r=1,i=1;return l(t)&&e&&(e=t.offsetHeight,0<(t=t.offsetWidth)&&(r=b(n.width)/t||1),0<e&&(i=b(n.height)/e||1)),{width:n.width/r,height:n.height/i,top:n.top/i,right:n.right/r,bottom:n.bottom/i,left:n.left/r,x:n.left/r,y:n.top/i}}function u(t){t=x(t);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function h(t){return t?(t.nodeName||"").toLowerCase():null}function _(t){return((m(t)?t.ownerDocument:t.document)||window.document).documentElement}function f(t){return c(_(t)).left+u(t).scrollLeft}function w(t){return x(t).getComputedStyle(t)}function p(t){var t=w(t),e=t.overflow,n=t.overflowX,t=t.overflowY;return/auto|scroll|overlay|hidden/.test(e+t+n)}function C(t,e,n){void 0===n&&(n=!1);var r=l(e),i=l(e)&&(s=(i=e).getBoundingClientRect(),o=b(s.width)/i.offsetWidth||1,s=b(s.height)/i.offsetHeight||1,1!==o||1!==s),o=_(e),s=c(t,i),t={scrollLeft:0,scrollTop:0},a={x:0,y:0};return!r&&n||("body"===h(e)&&!p(o)||(t=(r=e)!==x(r)&&l(r)?{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}:u(r)),l(e)?((a=c(e,!0)).x+=e.clientLeft,a.y+=e.clientTop):o&&(a.x=f(o))),{x:s.left+t.scrollLeft-a.x,y:s.top+t.scrollTop-a.y,width:s.width,height:s.height}}function D(t){var e=c(t),n=t.offsetWidth,r=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:r}}function s(t){return"html"===h(t)?t:t.assignedSlot||t.parentNode||(o(t)?t.host:null)||_(t)}function g(t,e){void 0===e&&(e=[]);var n=function t(e){return 0<=["html","body","#document"].indexOf(h(e))?e.ownerDocument.body:l(e)&&p(e)?e:t(s(e))}(t),t=n===(null==(t=t.ownerDocument)?void 0:t.body),r=x(n),r=t?[r].concat(r.visualViewport||[],p(n)?n:[]):n,n=e.concat(r);return t?n:n.concat(g(s(r)))}function i(t){return l(t)&&"fixed"!==w(t).position?t.offsetParent:null}function M(t){for(var e,n=x(t),r=i(t);r&&(e=r,0<=["table","td","th"].indexOf(h(e)))&&"static"===w(r).position;)r=i(r);return(!r||"html"!==h(r)&&("body"!==h(r)||"static"!==w(r).position))&&(r||function(t){var e=-1!==navigator.userAgent.toLowerCase().indexOf("firefox"),n=-1!==navigator.userAgent.indexOf("Trident");if(n&&l(t)&&"fixed"===w(t).position)return null;var r=s(t);for(o(r)&&(r=r.host);l(r)&&["html","body"].indexOf(h(r))<0;){var i=w(r);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||e&&"filter"===i.willChange||e&&i.filter&&"none"!==i.filter)return r;r=r.parentNode}return null}(t))||n}var E="top",O="bottom",N="right",A="left",L="auto",j=[E,O,N,A],B="start",k="end",W="clippingParents",q="viewport",d="popper",V="reference",$=j.reduce(function(t,e){return t.concat([e+"-"+B,e+"-"+k])},[]),U=[].concat(j,[L]).reduce(function(t,e){return t.concat([e,e+"-"+B,e+"-"+k])},[]),z=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function G(t){var n=new Map,r=new Set,i=[];return t.forEach(function(t){n.set(t.name,t)}),t.forEach(function(t){r.has(t.name)||!function e(t){r.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){r.has(t)||(t=n.get(t))&&e(t)}),i.push(t)}(t)}),i}function y(t){for(var e=arguments.length,n=new Array(1<e?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return[].concat(n).reduce(function(t,e){return t.replace(/%s/,e)},t)}var v='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',X='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',Z=["name","enabled","phase","fn","effect","requires","options"];function R(t){return t.split("-")[0]}function a(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&o(n)){var r=e;do{if(r&&t.isSameNode(r))return!0}while(r=r.parentNode||r.host)}return!1}function K(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function J(t,e){return e===q?K((r=x(n=t),i=_(n),r=r.visualViewport,o=i.clientWidth,i=i.clientHeight,a=s=0,r&&(o=r.width,i=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(s=r.offsetLeft,a=r.offsetTop)),{width:o,height:i,x:s+f(n),y:a})):m(e)?((o=c(r=e)).top=o.top+r.clientTop,o.left=o.left+r.clientLeft,o.bottom=o.top+r.clientHeight,o.right=o.left+r.clientWidth,o.width=r.clientWidth,o.height=r.clientHeight,o.x=o.left,o.y=o.top,o):K((i=_(t),s=_(i),n=u(i),a=null==(a=i.ownerDocument)?void 0:a.body,e=P(s.scrollWidth,s.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),t=P(s.scrollHeight,s.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),i=-n.scrollLeft+f(i),n=-n.scrollTop,"rtl"===w(a||s).direction&&(i+=P(s.clientWidth,a?a.clientWidth:0)-e),{width:e,height:t,x:i,y:n}));var n,r,i,o,s,a}function Q(n,t,e){var r,i="clippingParents"===t?(o=g(s(i=n)),m(r=0<=["absolute","fixed"].indexOf(w(i).position)&&l(i)?M(i):i)?o.filter(function(t){return m(t)&&a(t,r)&&"body"!==h(t)}):[]):[].concat(t),o=[].concat(i,[e]),t=o[0],e=o.reduce(function(t,e){e=J(n,e);return t.top=P(e.top,t.top),t.right=T(e.right,t.right),t.bottom=T(e.bottom,t.bottom),t.left=P(e.left,t.left),t},J(n,t));return e.width=e.right-e.left,e.height=e.bottom-e.top,e.x=e.left,e.y=e.top,e}function I(t){return t.split("-")[1]}function tt(t){return 0<=["top","bottom"].indexOf(t)?"x":"y"}function et(t){var e,n=t.reference,r=t.element,t=t.placement,i=t?R(t):null,t=t?I(t):null,o=n.x+n.width/2-r.width/2,s=n.y+n.height/2-r.height/2;switch(i){case E:e={x:o,y:n.y-r.height};break;case O:e={x:o,y:n.y+n.height};break;case N:e={x:n.x+n.width,y:s};break;case A:e={x:n.x-r.width,y:s};break;default:e={x:n.x,y:n.y}}var a=i?tt(i):null;if(null!=a){var l="y"===a?"height":"width";switch(t){case B:e[a]=e[a]-(n[l]/2-r[l]/2);break;case k:e[a]=e[a]+(n[l]/2-r[l]/2)}}return e}function nt(){return{top:0,right:0,bottom:0,left:0}}function rt(t){return Object.assign({},nt(),t)}function it(n,t){return t.reduce(function(t,e){return t[e]=n,t},{})}function H(t,e){var r,e=e=void 0===e?{}:e,n=e.placement,n=void 0===n?t.placement:n,i=e.boundary,i=void 0===i?W:i,o=e.rootBoundary,o=void 0===o?q:o,s=e.elementContext,s=void 0===s?d:s,a=e.altBoundary,a=void 0!==a&&a,e=e.padding,e=void 0===e?0:e,e=rt("number"!=typeof e?e:it(e,j)),l=t.rects.popper,a=t.elements[a?s===d?V:d:s],a=Q(m(a)?a:a.contextElement||_(t.elements.popper),i,o),i=c(t.elements.reference),o=et({reference:i,element:l,strategy:"absolute",placement:n}),l=K(Object.assign({},l,o)),o=s===d?l:i,u={top:a.top-o.top+e.top,bottom:o.bottom-a.bottom+e.bottom,left:a.left-o.left+e.left,right:o.right-a.right+e.right},l=t.modifiersData.offset;return s===d&&l&&(r=l[n],Object.keys(u).forEach(function(t){var e=0<=[N,O].indexOf(t)?1:-1,n=0<=[E,O].indexOf(t)?"y":"x";u[t]+=r[n]*e})),u}var ot="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",st={placement:"bottom",modifiers:[],strategy:"absolute"};function at(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function e(t){var t=t=void 0===t?{}:t,e=t.defaultModifiers,p=void 0===e?[]:e,e=t.defaultOptions,d=void 0===e?st:e;return function(s,a,e){void 0===e&&(e=d);var n,r,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},st,d),modifiersData:{},elements:{reference:s,popper:a},attributes:{},styles:{}},u=[],c=!1,h={state:l,setOptions:function(t){var n,e,r,i,o,t="function"==typeof t?t(l.options):t,t=(f(),l.options=Object.assign({},d,l.options,t),l.scrollParents={reference:m(s)?g(s):s.contextElement?g(s.contextElement):[],popper:g(a)},t=[].concat(p,l.options.modifiers),e=t.reduce(function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t},{}),t=Object.keys(e).map(function(t){return e[t]}),n=G(t),z.reduce(function(t,e){return t.concat(n.filter(function(t){return t.phase===e}))},[])),t=(l.orderedModifiers=t.filter(function(t){return t.enabled}),t=[].concat(t,l.options.modifiers),i=function(t){return t.name},o=new Set,t.filter(function(t){t=i(t);if(!o.has(t))return o.add(t),!0})),t=((r=t).forEach(function(n){[].concat(Object.keys(n),Z).filter(function(t,e,n){return n.indexOf(t)===e}).forEach(function(t){switch(t){case"name":"string"!=typeof n.name&&console.error(y(v,String(n.name),'"name"','"string"','"'+String(n.name)+'"'));break;case"enabled":"boolean"!=typeof n.enabled&&console.error(y(v,n.name,'"enabled"','"boolean"','"'+String(n.enabled)+'"'));break;case"phase":z.indexOf(n.phase)<0&&console.error(y(v,n.name,'"phase"',"either "+z.join(", "),'"'+String(n.phase)+'"'));break;case"fn":"function"!=typeof n.fn&&console.error(y(v,n.name,'"fn"','"function"','"'+String(n.fn)+'"'));break;case"effect":null!=n.effect&&"function"!=typeof n.effect&&console.error(y(v,n.name,'"effect"','"function"','"'+String(n.fn)+'"'));break;case"requires":null==n.requires||Array.isArray(n.requires)||console.error(y(v,n.name,'"requires"','"array"','"'+String(n.requires)+'"'));break;case"requiresIfExists":Array.isArray(n.requiresIfExists)||console.error(y(v,n.name,'"requiresIfExists"','"array"','"'+String(n.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+n.name+'" modifier, valid properties are '+Z.map(function(t){return'"'+t+'"'}).join(", ")+'; but "'+t+'" was provided.')}n.requires&&n.requires.forEach(function(e){null==r.find(function(t){return t.name===e})&&console.error(y(X,String(n.name),e,e))})})}),R(l.options.placement)!==L||l.orderedModifiers.find(function(t){return"flip"===t.name})||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" ")),w(a));return[t.marginTop,t.marginRight,t.marginBottom,t.marginLeft].some(function(t){return parseFloat(t)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" ")),l.orderedModifiers.forEach(function(t){var e=t.name,n=t.options,t=t.effect;"function"==typeof t&&(t=t({state:l,name:e,instance:h,options:void 0===n?{}:n}),u.push(t||function(){}))}),h.update()},forceUpdate:function(){if(!c){var t=l.elements,e=t.reference,t=t.popper;if(at(e,t)){l.rects={reference:C(e,M(t),"fixed"===l.options.strategy),popper:D(t)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(t){return l.modifiersData[t.name]=Object.assign({},t.data)});for(var n,r,i,o=0,s=0;s<l.orderedModifiers.length;s++){if(100<(o+=1)){console.error("Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.");break}!0===l.reset?(l.reset=!1,s=-1):(n=(i=l.orderedModifiers[s]).fn,r=i.options,i=i.name,"function"==typeof n&&(l=n({state:l,options:void 0===r?{}:r,name:i,instance:h})||l))}}else console.error(ot)}},update:(n=function(){return new Promise(function(t){h.forceUpdate(),t(l)})},function(){return r=r||new Promise(function(t){Promise.resolve().then(function(){r=void 0,t(n())})})}),destroy:function(){f(),c=!0}};return at(s,a)?h.setOptions(e).then(function(t){!c&&e.onFirstUpdate&&e.onFirstUpdate(t)}):console.error(ot),h;function f(){u.forEach(function(t){return t()}),u=[]}}}var S={passive:!0};var n={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,r=(t=t.options).scroll,i=void 0===r||r,o=void 0===(r=t.resize)||r,s=x(e.elements.popper),a=[].concat(e.scrollParents.reference,e.scrollParents.popper);return i&&a.forEach(function(t){t.addEventListener("scroll",n.update,S)}),o&&s.addEventListener("resize",n.update,S),function(){i&&a.forEach(function(t){t.removeEventListener("scroll",n.update,S)}),o&&s.removeEventListener("resize",n.update,S)}},data:{}};var r={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,t=t.name;e.modifiersData[t]=et({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},lt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ut(t){var e,n,r,i=t.popper,o=t.popperRect,s=t.placement,a=t.variation,l=t.offsets,u=t.position,c=t.gpuAcceleration,h=t.adaptive,f=t.roundOffsets,t=t.isFixed,p=l.x,p=void 0===p?0:p,d=l.y,d=void 0===d?0:d,m="function"==typeof f?f({x:p,y:d}):{x:p,y:d},m=(p=m.x,d=m.y,l.hasOwnProperty("x")),l=l.hasOwnProperty("y"),g=A,y=E,v=window,i=(h&&(n="clientHeight",e="clientWidth",(r=M(i))===x(i)&&"static"!==w(r=_(i)).position&&"absolute"===u&&(n="scrollHeight",e="scrollWidth"),s!==E&&(s!==A&&s!==N||a!==k)||(y=O,d=(d-((t&&r===v&&v.visualViewport?v.visualViewport.height:r[n])-o.height))*(c?1:-1)),s!==A&&(s!==E&&s!==O||a!==k)||(g=N,p=(p-((t&&r===v&&v.visualViewport?v.visualViewport.width:r[e])-o.width))*(c?1:-1))),Object.assign({position:u},h&&lt)),t=!0===f?(s=(n={x:p,y:d}).x,n=n.y,a=window.devicePixelRatio||1,{x:b(s*a)/a||0,y:b(n*a)/a||0}):{x:p,y:d};return p=t.x,d=t.y,c?Object.assign({},i,((r={})[y]=l?"0":"",r[g]=m?"0":"",r.transform=(v.devicePixelRatio||1)<=1?"translate("+p+"px, "+d+"px)":"translate3d("+p+"px, "+d+"px, 0)",r)):Object.assign({},i,((e={})[y]=l?d+"px":"",e[g]=m?p+"px":"",e.transform="",e))}var ct={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,t=t.options,n=void 0===(n=t.gpuAcceleration)||n,r=void 0===(r=t.adaptive)||r,t=void 0===(t=t.roundOffsets)||t,i=w(e.elements.popper).transitionProperty||"",n=(r&&["transform","top","right","bottom","left"].some(function(t){return 0<=i.indexOf(t)})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',"\n\n",'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.","\n\n","We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" ")),{placement:R(e.placement),variation:I(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:n,isFixed:"fixed"===e.options.strategy});null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,ut(Object.assign({},n,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:t})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,ut(Object.assign({},n,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:t})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var ht={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var i=t.state;Object.keys(i.elements).forEach(function(t){var e=i.styles[t]||{},n=i.attributes[t]||{},r=i.elements[t];l(r)&&h(r)&&(Object.assign(r.style,e),Object.keys(n).forEach(function(t){var e=n[t];!1===e?r.removeAttribute(t):r.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var r=t.state,i={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,i.popper),r.styles=i,r.elements.arrow&&Object.assign(r.elements.arrow.style,i.arrow),function(){Object.keys(r.elements).forEach(function(t){var e=r.elements[t],n=r.attributes[t]||{},t=Object.keys((r.styles.hasOwnProperty(t)?r.styles:i)[t]).reduce(function(t,e){return t[e]="",t},{});l(e)&&h(e)&&(Object.assign(e.style,t),Object.keys(n).forEach(function(t){e.removeAttribute(t)}))})}},requires:["computeStyles"]};var ft={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var s=t.state,e=t.options,t=t.name,a=void 0===(e=e.offset)?[0,0]:e,e=U.reduce(function(t,e){var n,r,i,o;return t[e]=(e=e,n=s.rects,r=a,i=R(e),o=0<=[A,E].indexOf(i)?-1:1,e=(n="function"==typeof r?r(Object.assign({},n,{placement:e})):r)[0]||0,r=(n[1]||0)*o,0<=[A,N].indexOf(i)?{x:r,y:e}:{x:e,y:r}),t},{}),n=(r=e[s.placement]).x,r=r.y;null!=s.modifiersData.popperOffsets&&(s.modifiersData.popperOffsets.x+=n,s.modifiersData.popperOffsets.y+=r),s.modifiersData[t]=e}},pt={left:"right",right:"left",bottom:"top",top:"bottom"};function Y(t){return t.replace(/left|right|bottom|top/g,function(t){return pt[t]})}var dt={start:"end",end:"start"};function mt(t){return t.replace(/start|end/g,function(t){return dt[t]})}var gt={name:"flip",enabled:!0,phase:"main",fn:function(t){var h=t.state,e=t.options,t=t.name;if(!h.modifiersData[t]._skip){for(var n=e.mainAxis,r=void 0===n||n,n=e.altAxis,i=void 0===n||n,n=e.fallbackPlacements,f=e.padding,p=e.boundary,d=e.rootBoundary,o=e.altBoundary,s=e.flipVariations,m=void 0===s||s,g=e.allowedAutoPlacements,s=h.options.placement,e=R(s),n=n||(e===s||!m?[Y(s)]:function(t){if(R(t)===L)return[];var e=Y(t);return[mt(t),e,mt(e)]}(s)),a=[s].concat(n).reduce(function(t,e){return t.concat(R(e)===L?(n=h,r=(t=t=void 0===(t={placement:e,boundary:p,rootBoundary:d,padding:f,flipVariations:m,allowedAutoPlacements:g})?{}:t).placement,i=t.boundary,o=t.rootBoundary,s=t.padding,a=t.flipVariations,l=void 0===(t=t.allowedAutoPlacements)?U:t,u=I(r),t=u?a?$:$.filter(function(t){return I(t)===u}):j,0===(r=t.filter(function(t){return 0<=l.indexOf(t)})).length&&(r=t,console.error(["Popper: The `allowedAutoPlacements` option did not allow any","placements. Ensure the `placement` option matches the variation","of the allowed placements.",'For example, "auto" cannot be used to allow "bottom-start".','Use "auto-start" instead.'].join(" "))),c=r.reduce(function(t,e){return t[e]=H(n,{placement:e,boundary:i,rootBoundary:o,padding:s})[R(e)],t},{}),Object.keys(c).sort(function(t,e){return c[t]-c[e]})):e);var n,r,i,o,s,a,l,u,c},[]),l=h.rects.reference,u=h.rects.popper,c=new Map,y=!0,v=a[0],x=0;x<a.length;x++){var b=a[x],_=R(b),w=I(b)===B,k=0<=[E,O].indexOf(_),S=k?"width":"height",C=H(h,{placement:b,boundary:p,rootBoundary:d,altBoundary:o,padding:f}),k=k?w?N:A:w?O:E,w=(l[S]>u[S]&&(k=Y(k)),Y(k)),S=[];if(r&&S.push(C[_]<=0),i&&S.push(C[k]<=0,C[w]<=0),S.every(function(t){return t})){v=b,y=!1;break}c.set(b,S)}if(y)for(var P=m?3:1;0<P;P--)if("break"===function(e){var t=a.find(function(t){t=c.get(t);if(t)return t.slice(0,e).every(function(t){return t})});if(t)return v=t,"break"}(P))break;h.placement!==v&&(h.modifiersData[t]._skip=!0,h.placement=v,h.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function F(t,e,n){return P(t,T(e,n))}var yt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e,n,r,i,o,s,a,l,u,c=t.state,h=t.options,t=t.name,f=void 0===(f=h.mainAxis)||f,p=void 0!==(p=h.altAxis)&&p,d=h.boundary,m=h.rootBoundary,g=h.altBoundary,y=h.padding,v=void 0===(v=h.tether)||v,h=void 0===(h=h.tetherOffset)?0:h,d=H(c,{boundary:d,rootBoundary:m,padding:y,altBoundary:g}),m=R(c.placement),g=!(y=I(c.placement)),x=tt(m),b="x"===x?"y":"x",_=c.modifiersData.popperOffsets,w=c.rects.reference,k=c.rects.popper,h="number"==typeof(h="function"==typeof h?h(Object.assign({},c.rects,{placement:c.placement})):h)?{mainAxis:h,altAxis:h}:Object.assign({mainAxis:0,altAxis:0},h),S=c.modifiersData.offset?c.modifiersData.offset[c.placement]:null,C={x:0,y:0};_&&(f&&(f="y"===x?"height":"width",s=(a=_[x])+d[n="y"===x?E:A],l=a-d[u="y"===x?O:N],e=v?-k[f]/2:0,i=(y===B?w:k)[f],y=y===B?-k[f]:-w[f],o=c.elements.arrow,o=v&&o?D(o):{width:0,height:0},n=(r=c.modifiersData["arrow#persistent"]?c.modifiersData["arrow#persistent"].padding:nt())[n],r=r[u],u=F(0,w[f],o[f]),o=g?w[f]/2-e-u-n-h.mainAxis:i-u-n-h.mainAxis,i=g?-w[f]/2+e+u+r+h.mainAxis:y+u+r+h.mainAxis,g=(n=c.elements.arrow&&M(c.elements.arrow))?"y"===x?n.clientTop||0:n.clientLeft||0:0,y=a+i-(e=null!=(f=null==S?void 0:S[x])?f:0),u=F(v?T(s,a+o-e-g):s,a,v?P(l,y):l),_[x]=u,C[x]=u-a),p&&(r="y"==b?"height":"width",i=(n=_[b])+d["x"===x?E:A],f=n-d["x"===x?O:N],o=-1!==[E,A].indexOf(m),g=null!=(e=null==S?void 0:S[b])?e:0,s=o?i:n-w[r]-k[r]-g+h.altAxis,y=o?n+w[r]+k[r]-g-h.altAxis:f,a=v&&o?(l=F(l=s,n,u=y),u<l?u:l):F(v?s:i,n,v?y:f),_[b]=a,C[b]=a-n),c.modifiersData[t]=C)},requiresIfExists:["offset"]};var vt={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n,r,i,o=t.state,s=t.name,t=t.options,a=o.elements.arrow,l=o.modifiersData.popperOffsets,u=tt(c=R(o.placement)),c=0<=[A,N].indexOf(c)?"height":"width";a&&l&&(t=t.padding,n=o,n=rt("number"!=typeof(t="function"==typeof t?t(Object.assign({},n.rects,{placement:n.placement})):t)?t:it(t,j)),t=D(a),i="y"===u?E:A,r="y"===u?O:N,e=o.rects.reference[c]+o.rects.reference[u]-l[u]-o.rects.popper[c],l=l[u]-o.rects.reference[u],a=(a=M(a))?"y"===u?a.clientHeight||0:a.clientWidth||0:0,i=n[i],n=a-t[c]-n[r],i=F(i,r=a/2-t[c]/2+(e/2-l/2),n),o.modifiersData[s]=((a={})[u]=i,a.centerOffset=i-r,a))},effect:function(t){var e=t.state;null==(t=void 0===(t=t.options.element)?"[data-popper-arrow]":t)||"string"==typeof t&&!(t=e.elements.popper.querySelector(t))||(l(t)||console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).',"To use an SVG arrow, wrap it in an HTMLElement that will be used as","the arrow."].join(" ")),a(e.elements.popper,t)?e.elements.arrow=t:console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper',"element."].join(" ")))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function xt(t,e,n){return{top:t.top-e.height-(n=void 0===n?{x:0,y:0}:n).y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function bt(e){return[E,N,O,A].some(function(t){return 0<=e[t]})}var _t={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,t=t.name,n=e.rects.reference,r=e.rects.popper,i=e.modifiersData.preventOverflow,o=H(e,{elementContext:"reference"}),s=H(e,{altBoundary:!0}),o=xt(o,n),n=xt(s,r,i),s=bt(o),r=bt(n);e.modifiersData[t]={referenceClippingOffsets:o,popperEscapeOffsets:n,isReferenceHidden:s,hasPopperEscaped:r},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":s,"data-popper-escaped":r})}},wt=e({defaultModifiers:[n,r,ct,ht]}),kt=[n,r,ct,ht,ft,gt,yt,vt,_t],St=e({defaultModifiers:kt});t.applyStyles=ht,t.arrow=vt,t.computeStyles=ct,t.createPopper=St,t.createPopperLite=wt,t.defaultModifiers=kt,t.detectOverflow=H,t.eventListeners=n,t.flip=gt,t.hide=_t,t.offset=ft,t.popperGenerator=e,t.popperOffsets=r,t.preventOverflow=yt,Object.defineProperty(t,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Popper={}),t=this,e=function(){"use strict";var B;function p(){return B.apply(null,arguments)}function s(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function R(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function f(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function I(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;for(var e in t)if(f(t,e))return;return 1}function a(t){return void 0===t}function l(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function H(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function Y(t,e){for(var n=[],r=t.length,i=0;i<r;++i)n.push(e(t[i],i));return n}function F(t,e){for(var n in e)f(e,n)&&(t[n]=e[n]);return f(e,"toString")&&(t.toString=e.toString),f(e,"valueOf")&&(t.valueOf=e.valueOf),t}function u(t,e,n,r){return Oe(t,e,n,r,!0).utc()}function d(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function W(t){if(null==t._isValid){var e=d(t),n=V.call(e.parsedDateParts,function(t){return null!=t}),n=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidEra&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&n);if(t._strict&&(n=n&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return n;t._isValid=n}return t._isValid}function q(t){var e=u(NaN);return null!=t?F(d(e),t):d(e).userInvalidated=!0,e}var V=Array.prototype.some||function(t){for(var e=Object(this),n=e.length>>>0,r=0;r<n;r++)if(r in e&&t.call(this,e[r],r,e))return!0;return!1},$=p.momentProperties=[],U=!1;function z(t,e){var n,r,i,o=$.length;if(a(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),a(e._i)||(t._i=e._i),a(e._f)||(t._f=e._f),a(e._l)||(t._l=e._l),a(e._strict)||(t._strict=e._strict),a(e._tzm)||(t._tzm=e._tzm),a(e._isUTC)||(t._isUTC=e._isUTC),a(e._offset)||(t._offset=e._offset),a(e._pf)||(t._pf=d(e)),a(e._locale)||(t._locale=e._locale),0<o)for(n=0;n<o;n++)a(i=e[r=$[n]])||(t[r]=i);return t}function G(t){z(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===U&&(U=!0,p.updateOffset(this),U=!1)}function c(t){return t instanceof G||null!=t&&null!=t._isAMomentObject}function X(t){!1===p.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function t(o,s){var a=!0;return F(function(){if(null!=p.deprecationHandler&&p.deprecationHandler(null,o),a){for(var t,e,n=[],r=arguments.length,i=0;i<r;i++){if(t="","object"==typeof arguments[i]){for(e in t+="\n["+i+"] ",arguments[0])f(arguments[0],e)&&(t+=e+": "+arguments[0][e]+", ");t=t.slice(0,-2)}else t=arguments[i];n.push(t)}X(o+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),a=!1}return s.apply(this,arguments)},s)}var Z={};function K(t,e){null!=p.deprecationHandler&&p.deprecationHandler(t,e),Z[t]||(X(e),Z[t]=!0)}function h(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function J(t,e){var n,r=F({},t);for(n in e)f(e,n)&&(R(t[n])&&R(e[n])?(r[n]={},F(r[n],t[n]),F(r[n],e[n])):null!=e[n]?r[n]=e[n]:delete r[n]);for(n in t)f(t,n)&&!f(e,n)&&R(t[n])&&(r[n]=F({},r[n]));return r}function Q(t){null!=t&&this.set(t)}p.suppressDeprecationWarnings=!1,p.deprecationHandler=null;var tt=Object.keys||function(t){var e,n=[];for(e in t)f(t,e)&&n.push(e);return n};function o(t,e,n){var r=""+Math.abs(t);return(0<=t?n?"+":"":"-")+Math.pow(10,Math.max(0,e-r.length)).toString().substr(1)+r}var et=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,nt=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,rt={},it={};function r(t,e,n,r){var i="string"==typeof r?function(){return this[r]()}:r;t&&(it[t]=i),e&&(it[e[0]]=function(){return o(i.apply(this,arguments),e[1],e[2])}),n&&(it[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),t)})}function ot(t,e){return t.isValid()?(e=st(e,t.localeData()),rt[e]=rt[e]||function(r){for(var t,i=r.match(et),e=0,o=i.length;e<o;e++)it[i[e]]?i[e]=it[i[e]]:i[e]=(t=i[e]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var e="",n=0;n<o;n++)e+=h(i[n])?i[n].call(t,r):i[n];return e}}(e),rt[e](t)):t.localeData().invalidDate()}function st(t,e){var n=5;function r(t){return e.longDateFormat(t)||t}for(nt.lastIndex=0;0<=n&&nt.test(t);)t=t.replace(nt,r),nt.lastIndex=0,--n;return t}var at={};function e(t,e){var n=t.toLowerCase();at[n]=at[n+"s"]=at[e]=t}function m(t){return"string"==typeof t?at[t]||at[t.toLowerCase()]:void 0}function lt(t){var e,n,r={};for(n in t)f(t,n)&&(e=m(n))&&(r[e]=t[n]);return r}var ut={};function n(t,e){ut[t]=e}function ct(t){return t%4==0&&t%100!=0||t%400==0}function g(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function y(t){var t=+t,e=0;return e=0!=t&&isFinite(t)?g(t):e}function ht(e,n){return function(t){return null!=t?(pt(this,e,t),p.updateOffset(this,n),this):ft(this,e)}}function ft(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function pt(t,e,n){t.isValid()&&!isNaN(n)&&("FullYear"===e&&ct(t.year())&&1===t.month()&&29===t.date()?(n=y(n),t._d["set"+(t._isUTC?"UTC":"")+e](n,t.month(),At(n,t.month()))):t._d["set"+(t._isUTC?"UTC":"")+e](n))}var i=/\d/,v=/\d\d/,dt=/\d{3}/,mt=/\d{4}/,gt=/[+-]?\d{6}/,x=/\d\d?/,yt=/\d\d\d\d?/,vt=/\d\d\d\d\d\d?/,xt=/\d{1,3}/,bt=/\d{1,4}/,_t=/[+-]?\d{1,6}/,wt=/\d+/,kt=/[+-]?\d+/,St=/Z|[+-]\d\d:?\d\d/gi,Ct=/Z|[+-]\d\d(?::?\d\d)?/gi,b=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;function _(t,n,r){Tt[t]=h(n)?n:function(t,e){return t&&r?r:n}}function Pt(t,e){return f(Tt,t)?Tt[t](e._strict,e._locale):new RegExp(w(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,n,r,i){return e||n||r||i})))}function w(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var Tt={},Dt={};function k(t,n){var e,r,i=n;for("string"==typeof t&&(t=[t]),l(n)&&(i=function(t,e){e[n]=y(t)}),r=t.length,e=0;e<r;e++)Dt[t[e]]=i}function Mt(t,i){k(t,function(t,e,n,r){n._w=n._w||{},i(t,n._w,n,r)})}var S,C=0,P=1,T=2,D=3,M=4,E=5,Et=6,Ot=7,Nt=8;function At(t,e){if(isNaN(t)||isNaN(e))return NaN;var n=(e%(n=12)+n)%n;return t+=(e-n)/12,1==n?ct(t)?29:28:31-n%7%2}S=Array.prototype.indexOf||function(t){for(var e=0;e<this.length;++e)if(this[e]===t)return e;return-1},r("M",["MM",2],"Mo",function(){return this.month()+1}),r("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),r("MMMM",0,0,function(t){return this.localeData().months(this,t)}),e("month","M"),n("month",8),_("M",x),_("MM",x,v),_("MMM",function(t,e){return e.monthsShortRegex(t)}),_("MMMM",function(t,e){return e.monthsRegex(t)}),k(["M","MM"],function(t,e){e[P]=y(t)-1}),k(["MMM","MMMM"],function(t,e,n,r){r=n._locale.monthsParse(t,r,n._strict);null!=r?e[P]=r:d(n).invalidMonth=t});var Lt="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),jt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Bt=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Rt=b,It=b;function Ht(t,e){var n;if(t.isValid()){if("string"==typeof e)if(/^\d+$/.test(e))e=y(e);else if(!l(e=t.localeData().monthsParse(e)))return;n=Math.min(t.date(),At(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,n)}}function Yt(t){return null!=t?(Ht(this,t),p.updateOffset(this,!0),this):ft(this,"Month")}function Ft(){function t(t,e){return e.length-t.length}for(var e,n=[],r=[],i=[],o=0;o<12;o++)e=u([2e3,o]),n.push(this.monthsShort(e,"")),r.push(this.months(e,"")),i.push(this.months(e,"")),i.push(this.monthsShort(e,""));for(n.sort(t),r.sort(t),i.sort(t),o=0;o<12;o++)n[o]=w(n[o]),r[o]=w(r[o]);for(o=0;o<24;o++)i[o]=w(i[o]);this._monthsRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function Wt(t){return ct(t)?366:365}r("Y",0,0,function(){var t=this.year();return t<=9999?o(t,4):"+"+t}),r(0,["YY",2],0,function(){return this.year()%100}),r(0,["YYYY",4],0,"year"),r(0,["YYYYY",5],0,"year"),r(0,["YYYYYY",6,!0],0,"year"),e("year","y"),n("year",1),_("Y",kt),_("YY",x,v),_("YYYY",bt,mt),_("YYYYY",_t,gt),_("YYYYYY",_t,gt),k(["YYYYY","YYYYYY"],C),k("YYYY",function(t,e){e[C]=2===t.length?p.parseTwoDigitYear(t):y(t)}),k("YY",function(t,e){e[C]=p.parseTwoDigitYear(t)}),k("Y",function(t,e){e[C]=parseInt(t,10)}),p.parseTwoDigitYear=function(t){return y(t)+(68<y(t)?1900:2e3)};var qt=ht("FullYear",!0);function Vt(t,e,n,r,i,o,s){var a;return t<100&&0<=t?(a=new Date(t+400,e,n,r,i,o,s),isFinite(a.getFullYear())&&a.setFullYear(t)):a=new Date(t,e,n,r,i,o,s),a}function $t(t){var e;return t<100&&0<=t?((e=Array.prototype.slice.call(arguments))[0]=t+400,e=new Date(Date.UTC.apply(null,e)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function Ut(t,e,n){n=7+e-n;return n-(7+$t(t,0,n).getUTCDay()-e)%7-1}function zt(t,e,n,r,i){var o,e=1+7*(e-1)+(7+n-r)%7+Ut(t,r,i),n=e<=0?Wt(o=t-1)+e:e>Wt(t)?(o=t+1,e-Wt(t)):(o=t,e);return{year:o,dayOfYear:n}}function Gt(t,e,n){var r,i,o=Ut(t.year(),e,n),o=Math.floor((t.dayOfYear()-o-1)/7)+1;return o<1?r=o+O(i=t.year()-1,e,n):o>O(t.year(),e,n)?(r=o-O(t.year(),e,n),i=t.year()+1):(i=t.year(),r=o),{week:r,year:i}}function O(t,e,n){var r=Ut(t,e,n),e=Ut(t+1,e,n);return(Wt(t)-r+e)/7}r("w",["ww",2],"wo","week"),r("W",["WW",2],"Wo","isoWeek"),e("week","w"),e("isoWeek","W"),n("week",5),n("isoWeek",5),_("w",x),_("ww",x,v),_("W",x),_("WW",x,v),Mt(["w","ww","W","WW"],function(t,e,n,r){e[r.substr(0,1)]=y(t)});function Xt(t,e){return t.slice(e,7).concat(t.slice(0,e))}r("d",0,"do","day"),r("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),r("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),r("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),r("e",0,0,"weekday"),r("E",0,0,"isoWeekday"),e("day","d"),e("weekday","e"),e("isoWeekday","E"),n("day",11),n("weekday",11),n("isoWeekday",11),_("d",x),_("e",x),_("E",x),_("dd",function(t,e){return e.weekdaysMinRegex(t)}),_("ddd",function(t,e){return e.weekdaysShortRegex(t)}),_("dddd",function(t,e){return e.weekdaysRegex(t)}),Mt(["dd","ddd","dddd"],function(t,e,n,r){r=n._locale.weekdaysParse(t,r,n._strict);null!=r?e.d=r:d(n).invalidWeekday=t}),Mt(["d","e","E"],function(t,e,n,r){e[r]=y(t)});var Zt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Kt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Jt="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Qt=b,te=b,ee=b;function ne(){function t(t,e){return e.length-t.length}for(var e,n,r,i=[],o=[],s=[],a=[],l=0;l<7;l++)r=u([2e3,1]).day(l),e=w(this.weekdaysMin(r,"")),n=w(this.weekdaysShort(r,"")),r=w(this.weekdays(r,"")),i.push(e),o.push(n),s.push(r),a.push(e),a.push(n),a.push(r);i.sort(t),o.sort(t),s.sort(t),a.sort(t),this._weekdaysRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function re(){return this.hours()%12||12}function ie(t,e){r(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function oe(t,e){return e._meridiemParse}r("H",["HH",2],0,"hour"),r("h",["hh",2],0,re),r("k",["kk",2],0,function(){return this.hours()||24}),r("hmm",0,0,function(){return""+re.apply(this)+o(this.minutes(),2)}),r("hmmss",0,0,function(){return""+re.apply(this)+o(this.minutes(),2)+o(this.seconds(),2)}),r("Hmm",0,0,function(){return""+this.hours()+o(this.minutes(),2)}),r("Hmmss",0,0,function(){return""+this.hours()+o(this.minutes(),2)+o(this.seconds(),2)}),ie("a",!0),ie("A",!1),e("hour","h"),n("hour",13),_("a",oe),_("A",oe),_("H",x),_("h",x),_("k",x),_("HH",x,v),_("hh",x,v),_("kk",x,v),_("hmm",yt),_("hmmss",vt),_("Hmm",yt),_("Hmmss",vt),k(["H","HH"],D),k(["k","kk"],function(t,e,n){t=y(t);e[D]=24===t?0:t}),k(["a","A"],function(t,e,n){n._isPm=n._locale.isPM(t),n._meridiem=t}),k(["h","hh"],function(t,e,n){e[D]=y(t),d(n).bigHour=!0}),k("hmm",function(t,e,n){var r=t.length-2;e[D]=y(t.substr(0,r)),e[M]=y(t.substr(r)),d(n).bigHour=!0}),k("hmmss",function(t,e,n){var r=t.length-4,i=t.length-2;e[D]=y(t.substr(0,r)),e[M]=y(t.substr(r,2)),e[E]=y(t.substr(i)),d(n).bigHour=!0}),k("Hmm",function(t,e,n){var r=t.length-2;e[D]=y(t.substr(0,r)),e[M]=y(t.substr(r))}),k("Hmmss",function(t,e,n){var r=t.length-4,i=t.length-2;e[D]=y(t.substr(0,r)),e[M]=y(t.substr(r,2)),e[E]=y(t.substr(i))});b=ht("Hours",!0);var se,ae={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Lt,monthsShort:jt,week:{dow:0,doy:6},weekdays:Zt,weekdaysMin:Jt,weekdaysShort:Kt,meridiemParse:/[ap]\.?m?\.?/i},N={},le={};function ue(t){return t&&t.toLowerCase().replace("_","-")}function ce(t){for(var e,n,r,i,o=0;o<t.length;){for(e=(i=ue(t[o]).split("-")).length,n=(n=ue(t[o+1]))?n.split("-"):null;0<e;){if(r=he(i.slice(0,e).join("-")))return r;if(n&&n.length>=e&&function(t,e){for(var n=Math.min(t.length,e.length),r=0;r<n;r+=1)if(t[r]!==e[r])return r;return n}(i,n)>=e-1)break;e--}o++}return se}function he(e){var t;if(void 0===N[e]&&"undefined"!=typeof module&&module&&module.exports&&null!=e.match("^[^/\\\\]*$"))try{t=se._abbr,require("./locale/"+e),fe(t)}catch(t){N[e]=null}return N[e]}function fe(t,e){return t&&((e=a(e)?de(t):pe(t,e))?se=e:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),se._abbr}function pe(t,e){if(null===e)return delete N[t],null;var n,r=ae;if(e.abbr=t,null!=N[t])K("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=N[t]._config;else if(null!=e.parentLocale)if(null!=N[e.parentLocale])r=N[e.parentLocale]._config;else{if(null==(n=he(e.parentLocale)))return le[e.parentLocale]||(le[e.parentLocale]=[]),le[e.parentLocale].push({name:t,config:e}),null;r=n._config}return N[t]=new Q(J(r,e)),le[t]&&le[t].forEach(function(t){pe(t.name,t.config)}),fe(t),N[t]}function de(t){var e;if(!(t=t&&t._locale&&t._locale._abbr?t._locale._abbr:t))return se;if(!s(t)){if(e=he(t))return e;t=[t]}return ce(t)}function me(t){var e=t._a;return e&&-2===d(t).overflow&&(e=e[P]<0||11<e[P]?P:e[T]<1||e[T]>At(e[C],e[P])?T:e[D]<0||24<e[D]||24===e[D]&&(0!==e[M]||0!==e[E]||0!==e[Et])?D:e[M]<0||59<e[M]?M:e[E]<0||59<e[E]?E:e[Et]<0||999<e[Et]?Et:-1,d(t)._overflowDayOfYear&&(e<C||T<e)&&(e=T),d(t)._overflowWeeks&&-1===e&&(e=Ot),d(t)._overflowWeekday&&-1===e&&(e=Nt),d(t).overflow=e),t}var ge=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ye=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ve=/Z|[+-]\d\d(?::?\d\d)?/,xe=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],be=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],_e=/^\/?Date\((-?\d+)/i,we=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,ke={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Se(t){var e,n,r,i,o,s,a=t._i,l=ge.exec(a)||ye.exec(a),a=xe.length,u=be.length;if(l){for(d(t).iso=!0,e=0,n=a;e<n;e++)if(xe[e][1].exec(l[1])){i=xe[e][0],r=!1!==xe[e][2];break}if(null==i)t._isValid=!1;else{if(l[3]){for(e=0,n=u;e<n;e++)if(be[e][1].exec(l[3])){o=(l[2]||" ")+be[e][0];break}if(null==o)return void(t._isValid=!1)}if(r||null==o){if(l[4]){if(!ve.exec(l[4]))return void(t._isValid=!1);s="Z"}t._f=i+(o||"")+(s||""),Me(t)}else t._isValid=!1}}else t._isValid=!1}function Ce(t,e,n,r,i,o){t=[function(t){t=parseInt(t,10);{if(t<=49)return 2e3+t;if(t<=999)return 1900+t}return t}(t),jt.indexOf(e),parseInt(n,10),parseInt(r,10),parseInt(i,10)];return o&&t.push(parseInt(o,10)),t}function Pe(t){var e,n,r,i,o=we.exec(t._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));o?(e=Ce(o[4],o[3],o[2],o[5],o[6],o[7]),n=o[1],r=e,i=t,n&&Kt.indexOf(n)!==new Date(r[0],r[1],r[2]).getDay()?(d(i).weekdayMismatch=!0,i._isValid=!1):(t._a=e,t._tzm=(n=o[8],r=o[9],i=o[10],n?ke[n]:r?0:60*(((n=parseInt(i,10))-(r=n%100))/100)+r),t._d=$t.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),d(t).rfc2822=!0)):t._isValid=!1}function Te(t,e,n){return null!=t?t:null!=e?e:n}function De(t){var e,n,r,i,o,s,a,l,u,c,h,f=[];if(!t._d){for(r=t,i=new Date(p.now()),n=r._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],t._w&&null==t._a[T]&&null==t._a[P]&&(null!=(i=(r=t)._w).GG||null!=i.W||null!=i.E?(l=1,u=4,o=Te(i.GG,r._a[C],Gt(A(),1,4).year),s=Te(i.W,1),((a=Te(i.E,1))<1||7<a)&&(c=!0)):(l=r._locale._week.dow,u=r._locale._week.doy,h=Gt(A(),l,u),o=Te(i.gg,r._a[C],h.year),s=Te(i.w,h.week),null!=i.d?((a=i.d)<0||6<a)&&(c=!0):null!=i.e?(a=i.e+l,(i.e<0||6<i.e)&&(c=!0)):a=l),s<1||s>O(o,l,u)?d(r)._overflowWeeks=!0:null!=c?d(r)._overflowWeekday=!0:(h=zt(o,s,a,l,u),r._a[C]=h.year,r._dayOfYear=h.dayOfYear)),null!=t._dayOfYear&&(i=Te(t._a[C],n[C]),(t._dayOfYear>Wt(i)||0===t._dayOfYear)&&(d(t)._overflowDayOfYear=!0),c=$t(i,0,t._dayOfYear),t._a[P]=c.getUTCMonth(),t._a[T]=c.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=f[e]=n[e];for(;e<7;e++)t._a[e]=f[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[D]&&0===t._a[M]&&0===t._a[E]&&0===t._a[Et]&&(t._nextDay=!0,t._a[D]=0),t._d=(t._useUTC?$t:Vt).apply(null,f),o=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[D]=24),t._w&&void 0!==t._w.d&&t._w.d!==o&&(d(t).weekdayMismatch=!0)}}function Me(t){if(t._f===p.ISO_8601)Se(t);else if(t._f===p.RFC_2822)Pe(t);else{t._a=[],d(t).empty=!0;for(var e,n,r,i,o,s=""+t._i,a=s.length,l=0,u=st(t._f,t._locale).match(et)||[],c=u.length,h=0;h<c;h++)n=u[h],(e=(s.match(Pt(n,t))||[])[0])&&(0<(r=s.substr(0,s.indexOf(e))).length&&d(t).unusedInput.push(r),s=s.slice(s.indexOf(e)+e.length),l+=e.length),it[n]?(e?d(t).empty=!1:d(t).unusedTokens.push(n),r=n,o=t,null!=(i=e)&&f(Dt,r)&&Dt[r](i,o._a,o,r)):t._strict&&!e&&d(t).unusedTokens.push(n);d(t).charsLeftOver=a-l,0<s.length&&d(t).unusedInput.push(s),t._a[D]<=12&&!0===d(t).bigHour&&0<t._a[D]&&(d(t).bigHour=void 0),d(t).parsedDateParts=t._a.slice(0),d(t).meridiem=t._meridiem,t._a[D]=function(t,e,n){if(null==n)return e;return null!=t.meridiemHour?t.meridiemHour(e,n):null!=t.isPM?((t=t.isPM(n))&&e<12&&(e+=12),e=t||12!==e?e:0):e}(t._locale,t._a[D],t._meridiem),null!==(a=d(t).era)&&(t._a[C]=t._locale.erasConvertYear(a,t._a[C])),De(t),me(t)}}function Ee(t){var e,n,r,i=t._i,o=t._f;if(t._locale=t._locale||de(t._l),null===i||void 0===o&&""===i)return q({nullInput:!0});if("string"==typeof i&&(t._i=i=t._locale.preparse(i)),c(i))return new G(me(i));if(H(i))t._d=i;else if(s(o))!function(t){var e,n,r,i,o,s,a=!1,l=t._f.length;if(0===l)return d(t).invalidFormat=!0,t._d=new Date(NaN);for(i=0;i<l;i++)o=0,s=!1,e=z({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[i],Me(e),W(e)&&(s=!0),o=(o+=d(e).charsLeftOver)+10*d(e).unusedTokens.length,d(e).score=o,a?o<r&&(r=o,n=e):(null==r||o<r||s)&&(r=o,n=e,s&&(a=!0));F(t,n||e)}(t);else if(o)Me(t);else if(a(o=(i=t)._i))i._d=new Date(p.now());else H(o)?i._d=new Date(o.valueOf()):"string"==typeof o?(n=i,null!==(e=_e.exec(n._i))?n._d=new Date(+e[1]):(Se(n),!1===n._isValid&&(delete n._isValid,Pe(n),!1===n._isValid&&(delete n._isValid,n._strict?n._isValid=!1:p.createFromInputFallback(n))))):s(o)?(i._a=Y(o.slice(0),function(t){return parseInt(t,10)}),De(i)):R(o)?(e=i)._d||(r=void 0===(n=lt(e._i)).day?n.date:n.day,e._a=Y([n.year,n.month,r,n.hour,n.minute,n.second,n.millisecond],function(t){return t&&parseInt(t,10)}),De(e)):l(o)?i._d=new Date(o):p.createFromInputFallback(i);return W(t)||(t._d=null),t}function Oe(t,e,n,r,i){var o={};return!0!==e&&!1!==e||(r=e,e=void 0),!0!==n&&!1!==n||(r=n,n=void 0),(R(t)&&I(t)||s(t)&&0===t.length)&&(t=void 0),o._isAMomentObject=!0,o._useUTC=o._isUTC=i,o._l=n,o._i=t,o._f=e,o._strict=r,(i=new G(me(Ee(i=o))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function A(t,e,n,r){return Oe(t,e,n,r,!1)}p.createFromInputFallback=t("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),p.ISO_8601=function(){},p.RFC_2822=function(){};yt=t("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=A.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:q()}),vt=t("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=A.apply(null,arguments);return this.isValid()&&t.isValid()?this<t?this:t:q()});function Ne(t,e){var n,r;if(!(e=1===e.length&&s(e[0])?e[0]:e).length)return A();for(n=e[0],r=1;r<e.length;++r)e[r].isValid()&&!e[r][t](n)||(n=e[r]);return n}var Ae=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Le(t){var t=lt(t),e=t.year||0,n=t.quarter||0,r=t.month||0,i=t.week||t.isoWeek||0,o=t.day||0,s=t.hour||0,a=t.minute||0,l=t.second||0,u=t.millisecond||0;this._isValid=function(t){var e,n,r=!1,i=Ae.length;for(e in t)if(f(t,e)&&(-1===S.call(Ae,e)||null!=t[e]&&isNaN(t[e])))return!1;for(n=0;n<i;++n)if(t[Ae[n]]){if(r)return!1;parseFloat(t[Ae[n]])!==y(t[Ae[n]])&&(r=!0)}return!0}(t),this._milliseconds=+u+1e3*l+6e4*a+1e3*s*60*60,this._days=+o+7*i,this._months=+r+3*n+12*e,this._data={},this._locale=de(),this._bubble()}function je(t){return t instanceof Le}function Be(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Re(t,n){r(t,0,0,function(){var t=this.utcOffset(),e="+";return t<0&&(t=-t,e="-"),e+o(~~(t/60),2)+n+o(~~t%60,2)})}Re("Z",":"),Re("ZZ",""),_("Z",Ct),_("ZZ",Ct),k(["Z","ZZ"],function(t,e,n){n._useUTC=!0,n._tzm=He(Ct,t)});var Ie=/([\+\-]|\d\d)/gi;function He(t,e){var e=(e||"").match(t);return null===e?null:0===(e=60*(t=((e[e.length-1]||[])+"").match(Ie)||["-",0,0])[1]+y(t[2]))?0:"+"===t[0]?e:-e}function Ye(t,e){var n;return e._isUTC?(e=e.clone(),n=(c(t)||H(t)?t:A(t)).valueOf()-e.valueOf(),e._d.setTime(e._d.valueOf()+n),p.updateOffset(e,!1),e):A(t).local()}function Fe(t){return-Math.round(t._d.getTimezoneOffset())}function We(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}p.updateOffset=function(){};var qe=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Ve=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function L(t,e){var n,r=t,i=null;return je(t)?r={ms:t._milliseconds,d:t._days,M:t._months}:l(t)||!isNaN(+t)?(r={},e?r[e]=+t:r.milliseconds=+t):(i=qe.exec(t))?(n="-"===i[1]?-1:1,r={y:0,d:y(i[T])*n,h:y(i[D])*n,m:y(i[M])*n,s:y(i[E])*n,ms:y(Be(1e3*i[Et]))*n}):(i=Ve.exec(t))?(n="-"===i[1]?-1:1,r={y:$e(i[2],n),M:$e(i[3],n),w:$e(i[4],n),d:$e(i[5],n),h:$e(i[6],n),m:$e(i[7],n),s:$e(i[8],n)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(e=function(t,e){var n;if(!t.isValid()||!e.isValid())return{milliseconds:0,months:0};e=Ye(e,t),t.isBefore(e)?n=Ue(t,e):((n=Ue(e,t)).milliseconds=-n.milliseconds,n.months=-n.months);return n}(A(r.from),A(r.to)),(r={}).ms=e.milliseconds,r.M=e.months),i=new Le(r),je(t)&&f(t,"_locale")&&(i._locale=t._locale),je(t)&&f(t,"_isValid")&&(i._isValid=t._isValid),i}function $e(t,e){t=t&&parseFloat(t.replace(",","."));return(isNaN(t)?0:t)*e}function Ue(t,e){var n={};return n.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(n.months,"M").isAfter(e)&&--n.months,n.milliseconds=+e-+t.clone().add(n.months,"M"),n}function ze(r,i){return function(t,e){var n;return null===e||isNaN(+e)||(K(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),n=t,t=e,e=n),Ge(this,L(t,e),r),this}}function Ge(t,e,n,r){var i=e._milliseconds,o=Be(e._days),e=Be(e._months);t.isValid()&&(r=null==r||r,e&&Ht(t,ft(t,"Month")+e*n),o&&pt(t,"Date",ft(t,"Date")+o*n),i&&t._d.setTime(t._d.valueOf()+i*n),r&&p.updateOffset(t,o||e))}L.fn=Le.prototype,L.invalid=function(){return L(NaN)};Lt=ze(1,"add"),Zt=ze(-1,"subtract");function Xe(t){return"string"==typeof t||t instanceof String}function Ze(t){return c(t)||H(t)||Xe(t)||l(t)||function(e){var t=s(e),n=!1;t&&(n=0===e.filter(function(t){return!l(t)&&Xe(e)}).length);return t&&n}(t)||function(t){var e,n,r=R(t)&&!I(t),i=!1,o=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],s=o.length;for(e=0;e<s;e+=1)n=o[e],i=i||f(t,n);return r&&i}(t)||null==t}function Ke(t,e){if(t.date()<e.date())return-Ke(e,t);var n=12*(e.year()-t.year())+(e.month()-t.month()),r=t.clone().add(n,"months"),e=e-r<0?(e-r)/(r-t.clone().add(n-1,"months")):(e-r)/(t.clone().add(1+n,"months")-r);return-(n+e)||0}function Je(t){return void 0===t?this._locale._abbr:(null!=(t=de(t))&&(this._locale=t),this)}p.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",p.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";Jt=t("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});function Qe(){return this._locale}var tn=126227808e5;function en(t,e){return(t%e+e)%e}function nn(t,e,n){return t<100&&0<=t?new Date(t+400,e,n)-tn:new Date(t,e,n).valueOf()}function rn(t,e,n){return t<100&&0<=t?Date.UTC(t+400,e,n)-tn:Date.UTC(t,e,n)}function on(t,e){return e.erasAbbrRegex(t)}function sn(){for(var t=[],e=[],n=[],r=[],i=this.eras(),o=0,s=i.length;o<s;++o)e.push(w(i[o].name)),t.push(w(i[o].abbr)),n.push(w(i[o].narrow)),r.push(w(i[o].name)),r.push(w(i[o].abbr)),r.push(w(i[o].narrow));this._erasRegex=new RegExp("^("+r.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+e.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+t.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+n.join("|")+")","i")}function an(t,e){r(0,[t,t.length],0,e)}function ln(t,e,n,r,i){var o;return null==t?Gt(this,r,i).year:(o=O(t,r,i),function(t,e,n,r,i){t=zt(t,e,n,r,i),e=$t(t.year,0,t.dayOfYear);return this.year(e.getUTCFullYear()),this.month(e.getUTCMonth()),this.date(e.getUTCDate()),this}.call(this,t,e=o<e?o:e,n,r,i))}r("N",0,0,"eraAbbr"),r("NN",0,0,"eraAbbr"),r("NNN",0,0,"eraAbbr"),r("NNNN",0,0,"eraName"),r("NNNNN",0,0,"eraNarrow"),r("y",["y",1],"yo","eraYear"),r("y",["yy",2],0,"eraYear"),r("y",["yyy",3],0,"eraYear"),r("y",["yyyy",4],0,"eraYear"),_("N",on),_("NN",on),_("NNN",on),_("NNNN",function(t,e){return e.erasNameRegex(t)}),_("NNNNN",function(t,e){return e.erasNarrowRegex(t)}),k(["N","NN","NNN","NNNN","NNNNN"],function(t,e,n,r){r=n._locale.erasParse(t,r,n._strict);r?d(n).era=r:d(n).invalidEra=t}),_("y",wt),_("yy",wt),_("yyy",wt),_("yyyy",wt),_("yo",function(t,e){return e._eraYearOrdinalRegex||wt}),k(["y","yy","yyy","yyyy"],C),k(["yo"],function(t,e,n,r){var i;n._locale._eraYearOrdinalRegex&&(i=t.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?e[C]=n._locale.eraYearOrdinalParse(t,i):e[C]=parseInt(t,10)}),r(0,["gg",2],0,function(){return this.weekYear()%100}),r(0,["GG",2],0,function(){return this.isoWeekYear()%100}),an("gggg","weekYear"),an("ggggg","weekYear"),an("GGGG","isoWeekYear"),an("GGGGG","isoWeekYear"),e("weekYear","gg"),e("isoWeekYear","GG"),n("weekYear",1),n("isoWeekYear",1),_("G",kt),_("g",kt),_("GG",x,v),_("gg",x,v),_("GGGG",bt,mt),_("gggg",bt,mt),_("GGGGG",_t,gt),_("ggggg",_t,gt),Mt(["gggg","ggggg","GGGG","GGGGG"],function(t,e,n,r){e[r.substr(0,2)]=y(t)}),Mt(["gg","GG"],function(t,e,n,r){e[r]=p.parseTwoDigitYear(t)}),r("Q",0,"Qo","quarter"),e("quarter","Q"),n("quarter",7),_("Q",i),k("Q",function(t,e){e[P]=3*(y(t)-1)}),r("D",["DD",2],"Do","date"),e("date","D"),n("date",9),_("D",x),_("DD",x,v),_("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),k(["D","DD"],T),k("Do",function(t,e){e[T]=y(t.match(x)[0])});bt=ht("Date",!0);r("DDD",["DDDD",3],"DDDo","dayOfYear"),e("dayOfYear","DDD"),n("dayOfYear",4),_("DDD",xt),_("DDDD",dt),k(["DDD","DDDD"],function(t,e,n){n._dayOfYear=y(t)}),r("m",["mm",2],0,"minute"),e("minute","m"),n("minute",14),_("m",x),_("mm",x,v),k(["m","mm"],M);var un,mt=ht("Minutes",!1),_t=(r("s",["ss",2],0,"second"),e("second","s"),n("second",15),_("s",x),_("ss",x,v),k(["s","ss"],E),ht("Seconds",!1));for(r("S",0,0,function(){return~~(this.millisecond()/100)}),r(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),r(0,["SSS",3],0,"millisecond"),r(0,["SSSS",4],0,function(){return 10*this.millisecond()}),r(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),r(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),r(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),r(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),r(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),e("millisecond","ms"),n("millisecond",16),_("S",xt,i),_("SS",xt,v),_("SSS",xt,dt),un="SSSS";un.length<=9;un+="S")_(un,wt);function cn(t,e){e[Et]=y(1e3*("0."+t))}for(un="S";un.length<=9;un+="S")k(un,cn);gt=ht("Milliseconds",!1),r("z",0,0,"zoneAbbr"),r("zz",0,0,"zoneName");i=G.prototype;function hn(t){return t}i.add=Lt,i.calendar=function(t,e){1===arguments.length&&(arguments[0]?Ze(arguments[0])?(t=arguments[0],e=void 0):function(t){for(var e=R(t)&&!I(t),n=!1,r=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i=0;i<r.length;i+=1)n=n||f(t,r[i]);return e&&n}(arguments[0])&&(e=arguments[0],t=void 0):e=t=void 0);var t=t||A(),n=Ye(t,this).startOf("day"),n=p.calendarFormat(this,n)||"sameElse",e=e&&(h(e[n])?e[n].call(this,t):e[n]);return this.format(e||this.localeData().calendar(n,this,A(t)))},i.clone=function(){return new G(this)},i.diff=function(t,e,n){var r,i,o;if(!this.isValid())return NaN;if(!(r=Ye(t,this)).isValid())return NaN;switch(i=6e4*(r.utcOffset()-this.utcOffset()),e=m(e)){case"year":o=Ke(this,r)/12;break;case"month":o=Ke(this,r);break;case"quarter":o=Ke(this,r)/3;break;case"second":o=(this-r)/1e3;break;case"minute":o=(this-r)/6e4;break;case"hour":o=(this-r)/36e5;break;case"day":o=(this-r-i)/864e5;break;case"week":o=(this-r-i)/6048e5;break;default:o=this-r}return n?o:g(o)},i.endOf=function(t){var e,n;if(void 0===(t=m(t))||"millisecond"===t||!this.isValid())return this;switch(n=this._isUTC?rn:nn,t){case"year":e=n(this.year()+1,0,1)-1;break;case"quarter":e=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":e=n(this.year(),this.month()+1,1)-1;break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":e=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":e=this._d.valueOf(),e+=36e5-en(e+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":e=this._d.valueOf(),e+=6e4-en(e,6e4)-1;break;case"second":e=this._d.valueOf(),e+=1e3-en(e,1e3)-1}return this._d.setTime(e),p.updateOffset(this,!0),this},i.format=function(t){return t=t||(this.isUtc()?p.defaultFormatUtc:p.defaultFormat),t=ot(this,t),this.localeData().postformat(t)},i.from=function(t,e){return this.isValid()&&(c(t)&&t.isValid()||A(t).isValid())?L({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},i.fromNow=function(t){return this.from(A(),t)},i.to=function(t,e){return this.isValid()&&(c(t)&&t.isValid()||A(t).isValid())?L({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},i.toNow=function(t){return this.to(A(),t)},i.get=function(t){return h(this[t=m(t)])?this[t]():this},i.invalidAt=function(){return d(this).overflow},i.isAfter=function(t,e){return t=c(t)?t:A(t),!(!this.isValid()||!t.isValid())&&("millisecond"===(e=m(e)||"millisecond")?this.valueOf()>t.valueOf():t.valueOf()<this.clone().startOf(e).valueOf())},i.isBefore=function(t,e){return t=c(t)?t:A(t),!(!this.isValid()||!t.isValid())&&("millisecond"===(e=m(e)||"millisecond")?this.valueOf()<t.valueOf():this.clone().endOf(e).valueOf()<t.valueOf())},i.isBetween=function(t,e,n,r){return t=c(t)?t:A(t),e=c(e)?e:A(e),!!(this.isValid()&&t.isValid()&&e.isValid())&&(("("===(r=r||"()")[0]?this.isAfter(t,n):!this.isBefore(t,n))&&(")"===r[1]?this.isBefore(e,n):!this.isAfter(e,n)))},i.isSame=function(t,e){var t=c(t)?t:A(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=m(e)||"millisecond")?this.valueOf()===t.valueOf():(t=t.valueOf(),this.clone().startOf(e).valueOf()<=t&&t<=this.clone().endOf(e).valueOf()))},i.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},i.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},i.isValid=function(){return W(this)},i.lang=Jt,i.locale=Je,i.localeData=Qe,i.max=vt,i.min=yt,i.parsingFlags=function(){return F({},d(this))},i.set=function(t,e){if("object"==typeof t)for(var n=function(t){var e,n=[];for(e in t)f(t,e)&&n.push({unit:e,priority:ut[e]});return n.sort(function(t,e){return t.priority-e.priority}),n}(t=lt(t)),r=n.length,i=0;i<r;i++)this[n[i].unit](t[n[i].unit]);else if(h(this[t=m(t)]))return this[t](e);return this},i.startOf=function(t){var e,n;if(void 0===(t=m(t))||"millisecond"===t||!this.isValid())return this;switch(n=this._isUTC?rn:nn,t){case"year":e=n(this.year(),0,1);break;case"quarter":e=n(this.year(),this.month()-this.month()%3,1);break;case"month":e=n(this.year(),this.month(),1);break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":e=n(this.year(),this.month(),this.date());break;case"hour":e=this._d.valueOf(),e-=en(e+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":e=this._d.valueOf(),e-=en(e,6e4);break;case"second":e=this._d.valueOf(),e-=en(e,1e3)}return this._d.setTime(e),p.updateOffset(this,!0),this},i.subtract=Zt,i.toArray=function(){return[this.year(),this.month(),this.date(),this.hour(),this.minute(),this.second(),this.millisecond()]},i.toObject=function(){return{years:this.year(),months:this.month(),date:this.date(),hours:this.hours(),minutes:this.minutes(),seconds:this.seconds(),milliseconds:this.milliseconds()}},i.toDate=function(){return new Date(this.valueOf())},i.toISOString=function(t){if(!this.isValid())return null;var e=(t=!0!==t)?this.clone().utc():this;return e.year()<0||9999<e.year()?ot(e,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):h(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",ot(e,"Z")):ot(e,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},i.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t,e="moment",n="";return this.isLocal()||(e=0===this.utcOffset()?"moment.utc":"moment.parseZone",n="Z"),e="["+e+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+(n+'[")]'))},"undefined"!=typeof Symbol&&null!=Symbol.for&&(i[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),i.toJSON=function(){return this.isValid()?this.toISOString():null},i.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},i.unix=function(){return Math.floor(this.valueOf()/1e3)},i.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},i.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},i.eraName=function(){for(var t,e=this.localeData().eras(),n=0,r=e.length;n<r;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].name;if(e[n].until<=t&&t<=e[n].since)return e[n].name}return""},i.eraNarrow=function(){for(var t,e=this.localeData().eras(),n=0,r=e.length;n<r;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].narrow;if(e[n].until<=t&&t<=e[n].since)return e[n].narrow}return""},i.eraAbbr=function(){for(var t,e=this.localeData().eras(),n=0,r=e.length;n<r;++n){if(t=this.clone().startOf("day").valueOf(),e[n].since<=t&&t<=e[n].until)return e[n].abbr;if(e[n].until<=t&&t<=e[n].since)return e[n].abbr}return""},i.eraYear=function(){for(var t,e,n=this.localeData().eras(),r=0,i=n.length;r<i;++r)if(t=n[r].since<=n[r].until?1:-1,e=this.clone().startOf("day").valueOf(),n[r].since<=e&&e<=n[r].until||n[r].until<=e&&e<=n[r].since)return(this.year()-p(n[r].since).year())*t+n[r].offset;return this.year()},i.year=qt,i.isLeapYear=function(){return ct(this.year())},i.weekYear=function(t){return ln.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},i.isoWeekYear=function(t){return ln.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},i.quarter=i.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},i.month=Yt,i.daysInMonth=function(){return At(this.year(),this.month())},i.week=i.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},i.isoWeek=i.isoWeeks=function(t){var e=Gt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},i.weeksInYear=function(){var t=this.localeData()._week;return O(this.year(),t.dow,t.doy)},i.weeksInWeekYear=function(){var t=this.localeData()._week;return O(this.weekYear(),t.dow,t.doy)},i.isoWeeksInYear=function(){return O(this.year(),1,4)},i.isoWeeksInISOWeekYear=function(){return O(this.isoWeekYear(),1,4)},i.date=bt,i.day=i.days=function(t){if(!this.isValid())return null!=t?this:NaN;var e,n,r=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(e=t,n=this.localeData(),t="string"!=typeof e?e:isNaN(e)?"number"==typeof(e=n.weekdaysParse(e))?e:null:parseInt(e,10),this.add(t-r,"d")):r},i.weekday=function(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")},i.isoWeekday=function(t){return this.isValid()?null!=t?(e=t,n=this.localeData(),n="string"==typeof e?n.weekdaysParse(e)%7||7:isNaN(e)?null:e,this.day(this.day()%7?n:n-7)):this.day()||7:null!=t?this:NaN;var e,n},i.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},i.hour=i.hours=b,i.minute=i.minutes=mt,i.second=i.seconds=_t,i.millisecond=i.milliseconds=gt,i.utcOffset=function(t,e,n){var r,i=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null==t)return this._isUTC?i:Fe(this);if("string"==typeof t){if(null===(t=He(Ct,t)))return this}else Math.abs(t)<16&&!n&&(t*=60);return!this._isUTC&&e&&(r=Fe(this)),this._offset=t,this._isUTC=!0,null!=r&&this.add(r,"m"),i!==t&&(!e||this._changeInProgress?Ge(this,L(t-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,p.updateOffset(this,!0),this._changeInProgress=null)),this},i.utc=function(t){return this.utcOffset(0,t)},i.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Fe(this),"m")),this},i.parseZone=function(){var t;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(t=He(St,this._i))?this.utcOffset(t):this.utcOffset(0,!0)),this},i.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?A(t).utcOffset():0,(this.utcOffset()-t)%60==0)},i.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},i.isLocal=function(){return!!this.isValid()&&!this._isUTC},i.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},i.isUtc=We,i.isUTC=We,i.zoneAbbr=function(){return this._isUTC?"UTC":""},i.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},i.dates=t("dates accessor is deprecated. Use date instead.",bt),i.months=t("months accessor is deprecated. Use month instead",Yt),i.years=t("years accessor is deprecated. Use year instead",qt),i.zone=t("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(t,e){return null!=t?(this.utcOffset(t="string"!=typeof t?-t:t,e),this):-this.utcOffset()}),i.isDSTShifted=t("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!a(this._isDSTShifted))return this._isDSTShifted;var t,e={};return z(e,this),(e=Ee(e))._a?(t=(e._isUTC?u:A)(e._a),this._isDSTShifted=this.isValid()&&0<function(t,e,n){for(var r=Math.min(t.length,e.length),i=Math.abs(t.length-e.length),o=0,s=0;s<r;s++)(n&&t[s]!==e[s]||!n&&y(t[s])!==y(e[s]))&&o++;return o+i}(e._a,t.toArray())):this._isDSTShifted=!1,this._isDSTShifted});v=Q.prototype;function fn(t,e,n,r){var i=de(),r=u().set(r,e);return i[n](r,t)}function pn(t,e,n){if(l(t)&&(e=t,t=void 0),t=t||"",null!=e)return fn(t,e,n,"month");for(var r=[],i=0;i<12;i++)r[i]=fn(t,i,n,"month");return r}function dn(t,e,n,r){e=("boolean"==typeof t?l(e)&&(n=e,e=void 0):(e=t,t=!1,l(n=e)&&(n=e,e=void 0)),e||"");var i,o=de(),s=t?o._week.dow:0,a=[];if(null!=n)return fn(e,(n+s)%7,r,"day");for(i=0;i<7;i++)a[i]=fn(e,(i+s)%7,r,"day");return a}v.calendar=function(t,e,n){return h(t=this._calendar[t]||this._calendar.sameElse)?t.call(e,n):t},v.longDateFormat=function(t){var e=this._longDateFormat[t],n=this._longDateFormat[t.toUpperCase()];return e||!n?e:(this._longDateFormat[t]=n.match(et).map(function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t}).join(""),this._longDateFormat[t])},v.invalidDate=function(){return this._invalidDate},v.ordinal=function(t){return this._ordinal.replace("%d",t)},v.preparse=hn,v.postformat=hn,v.relativeTime=function(t,e,n,r){var i=this._relativeTime[n];return h(i)?i(t,e,n,r):i.replace(/%d/i,t)},v.pastFuture=function(t,e){return h(t=this._relativeTime[0<t?"future":"past"])?t(e):t.replace(/%s/i,e)},v.set=function(t){var e,n;for(n in t)f(t,n)&&(h(e=t[n])?this[n]=e:this["_"+n]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},v.eras=function(t,e){for(var n,r=this._eras||de("en")._eras,i=0,o=r.length;i<o;++i)switch("string"==typeof r[i].since&&(n=p(r[i].since).startOf("day"),r[i].since=n.valueOf()),typeof r[i].until){case"undefined":r[i].until=1/0;break;case"string":n=p(r[i].until).startOf("day").valueOf(),r[i].until=n.valueOf()}return r},v.erasParse=function(t,e,n){var r,i,o,s,a,l=this.eras();for(t=t.toUpperCase(),r=0,i=l.length;r<i;++r)if(o=l[r].name.toUpperCase(),s=l[r].abbr.toUpperCase(),a=l[r].narrow.toUpperCase(),n)switch(e){case"N":case"NN":case"NNN":if(s===t)return l[r];break;case"NNNN":if(o===t)return l[r];break;case"NNNNN":if(a===t)return l[r]}else if(0<=[o,s,a].indexOf(t))return l[r]},v.erasConvertYear=function(t,e){var n=t.since<=t.until?1:-1;return void 0===e?p(t.since).year():p(t.since).year()+(e-t.offset)*n},v.erasAbbrRegex=function(t){return f(this,"_erasAbbrRegex")||sn.call(this),t?this._erasAbbrRegex:this._erasRegex},v.erasNameRegex=function(t){return f(this,"_erasNameRegex")||sn.call(this),t?this._erasNameRegex:this._erasRegex},v.erasNarrowRegex=function(t){return f(this,"_erasNarrowRegex")||sn.call(this),t?this._erasNarrowRegex:this._erasRegex},v.months=function(t,e){return t?(s(this._months)?this._months:this._months[(this._months.isFormat||Bt).test(e)?"format":"standalone"])[t.month()]:s(this._months)?this._months:this._months.standalone},v.monthsShort=function(t,e){return t?(s(this._monthsShort)?this._monthsShort:this._monthsShort[Bt.test(e)?"format":"standalone"])[t.month()]:s(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},v.monthsParse=function(t,e,n){var r,i;if(this._monthsParseExact)return function(t,e,n){var r,i,o,t=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)o=u([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(o,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(o,"").toLocaleLowerCase();return n?"MMM"===e?-1!==(i=S.call(this._shortMonthsParse,t))?i:null:-1!==(i=S.call(this._longMonthsParse,t))?i:null:"MMM"===e?-1!==(i=S.call(this._shortMonthsParse,t))||-1!==(i=S.call(this._longMonthsParse,t))?i:null:-1!==(i=S.call(this._longMonthsParse,t))||-1!==(i=S.call(this._shortMonthsParse,t))?i:null}.call(this,t,e,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(i=u([2e3,r]),n&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[r]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[r]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===e&&this._longMonthsParse[r].test(t))return r;if(n&&"MMM"===e&&this._shortMonthsParse[r].test(t))return r;if(!n&&this._monthsParse[r].test(t))return r}},v.monthsRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||Ft.call(this),t?this._monthsStrictRegex:this._monthsRegex):(f(this,"_monthsRegex")||(this._monthsRegex=It),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},v.monthsShortRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||Ft.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(f(this,"_monthsShortRegex")||(this._monthsShortRegex=Rt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},v.week=function(t){return Gt(t,this._week.dow,this._week.doy).week},v.firstDayOfYear=function(){return this._week.doy},v.firstDayOfWeek=function(){return this._week.dow},v.weekdays=function(t,e){return e=s(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"],!0===t?Xt(e,this._week.dow):t?e[t.day()]:e},v.weekdaysMin=function(t){return!0===t?Xt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},v.weekdaysShort=function(t){return!0===t?Xt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},v.weekdaysParse=function(t,e,n){var r,i;if(this._weekdaysParseExact)return function(t,e,n){var r,i,o,t=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)o=u([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(o,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(o,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(o,"").toLocaleLowerCase();return n?"dddd"===e?-1!==(i=S.call(this._weekdaysParse,t))?i:null:"ddd"===e?-1!==(i=S.call(this._shortWeekdaysParse,t))?i:null:-1!==(i=S.call(this._minWeekdaysParse,t))?i:null:"dddd"===e?-1!==(i=S.call(this._weekdaysParse,t))||-1!==(i=S.call(this._shortWeekdaysParse,t))||-1!==(i=S.call(this._minWeekdaysParse,t))?i:null:"ddd"===e?-1!==(i=S.call(this._shortWeekdaysParse,t))||-1!==(i=S.call(this._weekdaysParse,t))||-1!==(i=S.call(this._minWeekdaysParse,t))?i:null:-1!==(i=S.call(this._minWeekdaysParse,t))||-1!==(i=S.call(this._weekdaysParse,t))||-1!==(i=S.call(this._shortWeekdaysParse,t))?i:null}.call(this,t,e,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(i=u([2e3,1]).day(r),n&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[r]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[r]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===e&&this._fullWeekdaysParse[r].test(t))return r;if(n&&"ddd"===e&&this._shortWeekdaysParse[r].test(t))return r;if(n&&"dd"===e&&this._minWeekdaysParse[r].test(t))return r;if(!n&&this._weekdaysParse[r].test(t))return r}},v.weekdaysRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||ne.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(f(this,"_weekdaysRegex")||(this._weekdaysRegex=Qt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},v.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||ne.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(f(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=te),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},v.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||ne.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(f(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=ee),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},v.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},v.meridiem=function(t,e,n){return 11<t?n?"pm":"PM":n?"am":"AM"},fe("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===y(t%100/10)?"th":1==e?"st":2==e?"nd":3==e?"rd":"th")}}),p.lang=t("moment.lang is deprecated. Use moment.locale instead.",fe),p.langData=t("moment.langData is deprecated. Use moment.localeData instead.",de);var mn=Math.abs;function gn(t,e,n,r){e=L(e,n);return t._milliseconds+=r*e._milliseconds,t._days+=r*e._days,t._months+=r*e._months,t._bubble()}function yn(t){return t<0?Math.floor(t):Math.ceil(t)}function vn(t){return 4800*t/146097}function xn(t){return 146097*t/4800}function bn(t){return function(){return this.as(t)}}xt=bn("ms"),dt=bn("s"),Lt=bn("m"),vt=bn("h"),yt=bn("d"),Zt=bn("w"),b=bn("M"),mt=bn("Q"),_t=bn("y");function _n(t){return function(){return this.isValid()?this._data[t]:NaN}}var gt=_n("milliseconds"),bt=_n("seconds"),qt=_n("minutes"),v=_n("hours"),wn=_n("days"),kn=_n("months"),Sn=_n("years");var Cn=Math.round,Pn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function Tn(t,e,n,r){var i=L(t).abs(),o=Cn(i.as("s")),s=Cn(i.as("m")),a=Cn(i.as("h")),l=Cn(i.as("d")),u=Cn(i.as("M")),c=Cn(i.as("w")),i=Cn(i.as("y")),o=(o<=n.ss?["s",o]:o<n.s&&["ss",o])||s<=1&&["m"]||s<n.m&&["mm",s]||a<=1&&["h"]||a<n.h&&["hh",a]||l<=1&&["d"]||l<n.d&&["dd",l];return(o=(o=null!=n.w?o||c<=1&&["w"]||c<n.w&&["ww",c]:o)||u<=1&&["M"]||u<n.M&&["MM",u]||i<=1&&["y"]||["yy",i])[2]=e,o[3]=0<+t,o[4]=r,function(t,e,n,r,i){return i.relativeTime(e||1,!!n,t,r)}.apply(null,o)}var Dn=Math.abs;function Mn(t){return(0<t)-(t<0)||+t}function En(){if(!this.isValid())return this.localeData().invalidDate();var t,e,n,r,i,o,s,a=Dn(this._milliseconds)/1e3,l=Dn(this._days),u=Dn(this._months),c=this.asSeconds();return c?(t=g(a/60),e=g(t/60),a%=60,t%=60,n=g(u/12),u%=12,r=a?a.toFixed(3).replace(/\.?0+$/,""):"",i=Mn(this._months)!==Mn(c)?"-":"",o=Mn(this._days)!==Mn(c)?"-":"",s=Mn(this._milliseconds)!==Mn(c)?"-":"",(c<0?"-":"")+"P"+(n?i+n+"Y":"")+(u?i+u+"M":"")+(l?o+l+"D":"")+(e||t||a?"T":"")+(e?s+e+"H":"")+(t?s+t+"M":"")+(a?s+r+"S":"")):"P0D"}var j=Le.prototype;return j.isValid=function(){return this._isValid},j.abs=function(){var t=this._data;return this._milliseconds=mn(this._milliseconds),this._days=mn(this._days),this._months=mn(this._months),t.milliseconds=mn(t.milliseconds),t.seconds=mn(t.seconds),t.minutes=mn(t.minutes),t.hours=mn(t.hours),t.months=mn(t.months),t.years=mn(t.years),this},j.add=function(t,e){return gn(this,t,e,1)},j.subtract=function(t,e){return gn(this,t,e,-1)},j.as=function(t){if(!this.isValid())return NaN;var e,n,r=this._milliseconds;if("month"===(t=m(t))||"quarter"===t||"year"===t)switch(e=this._days+r/864e5,n=this._months+vn(e),t){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(e=this._days+Math.round(xn(this._months)),t){case"week":return e/7+r/6048e5;case"day":return e+r/864e5;case"hour":return 24*e+r/36e5;case"minute":return 1440*e+r/6e4;case"second":return 86400*e+r/1e3;case"millisecond":return Math.floor(864e5*e)+r;default:throw new Error("Unknown unit "+t)}},j.asMilliseconds=xt,j.asSeconds=dt,j.asMinutes=Lt,j.asHours=vt,j.asDays=yt,j.asWeeks=Zt,j.asMonths=b,j.asQuarters=mt,j.asYears=_t,j.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*y(this._months/12):NaN},j._bubble=function(){var t=this._milliseconds,e=this._days,n=this._months,r=this._data;return 0<=t&&0<=e&&0<=n||t<=0&&e<=0&&n<=0||(t+=864e5*yn(xn(n)+e),n=e=0),r.milliseconds=t%1e3,t=g(t/1e3),r.seconds=t%60,t=g(t/60),r.minutes=t%60,t=g(t/60),r.hours=t%24,e+=g(t/24),n+=t=g(vn(e)),e-=yn(xn(t)),t=g(n/12),n%=12,r.days=e,r.months=n,r.years=t,this},j.clone=function(){return L(this)},j.get=function(t){return t=m(t),this.isValid()?this[t+"s"]():NaN},j.milliseconds=gt,j.seconds=bt,j.minutes=qt,j.hours=v,j.days=wn,j.weeks=function(){return g(this.days()/7)},j.months=kn,j.years=Sn,j.humanize=function(t,e){if(!this.isValid())return this.localeData().invalidDate();var n=!1,r=Pn;return"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(n=t),"object"==typeof e&&(r=Object.assign({},Pn,e),null!=e.s&&null==e.ss&&(r.ss=e.s-1)),t=this.localeData(),e=Tn(this,!n,r,t),n&&(e=t.pastFuture(+this,e)),t.postformat(e)},j.toISOString=En,j.toString=En,j.toJSON=En,j.locale=Je,j.localeData=Qe,j.toIsoString=t("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",En),j.lang=Jt,r("X",0,0,"unix"),r("x",0,0,"valueOf"),_("x",kt),_("X",/[+-]?\d+(\.\d{1,3})?/),k("X",function(t,e,n){n._d=new Date(1e3*parseFloat(t))}),k("x",function(t,e,n){n._d=new Date(y(t))}),p.version="2.29.2",B=A,p.fn=i,p.min=function(){return Ne("isBefore",[].slice.call(arguments,0))},p.max=function(){return Ne("isAfter",[].slice.call(arguments,0))},p.now=function(){return Date.now?Date.now():+new Date},p.utc=u,p.unix=function(t){return A(1e3*t)},p.months=function(t,e){return pn(t,e,"months")},p.isDate=H,p.locale=fe,p.invalid=q,p.duration=L,p.isMoment=c,p.weekdays=function(t,e,n){return dn(t,e,n,"weekdays")},p.parseZone=function(){return A.apply(null,arguments).parseZone()},p.localeData=de,p.isDuration=je,p.monthsShort=function(t,e){return pn(t,e,"monthsShort")},p.weekdaysMin=function(t,e,n){return dn(t,e,n,"weekdaysMin")},p.defineLocale=pe,p.updateLocale=function(t,e){var n,r;return null!=e?(r=ae,null!=N[t]&&null!=N[t].parentLocale?N[t].set(J(N[t]._config,e)):(e=J(r=null!=(n=he(t))?n._config:r,e),null==n&&(e.abbr=t),(r=new Q(e)).parentLocale=N[t],N[t]=r),fe(t)):null!=N[t]&&(null!=N[t].parentLocale?(N[t]=N[t].parentLocale,t===fe()&&fe(t)):null!=N[t]&&delete N[t]),N[t]},p.locales=function(){return tt(N)},p.weekdaysShort=function(t,e,n){return dn(t,e,n,"weekdaysShort")},p.normalizeUnits=m,p.relativeTimeRounding=function(t){return void 0===t?Cn:"function"==typeof t&&(Cn=t,!0)},p.relativeTimeThreshold=function(t,e){return void 0!==Pn[t]&&(void 0===e?Pn[t]:(Pn[t]=e,"s"===t&&(Pn.ss=e-1),!0))},p.calendarFormat=function(t,e){return(t=t.diff(e,"days",!0))<-6?"sameElse":t<-1?"lastWeek":t<0?"lastDay":t<1?"sameDay":t<2?"nextDay":t<7?"nextWeek":"sameElse"},p.prototype=i,p.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},p},"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.moment=e(),t=window,e=function(){return n={"./dev/raphael.amd.js":function(t,e,n){n=[n("./dev/raphael.core.js"),n("./dev/raphael.svg.js"),n("./dev/raphael.vml.js")],e=function(t){return t}.apply(e,n);void 0!==e&&(t.exports=e)},"./dev/raphael.core.js":function(t,e,n){n=[n("./node_modules/eve-raphael/eve.js")],e=function(E){function O(t){var e,n;return O.is(t,"function")?f?t():E.on("raphael.DOMload",t):O.is(t,v)?O._engine.create[b](O,t.splice(0,3+O.is(t[0],B))).add(t):(e=Array.prototype.slice.call(arguments,0),O.is(e[e.length-1],"function")?(n=e.pop(),f?n.call(O._engine.create[b](O,e)):E.on("raphael.DOMload",function(){n.call(O._engine.create[b](O,e))})):O._engine.create[b](O,arguments))}O.version="2.3.0",O.eve=E;function l(){this.ca=this.customAttributes={}}function u(t,e,n,r){return[["M",t,e],["m",0,-(r=null==r?n:r)],["a",n,r,0,1,1,0,2*r],["a",n,r,0,1,1,0,-2*r],["z"]]}var f,W=/[, ]+/,p={circle:1,rect:1,path:1,ellipse:1,text:1,image:1},y=/\{(\d+)\}/g,N="hasOwnProperty",d={doc:document,win:window},x={was:Object.prototype[N].call(d.win,"Raphael"),is:d.win.Raphael},b="apply",A="concat",q="ontouchstart"in window||window.TouchEvent||window.DocumentTouch&&document instanceof DocumentTouch,_="",w=" ",L=String,j="split",V="click dblclick mousedown mousemove mouseout mouseover mouseup touchstart touchmove touchend touchcancel"[j](w),$={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},U=L.prototype.toLowerCase,S=Math,k=S.max,C=S.min,P=S.abs,g=S.pow,T=S.PI,B="number",z="string",v="array",G=Object.prototype.toString,X=(O._ISURL=/^url\(['"]?(.+?)['"]?\)$/i,/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\))\s*$/i),Z={NaN:1,Infinity:1,"-Infinity":1},K=/^(?:cubic-)?bezier\(([^,]+),([^,]+),([^,]+),([^\)]+)\)/,J=S.round,R=parseFloat,c=parseInt,Q=L.prototype.toUpperCase,tt=O._availableAttrs={"arrow-end":"none","arrow-start":"none",blur:0,"clip-rect":"0 0 1e9 1e9",cursor:"default",cx:0,cy:0,fill:"#fff","fill-opacity":1,font:'10px "Arial"',"font-family":'"Arial"',"font-size":"10","font-style":"normal","font-weight":400,gradient:0,height:0,href:"http://raphaeljs.com/","letter-spacing":0,opacity:1,path:"M0,0",r:0,rx:0,ry:0,src:"",stroke:"#000","stroke-dasharray":"","stroke-linecap":"butt","stroke-linejoin":"butt","stroke-miterlimit":0,"stroke-opacity":1,"stroke-width":1,target:"_blank","text-anchor":"middle",title:"Raphael",transform:"",width:0,x:0,y:0,class:""},et=O._availableAnimAttrs={blur:B,"clip-rect":"csv",cx:B,cy:B,fill:"colour","fill-opacity":B,"font-size":B,height:B,opacity:B,path:"path",r:B,rx:B,ry:B,stroke:"colour","stroke-opacity":B,"stroke-width":B,transform:"transform",width:B,x:B,y:B},nt=/[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/,rt={hs:1,rg:1},it=/,?([achlmqrstvxz]),?/gi,ot=/([achlmrqstvz])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/gi,st=/([rstm])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/gi,at=/(-?\d*\.?\d*(?:e[\-+]?\d+)?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/gi,i=(O._radial_gradient=/^r(?:\(([^,]+?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*([^\)]+?)\))?/,{}),lt=function(t,e){return R(t)-R(e)},ut=function(t){return t},ct=O._rectPath=function(t,e,n,r,i){return i?[["M",t+i,e],["l",n-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-n,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]]:[["M",t,e],["l",n,0],["l",0,r],["l",-n,0],["z"]]},ht=O._getPath={path:function(t){return t.attr("path")},circle:function(t){t=t.attrs;return u(t.cx,t.cy,t.r)},ellipse:function(t){t=t.attrs;return u(t.cx,t.cy,t.rx,t.ry)},rect:function(t){t=t.attrs;return ct(t.x,t.y,t.width,t.height,t.r)},image:function(t){t=t.attrs;return ct(t.x,t.y,t.width,t.height)},text:function(t){t=t._getBBox();return ct(t.x,t.y,t.width,t.height)},set:function(t){t=t._getBBox();return ct(t.x,t.y,t.width,t.height)}},ft=O.mapPath=function(t,e){if(!e)return t;for(var n,r,i,o,s,a=0,l=(t=jt(t)).length;a<l;a++)for(i=1,o=(s=t[a]).length;i<o;i+=2)n=e.x(s[i],s[i+1]),r=e.y(s[i],s[i+1]),s[i]=n,s[i+1]=r;return t};if(O._g=d,O.type=d.win.SVGAngle||d.doc.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")?"SVG":"VML","VML"==O.type){var t,e=d.doc.createElement("div");if(e.innerHTML='<v:shape adj="1"/>',(t=e.firstChild).style.behavior="url(#default#VML)",!t||"object"!=typeof t.adj)return O.type=_;e=null}function pt(t){if("function"==typeof t||Object(t)!==t)return t;var e,n=new t.constructor;for(e in t)t[N](e)&&(n[e]=pt(t[e]));return n}O.svg=!(O.vml="VML"==O.type),O._Paper=l,O.fn=t=l.prototype=O.prototype,O._id=0,O.is=function(t,e){return"finite"==(e=U.call(e))?!Z[N](+t):"array"==e?t instanceof Array:"null"==e&&null===t||e==typeof t&&null!==t||"object"==e&&t===Object(t)||"array"==e&&Array.isArray&&Array.isArray(t)||G.call(t).slice(8,-1).toLowerCase()==e},O.angle=function(t,e,n,r,i,o){var s,a;return null==i?(a=e-r,(s=t-n)||a?(180+180*S.atan2(-a,-s)/T+360)%360:0):O.angle(t,e,i,o)-O.angle(n,r,i,o)},O.rad=function(t){return t%360*T/180},O.deg=function(t){return Math.round(180*t/T%360*1e3)/1e3},O.snapTo=function(t,e,n){if(n=O.is(n,"finite")?n:10,O.is(t,v)){for(var r=t.length;r--;)if(P(t[r]-e)<=n)return t[r]}else{var i=e%(t=+t);if(i<n)return e-i;if(t-n<i)return e-i+t}return e};O.createUUID=(vt=/[xy]/g,xt=function(t){var e=16*S.random()|0;return("x"==t?e:3&e|8).toString(16)},function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(vt,xt).toUpperCase()});function dt(){return"hsb("+[this.h,this.s,this.b]+")"}function mt(){return"hsl("+[this.h,this.s,this.l]+")"}function gt(t,e,n){var r;return null==e&&O.is(t,"object")&&"r"in t&&"g"in t&&"b"in t&&(n=t.b,e=t.g,t=t.r),null==e&&O.is(t,z)&&(t=(r=O.getRGB(t)).r,e=r.g,n=r.b),(1<t||1<e||1<n)&&(t/=255,e/=255,n/=255),[t,e,n]}function yt(t,e,n,r){return t={r:t*=255,g:e*=255,b:n*=255,hex:O.rgb(t,e,n),toString:_t},O.is(r,"finite")&&(t.opacity=r),t}O.setWindow=function(t){E("raphael.setWindow",O,d.win,t),d.win=t,d.doc=d.win.document,O._engine.initWin&&O._engine.initWin(d.win)};var vt,xt,bt=function(t){if(O.vml){var n,r=/^\s+|\s+$/g;try{var e=new ActiveXObject("htmlfile");e.write("<body>"),e.close(),n=e.body}catch(t){n=createPopup().document.body}var i=n.createTextRange();bt=D(function(t){try{n.style.color=L(t).replace(r,_);var e=i.queryCommandValue("ForeColor");return"#"+("000000"+(e=(255&e)<<16|65280&e|(16711680&e)>>>16).toString(16)).slice(-6)}catch(t){return"none"}})}else{var o=d.doc.createElement("i");o.title="Raphaël Colour Picker",o.style.display="none",d.doc.body.appendChild(o),bt=D(function(t){return o.style.color=t,d.doc.defaultView.getComputedStyle(o,_).getPropertyValue("color")})}return bt(t)},_t=function(){return this.hex};function D(i,o,s){function a(){var t=Array.prototype.slice.call(arguments,0),e=t.join("␀"),n=a.cache=a.cache||{},r=a.count=a.count||[];return n[N](e)?function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return t.push(t.splice(n,1)[0])}(r,e):(1e3<=r.length&&delete n[r.shift()],r.push(e),n[e]=i[b](o,t)),s?s(n[e]):n[e]}return a}O.color=function(t){var e;return O.is(t,"object")&&"h"in t&&"s"in t&&"b"in t?(e=O.hsb2rgb(t),t.r=e.r,t.g=e.g,t.b=e.b,t.hex=e.hex):O.is(t,"object")&&"h"in t&&"s"in t&&"l"in t?(e=O.hsl2rgb(t),t.r=e.r,t.g=e.g,t.b=e.b,t.hex=e.hex):(O.is(t,"string")&&(t=O.getRGB(t)),O.is(t,"object")&&"r"in t&&"g"in t&&"b"in t?(e=O.rgb2hsl(t),t.h=e.h,t.s=e.s,t.l=e.l,e=O.rgb2hsb(t),t.v=e.b):(t={hex:"none"}).r=t.g=t.b=t.h=t.s=t.v=t.l=-1),t.toString=_t,t},O.hsb2rgb=function(t,e,n,r){var i,o,s;return this.is(t,"object")&&"h"in t&&"s"in t&&"b"in t&&(n=t.b,e=t.s,r=t.o,t=t.h),s=(e=n*e)*(1-P((t=(t*=360)%360/60)%2-1)),i=o=n=n-e,yt(i+=[e,s,0,0,s,e][t=~~t],o+=[s,e,e,s,0,0][t],n+=[0,0,s,e,e,s][t],r)},O.hsl2rgb=function(t,e,n,r){var i,o,s;return this.is(t,"object")&&"h"in t&&"s"in t&&"l"in t&&(n=t.l,e=t.s,t=t.h),(1<t||1<e||1<n)&&(t/=360,e/=100,n/=100),s=(e=2*e*(n<.5?n:1-n))*(1-P((t=(t*=360)%360/60)%2-1)),i=o=n=n-e/2,yt(i+=[e,s,0,0,s,e][t=~~t],o+=[s,e,e,s,0,0][t],n+=[0,0,s,e,e,s][t],r)},O.rgb2hsb=function(t,e,n){var r,i;return t=(n=gt(t,e,n))[0],e=n[1],n=n[2],{h:((0==(i=(r=k(t,e,n))-C(t,e,n))?null:r==t?(e-n)/i:r==e?(n-t)/i+2:(t-e)/i+4)+360)%6*60/360,s:0==i?0:i/r,b:r,toString:dt}},O.rgb2hsl=function(t,e,n){var r,i,o;return t=(n=gt(t,e,n))[0],e=n[1],n=n[2],r=((i=k(t,e,n))+(o=C(t,e,n)))/2,{h:((0==(o=i-o)?null:i==t?(e-n)/o:i==e?(n-t)/o+2:(t-e)/o+4)+360)%6*60/360,s:0==o?0:r<.5?o/(2*r):o/(2-2*r),l:r,toString:mt}},O._path2string=function(){return this.join(",").replace(it,"$1")};O._preload=function(t,e){var n=d.doc.createElement("img");n.style.cssText="position:absolute;left:-9999em;top:-9999em",n.onload=function(){e.call(this),this.onload=null,d.doc.body.removeChild(this)},n.onerror=function(){d.doc.body.removeChild(this)},d.doc.body.appendChild(n),n.src=t};function wt(){return this.hex}function kt(t,e){for(var n=[],r=0,i=t.length;r<i-2*!e;r+=2){var o=[{x:+t[r-2],y:+t[r-1]},{x:+t[r],y:+t[r+1]},{x:+t[r+2],y:+t[r+3]},{x:+t[r+4],y:+t[r+5]}];e?r?i-4==r?o[3]={x:+t[0],y:+t[1]}:i-2==r&&(o[2]={x:+t[0],y:+t[1]},o[3]={x:+t[2],y:+t[3]}):o[0]={x:+t[i-2],y:+t[i-1]}:i-4==r?o[3]=o[2]:r||(o[0]={x:+t[r],y:+t[r+1]}),n.push(["C",(-o[0].x+6*o[1].x+o[2].x)/6,(-o[0].y+6*o[1].y+o[2].y)/6,(o[1].x+6*o[2].x-o[3].x)/6,(o[1].y+6*o[2].y-o[3].y)/6,o[2].x,o[2].y])}return n}O.getRGB=D(function(t){if(!t||(t=L(t)).indexOf("-")+1)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:wt};if("none"==t)return{r:-1,g:-1,b:-1,hex:"none",toString:wt};var e,n,r,i,o,s,t=(t=rt[N](t.toLowerCase().substring(0,2))||"#"==t.charAt()?t:bt(t)).match(X);return t?(t[2]&&(r=c(t[2].substring(5),16),n=c(t[2].substring(3,5),16),e=c(t[2].substring(1,3),16)),t[3]&&(r=c((o=t[3].charAt(3))+o,16),n=c((o=t[3].charAt(2))+o,16),e=c((o=t[3].charAt(1))+o,16)),t[4]&&(s=t[4][j](nt),e=R(s[0]),"%"==s[0].slice(-1)&&(e*=2.55),n=R(s[1]),"%"==s[1].slice(-1)&&(n*=2.55),r=R(s[2]),"%"==s[2].slice(-1)&&(r*=2.55),"rgba"==t[1].toLowerCase().slice(0,4)&&(i=R(s[3])),s[3]&&"%"==s[3].slice(-1)&&(i/=100)),t[5]?(s=t[5][j](nt),e=R(s[0]),"%"==s[0].slice(-1)&&(e*=2.55),n=R(s[1]),"%"==s[1].slice(-1)&&(n*=2.55),r=R(s[2]),"%"==s[2].slice(-1)&&(r*=2.55),"deg"!=s[0].slice(-3)&&"°"!=s[0].slice(-1)||(e/=360),"hsba"==t[1].toLowerCase().slice(0,4)&&(i=R(s[3])),s[3]&&"%"==s[3].slice(-1)&&(i/=100),O.hsb2rgb(e,n,r,i)):t[6]?(s=t[6][j](nt),e=R(s[0]),"%"==s[0].slice(-1)&&(e*=2.55),n=R(s[1]),"%"==s[1].slice(-1)&&(n*=2.55),r=R(s[2]),"%"==s[2].slice(-1)&&(r*=2.55),"deg"!=s[0].slice(-3)&&"°"!=s[0].slice(-1)||(e/=360),"hsla"==t[1].toLowerCase().slice(0,4)&&(i=R(s[3])),s[3]&&"%"==s[3].slice(-1)&&(i/=100),O.hsl2rgb(e,n,r,i)):((t={r:e,g:n,b:r,toString:wt}).hex="#"+(16777216|r|n<<8|e<<16).toString(16).slice(1),O.is(i,"finite")&&(t.opacity=i),t)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:wt}},O),O.hsb=D(function(t,e,n){return O.hsb2rgb(t,e,n).hex}),O.hsl=D(function(t,e,n){return O.hsl2rgb(t,e,n).hex}),O.rgb=D(function(t,e,n){return"#"+(n+.5|16777216|(e+.5|0)<<8|(t+.5|0)<<16).toString(16).slice(1)}),O.getColor=function(t){var t=this.getColor.start=this.getColor.start||{h:0,s:1,b:t||.75},e=this.hsb2rgb(t.h,t.s,t.b);return t.h+=.075,1<t.h&&(t.h=0,t.s-=.2,t.s<=0&&(this.getColor.start={h:0,s:1,b:t.b})),e.hex},O.getColor.reset=function(){delete this.start},O.parsePathString=function(t){if(!t)return null;var e=M(t);if(e.arr)return H(e.arr);var o={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},s=[];return(s=O.is(t,v)&&O.is(t[0],v)?H(t):s).length||L(t).replace(ot,function(t,e,n){var r=[],i=e.toLowerCase();if(n.replace(at,function(t,e){e&&r.push(+e)}),"m"==i&&2<r.length&&(s.push([e][A](r.splice(0,2))),i="l",e="m"==e?"l":"L"),"r"==i)s.push([e][A](r));else for(;r.length>=o[i]&&(s.push([e][A](r.splice(0,o[i]))),o[i]););}),s.toString=O._path2string,e.arr=H(s),s},O.parseTransformString=D(function(t){if(!t)return null;var i=[];return(i=O.is(t,v)&&O.is(t[0],v)?H(t):i).length||L(t).replace(st,function(t,e,n){var r=[];U.call(e);n.replace(at,function(t,e){e&&r.push(+e)}),i.push([e][A](r))}),i.toString=O._path2string,i},this,function(t){if(!t)return t;for(var e=[],n=0;n<t.length;n++){for(var r=[],i=0;i<t[n].length;i++)r.push(t[n][i]);e.push(r)}return e});var M=function(e){var n=M.ps=M.ps||{};return n[e]?n[e].sleep=100:n[e]={sleep:100},setTimeout(function(){for(var t in n)n[N](t)&&t!=e&&(n[t].sleep--,n[t].sleep||delete n[t])}),n[e]};function St(t,e,n,r,i){return t*(t*(-3*e+9*n-9*r+3*i)+6*e-12*n+6*r)-3*e+3*n}function I(t,e,n,r,i,o,s,a,l){for(var u=(l=1<(l=null==l?1:l)?1:l<0?0:l)/2,c=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],h=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],f=0,p=0;p<12;p++){var d=u*c[p]+u,m=St(d,t,n,i,s),d=St(d,e,r,o,a);f+=h[p]*S.sqrt(m*m+d*d)}return u*f}function Ct(t,e,n){var r=O.bezierBBox(t),i=O.bezierBBox(e);if(!O.isBBoxIntersect(r,i))return n?0:[];for(var r=I.apply(0,t),i=I.apply(0,e),o=k(~~(r/5),1),s=k(~~(i/5),1),a=[],l=[],u={},c=n?0:[],h=0;h<o+1;h++){var f=O.findDotsAtSegment.apply(O,t.concat(h/o));a.push({x:f.x,y:f.y,t:h/o})}for(h=0;h<s+1;h++)f=O.findDotsAtSegment.apply(O,e.concat(h/s)),l.push({x:f.x,y:f.y,t:h/s});for(h=0;h<o;h++)for(var p=0;p<s;p++){var d=a[h],m=a[h+1],g=l[p],y=l[p+1],v=P(m.x-d.x)<.001?"y":"x",x=P(y.x-g.x)<.001?"y":"x",b=function(t,e,n,r,i,o,s,a){if(!(k(t,n)<C(i,s)||C(t,n)>k(i,s)||k(e,r)<C(o,a)||C(e,r)>k(o,a))){var l=(t*r-e*n)*(i-s)-(t-n)*(i*a-o*s),u=(t*r-e*n)*(o-a)-(e-r)*(i*a-o*s),c=(t-n)*(o-a)-(e-r)*(i-s);if(c){var l=l/c,u=u/c,c=+l.toFixed(2),h=+u.toFixed(2);if(!(c<+C(t,n).toFixed(2)||c>+k(t,n).toFixed(2)||c<+C(i,s).toFixed(2)||c>+k(i,s).toFixed(2)||h<+C(e,r).toFixed(2)||h>+k(e,r).toFixed(2)||h<+C(o,a).toFixed(2)||h>+k(o,a).toFixed(2)))return{x:l,y:u}}}}(d.x,d.y,m.x,m.y,g.x,g.y,y.x,y.y);b&&u[b.x.toFixed(4)]!=b.y.toFixed(4)&&(u[b.x.toFixed(4)]=b.y.toFixed(4),v=d.t+P((b[v]-d[v])/(m[v]-d[v]))*(m.t-d.t),m=g.t+P((b[x]-g[x])/(y[x]-g[x]))*(y.t-g.t),0<=v&&v<=1.001&&0<=m&&m<=1.001&&(n?c++:c.push({x:b.x,y:b.y,t1:C(v,1),t2:C(m,1)})))}return c}function Pt(t,e,n){t=O._path2curve(t),e=O._path2curve(e);for(var r,i,o,s,a,l,u,c,h=n?0:[],f=0,p=t.length;f<p;f++){var d=t[f];if("M"==d[0])r=o=d[1],m=s=d[2];else for(var m="C"==d[0]?(r=(u=[r,m].concat(d.slice(1)))[6],u[7]):(u=[r,m,r,m,o,s,o,s],r=o,s),g=0,y=e.length;g<y;g++){var v=e[g];if("M"==v[0])i=a=v[1],x=l=v[2];else{var x="C"==v[0]?(i=(c=[i,x].concat(v.slice(1)))[6],c[7]):(c=[i,x,i,x,a,l,a,l],i=a,l),b=Ct(u,c,n);if(n)h+=b;else{for(var _=0,w=b.length;_<w;_++)b[_].segment1=f,b[_].segment2=g,b[_].bez1=u,b[_].bez2=c;h=h.concat(b)}}}}return h}O.findDotsAtSegment=function(t,e,n,r,i,o,s,a,l){var u=1-l,c=g(u,3),h=g(u,2),f=l*l,p=f*l,d=c*t+3*h*l*n+3*u*l*l*i+p*s,c=c*e+3*h*l*r+3*u*l*l*o+p*a,h=t+2*l*(n-t)+f*(i-2*n+t),p=e+2*l*(r-e)+f*(o-2*r+e),m=n+2*l*(i-n)+f*(s-2*i+n),f=r+2*l*(o-r)+f*(a-2*o+r),t=u*t+l*n,n=u*e+l*r,e=u*i+l*s,r=u*o+l*a,i=90-180*S.atan2(h-m,p-f)/T;return(m<h||p<f)&&(i+=180),{x:d,y:c,m:{x:h,y:p},n:{x:m,y:f},start:{x:t,y:n},end:{x:e,y:r},alpha:i}},O.bezierBBox=function(t,e,n,r,i,o,s,a){O.is(t,"array")||(t=[t,e,n,r,i,o,s,a]);e=Lt.apply(null,t);return{x:e.min.x,y:e.min.y,x2:e.max.x,y2:e.max.y,width:e.max.x-e.min.x,height:e.max.y-e.min.y}},O.isPointInsideBBox=function(t,e,n){return e>=t.x&&e<=t.x2&&n>=t.y&&n<=t.y2},O.isBBoxIntersect=function(t,e){var n=O.isPointInsideBBox;return n(e,t.x,t.y)||n(e,t.x2,t.y)||n(e,t.x,t.y2)||n(e,t.x2,t.y2)||n(t,e.x,e.y)||n(t,e.x2,e.y)||n(t,e.x,e.y2)||n(t,e.x2,e.y2)||(t.x<e.x2&&t.x>e.x||e.x<t.x2&&e.x>t.x)&&(t.y<e.y2&&t.y>e.y||e.y<t.y2&&e.y>t.y)},O.pathIntersection=function(t,e){return Pt(t,e)},O.pathIntersectionNumber=function(t,e){return Pt(t,e,1)},O.isPointInsidePath=function(t,e,n){var r=O.pathBBox(t);return O.isPointInsideBBox(r,e,n)&&Pt(t,[["M",e,n],["H",r.x2+10]],1)%2==1},O._removedFactory=function(t){return function(){E("raphael.log",null,"Raphaël: you are calling to method “"+t+"” of removed object",t)}};function Tt(t,e,n,r){return[t,e,n,r,n,r]}function Dt(t,e,n,r,i,o){return[1/3*t+2/3*n,1/3*e+2/3*r,1/3*i+2/3*n,1/3*o+2/3*r,i,o]}function Mt(t,e,n,r,i,o,s,a,l){var u=1-l;return{x:g(u,3)*t+3*g(u,2)*l*n+3*u*l*l*i+g(l,3)*s,y:g(u,3)*e+3*g(u,2)*l*r+3*u*l*l*o+g(l,3)*a}}function Et(t){var e=t[0];switch(e.toLowerCase()){case"t":return[e,0,0];case"m":return[e,1,0,0,1,0,0];case"r":return 4==t.length?[e,0,t[2],t[3]]:[e,0];case"s":return 5==t.length?[e,1,1,t[3],t[4]]:3==t.length?[e,1,1]:[e,1]}}var Ot=O.pathBBox=function(t){var e=M(t);if(e.bbox)return pt(e.bbox);if(!t)return{x:0,y:0,width:0,height:0,x2:0,y2:0};for(var n,r,i=0,o=0,s=[],a=[],l=0,u=(t=jt(t)).length;l<u;l++)"M"==(r=t[l])[0]?(i=r[1],o=r[2],s.push(i),a.push(o)):(n=Lt(i,o,r[1],r[2],r[3],r[4],r[5],r[6]),s=s[A](n.min.x,n.max.x),a=a[A](n.min.y,n.max.y),i=r[5],o=r[6]);var c=C[b](0,s),h=C[b](0,a),f=k[b](0,s),p=k[b](0,a),d=f-c,m=p-h,f={x:c,y:h,x2:f,y2:p,width:d,height:m,cx:c+d/2,cy:h+m/2};return e.bbox=pt(f),f},H=function(t){t=pt(t);return t.toString=O._path2string,t},e=O._pathToRelative=function(t){var e=M(t);if(e.rel)return H(e.rel);var n=[],r=0,i=0,o=0,s=0,a=0;"M"==(t=O.is(t,v)&&O.is(t&&t[0],v)?t:O.parsePathString(t))[0][0]&&(o=r=t[0][1],s=i=t[0][2],a++,n.push(["M",r,i]));for(var l=a,u=t.length;l<u;l++){var c=n[l]=[],h=t[l];if(h[0]!=U.call(h[0]))switch(c[0]=U.call(h[0]),c[0]){case"a":c[1]=h[1],c[2]=h[2],c[3]=h[3],c[4]=h[4],c[5]=h[5],c[6]=+(h[6]-r).toFixed(3),c[7]=+(h[7]-i).toFixed(3);break;case"v":c[1]=+(h[1]-i).toFixed(3);break;case"m":o=h[1],s=h[2];default:for(var f=1,p=h.length;f<p;f++)c[f]=+(h[f]-(f%2?r:i)).toFixed(3)}else{c=n[l]=[],"m"==h[0]&&(o=h[1]+r,s=h[2]+i);for(var d=0,m=h.length;d<m;d++)n[l][d]=h[d]}var g=n[l].length;switch(n[l][0]){case"z":r=o,i=s;break;case"h":r+=+n[l][g-1];break;case"v":i+=+n[l][g-1];break;default:r+=+n[l][g-2],i+=+n[l][g-1]}}return n.toString=O._path2string,e.rel=H(n),n},Nt=O._pathToAbsolute=function(t){var e=M(t);if(e.abs)return H(e.abs);if(!(t=O.is(t,v)&&O.is(t&&t[0],v)?t:O.parsePathString(t))||!t.length)return[["M",0,0]];for(var n,r,i=[],o=0,s=0,a=0,l=0,u=0,c=("M"==t[0][0]&&(a=o=+t[0][1],l=s=+t[0][2],u++,i[0]=["M",o,s]),3==t.length&&"M"==t[0][0]&&"R"==t[1][0].toUpperCase()&&"Z"==t[2][0].toUpperCase()),h=u,f=t.length;h<f;h++){if(i.push(n=[]),(r=t[h])[0]!=Q.call(r[0]))switch(n[0]=Q.call(r[0]),n[0]){case"A":n[1]=r[1],n[2]=r[2],n[3]=r[3],n[4]=r[4],n[5]=r[5],n[6]=+(r[6]+o),n[7]=+(r[7]+s);break;case"V":n[1]=+r[1]+s;break;case"H":n[1]=+r[1]+o;break;case"R":for(var p=[o,s][A](r.slice(1)),d=2,m=p.length;d<m;d++)p[d]=+p[d]+o,p[++d]=+p[d]+s;i.pop(),i=i[A](kt(p,c));break;case"M":a=+r[1]+o,l=+r[2]+s;default:for(d=1,m=r.length;d<m;d++)n[d]=+r[d]+(d%2?o:s)}else if("R"==r[0])p=[o,s][A](r.slice(1)),i.pop(),i=i[A](kt(p,c)),n=["R"][A](r.slice(-2));else for(var g=0,y=r.length;g<y;g++)n[g]=r[g];switch(n[0]){case"Z":o=a,s=l;break;case"H":o=n[1];break;case"V":s=n[1];break;case"M":a=n[n.length-2],l=n[n.length-1];default:o=n[n.length-2],s=n[n.length-1]}}return i.toString=O._path2string,e.abs=H(i),i},At=function(t,e,n,r,i,o,s,a,l,u){var c=120*T/180,h=T/180*(+i||0),f=[],p=D(function(t,e,n){return{x:t*S.cos(n)-e*S.sin(n),y:t*S.sin(n)+e*S.cos(n)}}),o=(u?(x=u[0],b=u[1],y=u[2],v=u[3]):(t=(m=p(t,e,-h)).x,e=m.y,a=(m=p(a,l,-h)).x,l=m.y,S.cos(T/180*i),S.sin(T/180*i),1<(d=(m=(t-a)/2)*m/(n*n)+(g=(e-l)/2)*g/(r*r))&&(n*=d=S.sqrt(d),r*=d),y=(d=(o==s?-1:1)*S.sqrt(P(((d=n*n)*(o=r*r)-d*g*g-o*m*m)/(d*g*g+o*m*m))))*n*g/r+(t+a)/2,x=S.asin(((e-(v=d*-r*m/n+(e+l)/2))/r).toFixed(9)),b=S.asin(((l-v)/r).toFixed(9)),(x=t<y?T-x:x)<0&&(x=2*T+x),(b=a<y?T-b:b)<0&&(b=2*T+b),s&&b<x&&(x-=2*T),!s&&x<b&&(b-=2*T)),b-x),c=(P(o)>c&&(g=b,d=a,m=l,a=y+n*S.cos(b=x+c*(s&&x<b?1:-1)),l=v+r*S.sin(b),f=At(a,l,n,r,i,0,s,d,m,[b,g,y,v])),o=b-x,S.cos(x)),i=S.sin(x),s=S.cos(b),d=S.sin(b),m=S.tan(o/4),g=4/3*n*m,y=4/3*r*m,v=[t,e],x=[t+g*i,e-y*c],b=[a+g*d,l-y*s],o=[a,l];if(x[0]=2*v[0]-x[0],x[1]=2*v[1]-x[1],u)return[x,b,o][A](f);for(var _=[],w=0,k=(f=[x,b,o][A](f).join()[j](",")).length;w<k;w++)_[w]=w%2?p(f[w-1],f[w],h).y:p(f[w],f[w+1],h).x;return _},Lt=D(function(t,e,n,r,i,o,s,a){var l,u=i-2*n+t-(s-2*i+n),c=2*(n-t)-2*(i-n),h=t-n,f=(-c+S.sqrt(c*c-4*u*h))/2/u,p=(-c-S.sqrt(c*c-4*u*h))/2/u,d=[e,a],m=[t,s];return"1e12"<P(f)&&(f=.5),"1e12"<P(p)&&(p=.5),0<f&&f<1&&(l=Mt(t,e,n,r,i,o,s,a,f),m.push(l.x),d.push(l.y)),0<p&&p<1&&(l=Mt(t,e,n,r,i,o,s,a,p),m.push(l.x),d.push(l.y)),f=(-(c=2*(r-e)-2*(o-r))+S.sqrt(c*c-4*(u=o-2*r+e-(a-2*o+r))*(h=e-r)))/2/u,p=(-c-S.sqrt(c*c-4*u*h))/2/u,"1e12"<P(f)&&(f=.5),"1e12"<P(p)&&(p=.5),0<f&&f<1&&(l=Mt(t,e,n,r,i,o,s,a,f),m.push(l.x),d.push(l.y)),0<p&&p<1&&(l=Mt(t,e,n,r,i,o,s,a,p),m.push(l.x),d.push(l.y)),{min:{x:C[b](0,m),y:C[b](0,d)},max:{x:k[b](0,m),y:k[b](0,d)}}}),jt=O._path2curve=D(function(t,e){var n=!e&&M(t);if(!e&&n.curve)return H(n.curve);function r(t,e,n){var r,i;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(t[0]in{T:1,Q:1}||(e.qx=e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":t=["C"][A](At[b](0,[e.x,e.y][A](t.slice(1))));break;case"S":i="C"==n||"S"==n?(r=2*e.x-e.bx,2*e.y-e.by):(r=e.x,e.y),t=["C",r,i][A](t.slice(1));break;case"T":"Q"==n||"T"==n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),t=["C"][A](Dt(e.x,e.y,e.qx,e.qy,t[1],t[2]));break;case"Q":e.qx=t[1],e.qy=t[2],t=["C"][A](Dt(e.x,e.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"][A](Tt(e.x,e.y,t[1],t[2]));break;case"H":t=["C"][A](Tt(e.x,e.y,t[1],e.y));break;case"V":t=["C"][A](Tt(e.x,e.y,e.x,t[1]));break;case"Z":t=["C"][A](Tt(e.x,e.y,e.X,e.Y))}return t}function i(t,e){if(7<t[e].length){t[e].shift();for(var n=t[e];n.length;)c[e]="A",a&&(h[e]="A"),t.splice(e++,0,["C"][A](n.splice(0,6)));t.splice(e,1),m=k(s.length,a&&a.length||0)}}function o(t,e,n,r,i){t&&e&&"M"==t[i][0]&&"M"!=e[i][0]&&(e.splice(i,0,["M",r.x,r.y]),n.bx=0,n.by=0,n.x=t[i][1],n.y=t[i][2],m=k(s.length,a&&a.length||0))}for(var s=Nt(t),a=e&&Nt(e),l={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},u={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},c=[],h=[],f="",p="",d=0,m=k(s.length,a&&a.length||0);d<m;d++){"C"!=(f=s[d]?s[d][0]:f)&&(c[d]=f,d&&(p=c[d-1])),s[d]=r(s[d],l,p),"A"!=c[d]&&"C"==f&&(c[d]="C"),i(s,d),a&&("C"!=(f=a[d]?a[d][0]:f)&&(h[d]=f,d&&(p=h[d-1])),a[d]=r(a[d],u,p),"A"!=h[d]&&"C"==f&&(h[d]="C"),i(a,d)),o(s,a,l,u,d),o(a,s,u,l,d);var g=s[d],y=a&&a[d],v=g.length,x=a&&y.length;l.x=g[v-2],l.y=g[v-1],l.bx=R(g[v-4])||l.x,l.by=R(g[v-3])||l.y,u.bx=a&&(R(y[x-4])||u.x),u.by=a&&(R(y[x-3])||u.y),u.x=a&&y[x-2],u.y=a&&y[x-1]}return a||(n.curve=H(s)),a?[s,a]:s},null,H),Bt=(O._parseDots=D(function(t){for(var e=[],n=0,r=t.length;n<r;n++){var i={},o=t[n].match(/^([^:]*):?([\d\.]*)/);if(i.color=O.getRGB(o[1]),i.color.error)return null;i.opacity=i.color.opacity,i.color=i.color.hex,o[2]&&(i.offset=o[2]+"%"),e.push(i)}for(r=e.length-(n=1);n<r;n++)if(!e[n].offset){for(var s=R(e[n-1].offset||0),a=0,l=n+1;l<r;l++)if(e[l].offset){a=e[l].offset;break}a||(a=100,l=r);for(var u=((a=R(a))-s)/(l-n+1);n<l;n++)s+=u,e[n].offset=s+"%"}return e}),O._tear=function(t,e){t==e.top&&(e.top=t.prev),t==e.bottom&&(e.bottom=t.next),t.next&&(t.next.prev=t.prev),t.prev&&(t.prev.next=t.next)}),Rt=(O._tofront=function(t,e){e.top!==t&&(Bt(t,e),t.next=null,t.prev=e.top,e.top.next=t,e.top=t)},O._toback=function(t,e){e.bottom!==t&&(Bt(t,e),t.next=e.bottom,t.prev=null,e.bottom.prev=t,e.bottom=t)},O._insertafter=function(t,e,n){Bt(t,n),e==n.top&&(n.top=t),e.next&&(e.next.prev=t),t.next=e.next,(t.prev=e).next=t},O._insertbefore=function(t,e,n){Bt(t,n),e==n.bottom&&(n.bottom=t),e.prev&&(e.prev.next=t),t.prev=e.prev,(e.prev=t).next=e},O.toMatrix=function(t,e){var n=Ot(t),t={_:{transform:_},getBBox:function(){return n}};return It(t,e),t.matrix}),It=(O.transformPath=function(t,e){return ft(t,Rt(t,e))},O._extractTransform=function(t,e){if(null==e)return t._.transform;e=L(e).replace(/\.{3}|\u2026/g,t._.transform||_);var n,r=O.parseTransformString(e),i=0,o=1,s=1,a=t._,l=new Y;if(a.transform=r||[],r)for(var u=0,c=r.length;u<c;u++){var h,f,p,d,m,g=r[u],y=g.length,v=L(g[0]).toLowerCase(),x=g[0]!=v,b=x?l.invert():0;"t"==v&&3==y?x?(h=b.x(0,0),f=b.y(0,0),p=b.x(g[1],g[2]),d=b.y(g[1],g[2]),l.translate(p-h,d-f)):l.translate(g[1],g[2]):"r"==v?2==y?(m=m||t.getBBox(1),l.rotate(g[1],m.x+m.width/2,m.y+m.height/2),i+=g[1]):4==y&&(x?(p=b.x(g[2],g[3]),d=b.y(g[2],g[3]),l.rotate(g[1],p,d)):l.rotate(g[1],g[2],g[3]),i+=g[1]):"s"==v?2==y||3==y?(m=m||t.getBBox(1),l.scale(g[1],g[y-1],m.x+m.width/2,m.y+m.height/2),o*=g[1],s*=g[y-1]):5==y&&(x?(p=b.x(g[3],g[4]),d=b.y(g[3],g[4]),l.scale(g[1],g[2],p,d)):l.scale(g[1],g[2],g[3],g[4]),o*=g[1],s*=g[2]):"m"==v&&7==y&&l.add(g[1],g[2],g[3],g[4],g[5],g[6]),a.dirtyT=1,t.matrix=l}t.matrix=l,a.sx=o,a.sy=s,a.deg=i,a.dx=e=l.e,a.dy=n=l.f,1==o&&1==s&&!i&&a.bbox?(a.bbox.x+=+e,a.bbox.y+=+n):a.dirtyT=1}),Ht=O._equaliseTransform=function(t,e){e=L(e).replace(/\.{3}|\u2026/g,t),t=O.parseTransformString(t)||[],e=O.parseTransformString(e)||[];for(var n,r,i,o,s=k(t.length,e.length),a=[],l=[],u=0;u<s;u++){if(i=t[u]||Et(e[u]),o=e[u]||Et(i),i[0]!=o[0]||"r"==i[0].toLowerCase()&&(i[2]!=o[2]||i[3]!=o[3])||"s"==i[0].toLowerCase()&&(i[3]!=o[3]||i[4]!=o[4]))return;for(a[u]=[],l[u]=[],n=0,r=k(i.length,o.length);n<r;n++)n in i&&(a[u][n]=i[n]),n in o&&(l[u][n]=o[n])}return{from:a,to:l}};function Y(t,e,n,r,i,o){null!=t?(this.a=+t,this.b=+e,this.c=+n,this.d=+r,this.e=+i,this.f=+o):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0)}function Yt(t){return t[0]*t[0]+t[1]*t[1]}function Ft(t){var e=S.sqrt(Yt(t));t[0]&&(t[0]/=e),t[1]&&(t[1]/=e)}O._getContainer=function(t,e,n,r){var i=null!=r||O.is(t,"object")?t:d.doc.getElementById(t);if(null!=i)return i.tagName?null==e?{container:i,width:i.style.pixelWidth||i.offsetWidth,height:i.style.pixelHeight||i.offsetHeight}:{container:i,width:e,height:n}:{container:1,x:t,y:e,width:n,height:r}},O.pathToRelative=e,O._engine={},O.path2curve=jt,O.matrix=function(t,e,n,r,i,o){return new Y(t,e,n,r,i,o)},(e=Y.prototype).add=function(t,e,n,r,i,o){var s,a,l,u,c=[[],[],[]],h=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],f=[[t,n,i],[e,r,o],[0,0,1]];for(t&&t instanceof Y&&(f=[[t.a,t.c,t.e],[t.b,t.d,t.f],[0,0,1]]),s=0;s<3;s++)for(a=0;a<3;a++){for(l=u=0;l<3;l++)u+=h[s][l]*f[l][a];c[s][a]=u}this.a=c[0][0],this.b=c[1][0],this.c=c[0][1],this.d=c[1][1],this.e=c[0][2],this.f=c[1][2]},e.invert=function(){var t=this,e=t.a*t.d-t.b*t.c;return new Y(t.d/e,-t.b/e,-t.c/e,t.a/e,(t.c*t.f-t.d*t.e)/e,(t.b*t.e-t.a*t.f)/e)},e.clone=function(){return new Y(this.a,this.b,this.c,this.d,this.e,this.f)},e.translate=function(t,e){this.add(1,0,0,1,t,e)},e.scale=function(t,e,n,r){null==e&&(e=t),(n||r)&&this.add(1,0,0,1,n,r),this.add(t,0,0,e,0,0),(n||r)&&this.add(1,0,0,1,-n,-r)},e.rotate=function(t,e,n){t=O.rad(t),e=e||0,n=n||0;var r=+S.cos(t).toFixed(9),t=+S.sin(t).toFixed(9);this.add(r,t,-t,r,e,n),this.add(1,0,0,1,-e,-n)},e.x=function(t,e){return t*this.a+e*this.c+this.e},e.y=function(t,e){return t*this.b+e*this.d+this.f},e.get=function(t){return+this[L.fromCharCode(97+t)].toFixed(4)},e.toString=function(){return O.svg?"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")":[this.get(0),this.get(2),this.get(1),this.get(3),0,0].join()},e.toFilter=function(){return"progid:DXImageTransform.Microsoft.Matrix(M11="+this.get(0)+", M12="+this.get(2)+", M21="+this.get(1)+", M22="+this.get(3)+", Dx="+this.get(4)+", Dy="+this.get(5)+", sizingmethod='auto expand')"},e.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},e.split=function(){var t={},e=(t.dx=this.e,t.dy=this.f,[[this.a,this.c],[this.b,this.d]]),n=(t.scalex=S.sqrt(Yt(e[0])),Ft(e[0]),t.shear=e[0][0]*e[1][0]+e[0][1]*e[1][1],e[1]=[e[1][0]-e[0][0]*t.shear,e[1][1]-e[0][1]*t.shear],t.scaley=S.sqrt(Yt(e[1])),Ft(e[1]),t.shear/=t.scaley,-e[0][1]),e=e[1][1];return e<0?(t.rotate=O.deg(S.acos(e)),n<0&&(t.rotate=360-t.rotate)):t.rotate=O.deg(S.asin(n)),t.isSimple=!(+t.shear.toFixed(9)||t.scalex.toFixed(9)!=t.scaley.toFixed(9)&&t.rotate),t.isSuperSimple=!+t.shear.toFixed(9)&&t.scalex.toFixed(9)==t.scaley.toFixed(9)&&!t.rotate,t.noRotation=!+t.shear.toFixed(9)&&!t.rotate,t},e.toTransformString=function(t){t=t||this[j]();return t.isSimple?(t.scalex=+t.scalex.toFixed(4),t.scaley=+t.scaley.toFixed(4),t.rotate=+t.rotate.toFixed(4),(t.dx||t.dy?"t"+[t.dx,t.dy]:_)+(1!=t.scalex||1!=t.scaley?"s"+[t.scalex,t.scaley,0,0]:_)+(t.rotate?"r"+[t.rotate,0,0]:_)):"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]};function Wt(){this.returnValue=!1}function qt(){return this.originalEvent.preventDefault()}function Vt(){this.cancelBubble=!0}function $t(){return this.originalEvent.stopPropagation()}function Ut(t){var e=d.doc.documentElement.scrollTop||d.doc.body.scrollTop,n=d.doc.documentElement.scrollLeft||d.doc.body.scrollLeft;return{x:t.clientX+n,y:t.clientY+e}}for(var zt=d.doc.addEventListener?function(o,t,s,a){function e(t){var e=Ut(t);return s.call(a,t,e.x,e.y)}var n;return o.addEventListener(t,e,!1),q&&$[t]&&(n=function(t){for(var e=Ut(t),n=t,r=0,i=t.targetTouches&&t.targetTouches.length;r<i;r++)if(t.targetTouches[r].target==o){(t=t.targetTouches[r]).originalEvent=n,t.preventDefault=qt,t.stopPropagation=$t;break}return s.call(a,t,e.x,e.y)},o.addEventListener($[t],n,!1)),function(){return o.removeEventListener(t,e,!1),q&&$[t]&&o.removeEventListener($[t],n,!1),!0}}:d.doc.attachEvent?function(t,e,r,i){function n(t){t=t||d.win.event;var e=d.doc.documentElement.scrollTop||d.doc.body.scrollTop,n=d.doc.documentElement.scrollLeft||d.doc.body.scrollLeft,n=t.clientX+n,e=t.clientY+e;return t.preventDefault=t.preventDefault||Wt,t.stopPropagation=t.stopPropagation||Vt,r.call(i,t,n,e)}t.attachEvent("on"+e,n);return function(){return t.detachEvent("on"+e,n),!0}}:void 0,m=[],Gt=function(t){for(var e,n=t.clientX,r=t.clientY,i=d.doc.documentElement.scrollTop||d.doc.body.scrollTop,o=d.doc.documentElement.scrollLeft||d.doc.body.scrollLeft,s=m.length;s--;){if(e=m[s],q&&t.touches){for(var a,l=t.touches.length;l--;)if((a=t.touches[l]).identifier==e.el._drag.id){n=a.clientX,r=a.clientY,(t.originalEvent||t).preventDefault();break}}else t.preventDefault();var u,c=e.el.node,h=c.nextSibling,f=c.parentNode,p=c.style.display;d.win.opera&&f.removeChild(c),c.style.display="none",u=e.el.paper.getElementByPoint(n,r),c.style.display=p,d.win.opera&&(h?f.insertBefore(c,h):f.appendChild(c)),u&&E("raphael.drag.over."+e.el.id,e.el,u),n+=o,r+=i,E("raphael.drag.move."+e.el.id,e.move_scope||e.el,n-e.el._drag.x,r-e.el._drag.y,n,r,t)}},Xt=function(t){O.unmousemove(Gt).unmouseup(Xt);for(var e,n=m.length;n--;)(e=m[n]).el._drag={},E("raphael.drag.end."+e.el.id,e.end_scope||e.start_scope||e.move_scope||e.el,t);m=[]},o=O.el={},Zt=V.length;Zt--;)!function(r){O[r]=o[r]=function(t,e){return O.is(t,"function")&&(this.events=this.events||[],this.events.push({name:r,f:t,unbind:zt(this.shape||this.node||d.doc,r,t,e||this)})),this},O["un"+r]=o["un"+r]=function(t){for(var e=this.events||[],n=e.length;n--;)e[n].name!=r||!O.is(t,"undefined")&&e[n].f!=t||(e[n].unbind(),e.splice(n,1),e.length||delete this.events);return this}}(V[Zt]);o.data=function(t,e){var n=i[this.id]=i[this.id]||{};if(0==arguments.length)return n;if(1!=arguments.length)return n[t]=e,E("raphael.data.set."+this.id,this,e,t),this;if(O.is(t,"object")){for(var r in t)t[N](r)&&this.data(r,t[r]);return this}return E("raphael.data.get."+this.id,this,n[t],t),n[t]},o.removeData=function(t){return null==t?delete i[this.id]:i[this.id]&&delete i[this.id][t],this},o.getData=function(){return pt(i[this.id]||{})},o.hover=function(t,e,n,r){return this.mouseover(t,n).mouseout(e,r||n)},o.unhover=function(t,e){return this.unmouseover(t).unmouseout(e)};var n=[];o.drag=function(a,l,u,c,h,f){function t(t){(t.originalEvent||t).preventDefault();var e=t.clientX,n=t.clientY,r=d.doc.documentElement.scrollTop||d.doc.body.scrollTop,i=d.doc.documentElement.scrollLeft||d.doc.body.scrollLeft;if(this._drag.id=t.identifier,q&&t.touches)for(var o,s=t.touches.length;s--;)if(o=t.touches[s],this._drag.id=o.identifier,o.identifier==this._drag.id){e=o.clientX,n=o.clientY;break}this._drag.x=e+i,this._drag.y=n+r,m.length||O.mousemove(Gt).mouseup(Xt),m.push({el:this,move_scope:c,start_scope:h,end_scope:f}),l&&E.on("raphael.drag.start."+this.id,l),a&&E.on("raphael.drag.move."+this.id,a),u&&E.on("raphael.drag.end."+this.id,u),E("raphael.drag.start."+this.id,h||c||this,this._drag.x,this._drag.y,t)}return this._drag={},n.push({el:this,start:t}),this.mousedown(t),this},o.onDragOver=function(t){t?E.on("raphael.drag.over."+this.id,t):E.unbind("raphael.drag.over."+this.id)},o.undrag=function(){for(var t=n.length;t--;)n[t].el==this&&(this.unmousedown(n[t].start),n.splice(t,1),E.unbind("raphael.drag.*."+this.id));n.length||O.unmousemove(Gt).unmouseup(Xt),m=[]},t.circle=function(t,e,n){t=O._engine.circle(this,t||0,e||0,n||0);return this.__set__&&this.__set__.push(t),t},t.rect=function(t,e,n,r,i){t=O._engine.rect(this,t||0,e||0,n||0,r||0,i||0);return this.__set__&&this.__set__.push(t),t},t.ellipse=function(t,e,n,r){t=O._engine.ellipse(this,t||0,e||0,n||0,r||0);return this.__set__&&this.__set__.push(t),t},t.path=function(t){!t||O.is(t,z)||O.is(t[0],v)||(t+=_);var e=O._engine.path(O.format[b](O,arguments),this);return this.__set__&&this.__set__.push(e),e},t.image=function(t,e,n,r,i){t=O._engine.image(this,t||"about:blank",e||0,n||0,r||0,i||0);return this.__set__&&this.__set__.push(t),t},t.text=function(t,e,n){t=O._engine.text(this,t||0,e||0,L(n));return this.__set__&&this.__set__.push(t),t},t.set=function(t){O.is(t,"array")||(t=Array.prototype.splice.call(arguments,0,arguments.length));var e=new de(t);return this.__set__&&this.__set__.push(e),e.paper=this,e.type="set",e},t.setStart=function(t){this.__set__=t||this.set()},t.setFinish=function(t){var e=this.__set__;return delete this.__set__,e},t.getSize=function(){var t=this.canvas.parentNode;return{width:t.offsetWidth,height:t.offsetHeight}},t.setSize=function(t,e){return O._engine.setSize.call(this,t,e)},t.setViewBox=function(t,e,n,r,i){return O._engine.setViewBox.call(this,t,e,n,r,i)},t.top=t.bottom=null,t.raphael=O;function Kt(){return this.x+w+this.y+w+this.width+" × "+this.height}t.getElementByPoint=function(t,e){var n,r,i,o,s,a=this.canvas,l=d.doc.elementFromPoint(t,e);if(d.win.opera&&"svg"==l.tagName&&(r=(n=a).getBoundingClientRect(),n=n.ownerDocument,i=n.body,n=n.documentElement,o=n.clientTop||i.clientTop||0,s=n.clientLeft||i.clientLeft||0,o={y:r.top+(d.win.pageYOffset||n.scrollTop||i.scrollTop)-o,x:r.left+(d.win.pageXOffset||n.scrollLeft||i.scrollLeft)-s},(r=a.createSVGRect()).x=t-o.x,r.y=e-o.y,r.width=r.height=1,(n=a.getIntersectionList(r,null)).length&&(l=n[n.length-1])),!l)return null;for(;l.parentNode&&l!=a.parentNode&&!l.raphael;)l=l.parentNode;return l=(l=l==this.canvas.parentNode?a:l)&&l.raphael?this.getById(l.raphaelid):null},t.getElementsByBBox=function(e){var n=this.set();return this.forEach(function(t){O.isBBoxIntersect(t.getBBox(),e)&&n.push(t)}),n},t.getById=function(t){for(var e=this.bottom;e;){if(e.id==t)return e;e=e.next}return null},t.forEach=function(t,e){for(var n=this.bottom;n;){if(!1===t.call(e,n))return this;n=n.next}return this},t.getElementsByPoint=function(e,n){var r=this.set();return this.forEach(function(t){t.isPointInside(e,n)&&r.push(t)}),r},o.isPointInside=function(t,e){var n=this.realPath=ht[this.type](this);return this.attr("transform")&&this.attr("transform").length&&(n=O.transformPath(n,this.attr("transform"))),O.isPointInsidePath(n,t,e)},o.getBBox=function(t){if(this.removed)return{};var e=this._;return t?(!e.dirty&&e.bboxwt||(this.realPath=ht[this.type](this),e.bboxwt=Ot(this.realPath),e.bboxwt.toString=Kt,e.dirty=0),e.bboxwt):(!e.dirty&&!e.dirtyT&&e.bbox||(!e.dirty&&this.realPath||(e.bboxwt=0,this.realPath=ht[this.type](this)),e.bbox=Ot(ft(this.realPath,this.matrix)),e.bbox.toString=Kt,e.dirty=e.dirtyT=0),e.bbox)},o.clone=function(){if(this.removed)return null;var t=this.paper[this.type]().attr(this.attr());return this.__set__&&this.__set__.push(t),t},o.glow=function(t){if("text"==this.type)return null;for(var e={width:((t=t||{}).width||10)+(+this.attr("stroke-width")||1),fill:t.fill||!1,opacity:null==t.opacity?.5:t.opacity,offsetx:t.offsetx||0,offsety:t.offsety||0,color:t.color||"#000"},n=e.width/2,r=this.paper,i=r.set(),o=this.realPath||ht[this.type](this),o=this.matrix?ft(o,this.matrix):o,s=1;s<1+n;s++)i.push(r.path(o).attr({stroke:e.color,fill:e.fill?e.color:"none","stroke-linejoin":"round","stroke-linecap":"round","stroke-width":+(e.width/n*s).toFixed(3),opacity:+(e.opacity/n).toFixed(3)}));return i.insertBefore(this).translate(e.offsetx,e.offsety)};function Jt(p,d){return function(t,e,n){for(var r,i,o,s,a,l="",u={},c=0,h=0,f=(t=jt(t)).length;h<f;h++){if("M"==(o=t[h])[0])r=+o[1],i=+o[2];else{if(e<c+(s=Qt(r,i,o[1],o[2],o[3],o[4],o[5],o[6]))){if(d&&!u.start){if(l+=["C"+(a=Qt(r,i,o[1],o[2],o[3],o[4],o[5],o[6],e-c)).start.x,a.start.y,a.m.x,a.m.y,a.x,a.y],n)return l;u.start=l,l=["M"+a.x,a.y+"C"+a.n.x,a.n.y,a.end.x,a.end.y,o[5],o[6]].join(),c+=s,r=+o[5],i=+o[6];continue}if(!p&&!d)return{x:(a=Qt(r,i,o[1],o[2],o[3],o[4],o[5],o[6],e-c)).x,y:a.y,alpha:a.alpha}}c+=s,r=+o[5],i=+o[6]}l+=o.shift()+o}return u.end=l,a=(a=p?c:d?u:O.findDotsAtSegment(r,i,o[0],o[1],o[2],o[3],o[4],o[5],1)).alpha?{x:a.x,y:a.y,alpha:a.alpha}:a}}var Qt=function(t,e,n,r,i,o,s,a,l){return null==l?I(t,e,n,r,i,o,s,a):O.findDotsAtSegment(t,e,n,r,i,o,s,a,function(t,e,n,r,i,o,s,a,l){if(!(l<0||I(t,e,n,r,i,o,s,a)<l)){for(var u=.5,c=1-u,h=I(t,e,n,r,i,o,s,a,c);.01<P(h-l);)h=I(t,e,n,r,i,o,s,a,c+=(h<l?1:-1)*(u/=2));return c}}(t,e,n,r,i,o,s,a,l))},te=Jt(1),ee=Jt(),ne=Jt(0,1),r=(O.getTotalLength=te,O.getPointAtLength=ee,O.getSubpath=function(t,e,n){if(this.getTotalLength(t)-n<1e-6)return ne(t,e).end;t=ne(t,n,1);return e?ne(t,e).end:t},o.getTotalLength=function(){var t=this.getPath();if(t)return this.node.getTotalLength?this.node.getTotalLength():te(t)},o.getPointAtLength=function(t){var e=this.getPath();if(e)return ee(e,t)},o.getPath=function(){var t,e=O._getPath[this.type];if("text"!=this.type&&"set"!=this.type)return t=e?e(this):t},o.getSubpath=function(t,e){var n=this.getPath();if(n)return O.getSubpath(n,t,e)},O.easing_formulas={linear:function(t){return t},"<":function(t){return g(t,1.7)},">":function(t){return g(t,.48)},"<>":function(t){var t=.48-t/1.04,e=S.sqrt(.1734+t*t),n=e-t,e=-e-t,t=g(P(n),1/3)*(n<0?-1:1)+g(P(e),1/3)*(e<0?-1:1)+.5;return 3*(1-t)*t*t+t*t*t},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return(t-=1)*t*(2.70158*t+1.70158)+1},elastic:function(t){return t==!!t?t:g(2,-10*t)*S.sin(2*T*(t-.075)/.3)+1},bounce:function(t){var e=7.5625,e=t<1/2.75?e*t*t:t<2/2.75?e*(t-=1.5/2.75)*t+.75:t<2.5/2.75?e*(t-=2.25/2.75)*t+.9375:e*(t-=2.625/2.75)*t+.984375;return e}}),F=(r.easeIn=r["ease-in"]=r["<"],r.easeOut=r["ease-out"]=r[">"],r.easeInOut=r["ease-in-out"]=r["<>"],r["back-in"]=r.backIn,r["back-out"]=r.backOut,[]),re=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){setTimeout(t,16)},ie=function(){for(var t=+new Date,e=0;e<F.length;e++){var n=F[e];if(!n.el.removed&&!n.paused){var r,i=t-n.start,o=n.ms,s=n.easing,a=n.from,l=n.diff,u=n.to,c=(n.t,n.el),h={},f={};if(n.initstatus?(i=(n.initstatus*n.anim.top-n.prev)/(n.percent-n.prev)*o,n.status=n.initstatus,delete n.initstatus,n.stop&&F.splice(e--,1)):n.status=(n.prev+(n.percent-n.prev)*(i/o))/n.anim.top,!(i<0))if(i<o){var p,d=s(i/o);for(p in a)if(a[N](p)){switch(et[p]){case B:m=+a[p]+d*o*l[p];break;case"colour":m="rgb("+[oe(J(a[p].r+d*o*l[p].r)),oe(J(a[p].g+d*o*l[p].g)),oe(J(a[p].b+d*o*l[p].b))].join(",")+")";break;case"path":for(var m=[],g=0,y=a[p].length;g<y;g++){m[g]=[a[p][g][0]];for(var v=1,x=a[p][g].length;v<x;v++)m[g][v]=+a[p][g][v]+d*o*l[p][g][v];m[g]=m[g].join(w)}m=m.join(w);break;case"transform":if(l[p].real)for(m=[],g=0,y=a[p].length;g<y;g++)for(m[g]=[a[p][g][0]],v=1,x=a[p][g].length;v<x;v++)m[g][v]=a[p][g][v]+d*o*l[p][g][v];else{function b(t){return+a[p][t]+d*o*l[p][t]}m=[["m",b(0),b(1),b(2),b(3),b(4),b(5)]]}break;case"csv":if("clip-rect"==p)for(m=[],g=4;g--;)m[g]=+a[p][g]+d*o*l[p][g];break;default:var _=[][A](a[p]);for(m=[],g=c.paper.customAttributes[p].length;g--;)m[g]=+_[g]+d*o*l[p][g]}h[p]=m}c.attr(h),!function(t,e,n){setTimeout(function(){E("raphael.anim.frame."+t,e,n)})}(c.id,c,n.anim)}else{if(!function(t,e,n){setTimeout(function(){E("raphael.anim.frame."+e.id,e,n),E("raphael.anim.finish."+e.id,e,n),O.is(t,"function")&&t.call(e)})}(n.callback,c,n.anim),c.attr(u),F.splice(e--,1),1<n.repeat&&!n.next){for(r in u)u[N](r)&&(f[r]=n.totalOrigin[r]);n.el.attr(f),ae(n.anim,n.el,n.anim.percents[0],null,n.totalOrigin,n.repeat-1)}n.next&&!n.stop&&ae(n.anim,n.el,n.next,null,n.totalOrigin,n.repeat)}}}F.length&&re(ie)},oe=function(t){return 255<t?255:t<0?0:t};function se(t,e,n,r,i,o){var l=3*e,u=3*(r-e)-l,c=1-l-u,r=3*n,e=3*(i-n)-r,i=1-r-e;function h(t){return((c*t+u)*t+l)*t}return n=function(t,e){var n,r,i,o,s,a;for(i=t,a=0;a<8;a++){if(o=h(i)-t,P(o)<e)return i;if(P(s=(3*c*i+2*u)*i+l)<1e-6)break;i-=o/s}if(r=1,(i=t)<(n=0))return n;if(r<i)return r;for(;n<r;){if(o=h(i),P(o-t)<e)return i;o<t?n=i:r=i,i=(r-n)/2+n}return i}(n=t,1/(200*o)),((i*n+e)*n+r)*n}function h(t,e){var n=[],r={};if(this.ms=e,this.times=1,t){for(var i in t)t[N](i)&&(r[R(i)]=t[i],n.push(R(i)));n.sort(lt)}this.anim=r,this.top=n[n.length-1],this.percents=n}function ae(t,e,n,r,i,o){n=R(n);var s,a,l,u,c,h=t.ms,f={},p={},d={};if(r)for(g=0,y=F.length;g<y;g++){var m=F[g];if(m.el.id==e.id&&m.anim==t){m.percent!=n?(F.splice(g,1),l=1):a=m,e.attr(m.totalOrigin);break}}else r=+p;for(var g=0,y=t.percents.length;g<y;g++){if(t.percents[g]==n||t.percents[g]>r*t.top){n=t.percents[g],c=t.percents[g-1]||0,h=h/t.top*(n-c),u=t.percents[g+1],s=t.anim[n];break}r&&e.attr(t.anim[t.percents[g]])}if(s){if(a)a.initstatus=r,a.start=new Date-a.ms*r;else{for(var v in s)if(s[N](v)&&(et[N](v)||e.paper.customAttributes[N](v)))switch(f[v]=e.attr(v),null==f[v]&&(f[v]=tt[v]),p[v]=s[v],et[v]){case B:d[v]=(p[v]-f[v])/h;break;case"colour":f[v]=O.getRGB(f[v]);var x=O.getRGB(p[v]);d[v]={r:(x.r-f[v].r)/h,g:(x.g-f[v].g)/h,b:(x.b-f[v].b)/h};break;case"path":var x=jt(f[v],p[v]),b=x[1];for(f[v]=x[0],d[v]=[],g=0,y=f[v].length;g<y;g++){d[v][g]=[0];for(var _=1,w=f[v][g].length;_<w;_++)d[v][g][_]=(b[g][_]-f[v][g][_])/h}break;case"transform":var k=e._,S=Ht(k[v],p[v]);if(S)for(f[v]=S.from,p[v]=S.to,d[v]=[],d[v].real=!0,g=0,y=f[v].length;g<y;g++)for(d[v][g]=[f[v][g][0]],_=1,w=f[v][g].length;_<w;_++)d[v][g][_]=(p[v][g][_]-f[v][g][_])/h;else{S=e.matrix||new Y,k={_:{transform:k.transform},getBBox:function(){return e.getBBox(1)}};f[v]=[S.a,S.b,S.c,S.d,S.e,S.f],It(k,p[v]),p[v]=k._.transform,d[v]=[(k.matrix.a-S.a)/h,(k.matrix.b-S.b)/h,(k.matrix.c-S.c)/h,(k.matrix.d-S.d)/h,(k.matrix.e-S.e)/h,(k.matrix.f-S.f)/h]}break;case"csv":var C=L(s[v])[j](W),P=L(f[v])[j](W);if("clip-rect"==v)for(f[v]=P,d[v]=[],g=P.length;g--;)d[v][g]=(C[g]-f[v][g])/h;p[v]=C;break;default:for(C=[][A](s[v]),P=[][A](f[v]),d[v]=[],g=e.paper.customAttributes[v].length;g--;)d[v][g]=((C[g]||0)-(P[g]||0))/h}var T,D=s.easing,M=O.easing_formulas[D];if(M=M||((M=L(D).match(K))&&5==M.length?(T=M,function(t){return se(t,+T[1],+T[2],+T[3],+T[4],h)}):ut),m={anim:t,percent:n,timestamp:D=s.start||t.start||+new Date,start:D+(t.del||0),status:0,initstatus:r||0,stop:!1,ms:h,easing:M,from:f,diff:d,to:p,el:e,callback:s.callback,prev:c,next:u,repeat:o||t.times,origin:e.attr(),totalOrigin:i},F.push(m),r&&!a&&!l&&(m.stop=!0,m.start=new Date-h*r,1==F.length))return ie();l&&(m.start=new Date-m.ms*r),1==F.length&&re(ie)}E("raphael.anim.start."+e.id,e,t)}}function le(t){for(var e=0;e<F.length;e++)F[e].el.paper==t&&F.splice(e--,1)}o.animateWith=function(t,e,n,r,i,o){if(this.removed)return o&&o.call(this),this;n=n instanceof h?n:O.animation(n,r,i,o);ae(n,this,n.percents[0],null,this.attr());for(var s=0,a=F.length;s<a;s++)if(F[s].anim==e&&F[s].el==t){F[a-1].start=F[s].start;break}return this},o.onAnimation=function(t){return t?E.on("raphael.anim.frame."+this.id,t):E.unbind("raphael.anim.frame."+this.id),this},h.prototype.delay=function(t){var e=new h(this.anim,this.ms);return e.times=this.times,e.del=+t||0,e},h.prototype.repeat=function(t){var e=new h(this.anim,this.ms);return e.del=this.del,e.times=S.floor(k(t,0))||1,e},O.animation=function(t,e,n,r){if(t instanceof h)return t;!O.is(n,"function")&&n||(r=r||n||null,n=null),e=+e||0;var i,o,s={};for(o in t=Object(t))t[N](o)&&R(o)!=o&&R(o)+"%"!=o&&(i=!0,s[o]=t[o]);if(i)return n&&(s.easing=n),r&&(s.callback=r),new h({100:s},e);if(r){var a,l=0;for(a in t){var u=c(a);t[N](a)&&l<u&&(l=u)}t[l+="%"].callback||(t[l].callback=r)}return new h(t,e)},o.animate=function(t,e,n,r){if(this.removed)return r&&r.call(this),this;t=t instanceof h?t:O.animation(t,e,n,r);return ae(t,this,t.percents[0],null,this.attr()),this},o.setTime=function(t,e){return t&&null!=e&&this.status(t,C(e,t.ms)/t.ms),this},o.status=function(t,e){var n,r,i=[],o=0;if(null!=e)return ae(t,this,-1,C(e,1)),this;for(n=F.length;o<n;o++)if((r=F[o]).el.id==this.id&&(!t||r.anim==t)){if(t)return r.status;i.push({anim:r.anim,status:r.status})}return t?0:i},o.pause=function(t){for(var e=0;e<F.length;e++)F[e].el.id!=this.id||t&&F[e].anim!=t||!1!==E("raphael.anim.pause."+this.id,this,F[e].anim)&&(F[e].paused=!0);return this},o.resume=function(t){for(var e,n=0;n<F.length;n++)F[n].el.id!=this.id||t&&F[n].anim!=t||(e=F[n],!1!==E("raphael.anim.resume."+this.id,this,e.anim)&&(delete e.paused,this.status(e.anim,e.status)));return this},o.stop=function(t){for(var e=0;e<F.length;e++)F[e].el.id!=this.id||t&&F[e].anim!=t||!1!==E("raphael.anim.stop."+this.id,this,F[e].anim)&&F.splice(e--,1);return this},E.on("raphael.remove",le),E.on("raphael.clear",le),o.toString=function(){return"Raphaël’s object"};var ue,ce,he,s,fe,pe,de=function(t){if(this.items=[],this.length=0,this.type="set",t)for(var e=0,n=t.length;e<n;e++)!t[e]||t[e].constructor!=o.constructor&&t[e].constructor!=de||(this[this.items.length]=this.items[this.items.length]=t[e],this.length++)},a=de.prototype;for(ue in a.push=function(){for(var t,e,n=0,r=arguments.length;n<r;n++)!(t=arguments[n])||t.constructor!=o.constructor&&t.constructor!=de||(this[e=this.items.length]=this.items[e]=t,this.length++);return this},a.pop=function(){return this.length&&delete this[this.length--],this.items.pop()},a.forEach=function(t,e){for(var n=0,r=this.items.length;n<r;n++)if(!1===t.call(e,this.items[n],n))return this;return this},o)o[N](ue)&&(a[ue]=function(n){return function(){var e=arguments;return this.forEach(function(t){t[n][b](t,e)})}}(ue));return a.attr=function(t,e){if(t&&O.is(t,v)&&O.is(t[0],"object"))for(var n=0,r=t.length;n<r;n++)this.items[n].attr(t[n]);else for(var i=0,o=this.items.length;i<o;i++)this.items[i].attr(t,e);return this},a.clear=function(){for(;this.length;)this.pop()},a.splice=function(t,e,n){t=t<0?k(this.length+t,0):t,e=k(0,C(this.length-t,e));for(var r=[],i=[],o=[],s=2;s<arguments.length;s++)o.push(arguments[s]);for(s=0;s<e;s++)i.push(this[t+s]);for(;s<this.length-t;s++)r.push(this[t+s]);var a=o.length;for(s=0;s<a+r.length;s++)this.items[t+s]=this[t+s]=s<a?o[s]:r[s-a];for(s=this.items.length=this.length-=e-a;this[s];)delete this[s++];return new de(i)},a.exclude=function(t){for(var e=0,n=this.length;e<n;e++)if(this[e]==t)return this.splice(e,1),!0},a.animate=function(t,e,n,r){!O.is(n,"function")&&n||(r=n||null);var i,o=this.items.length,s=o,a=this;if(!o)return this;r&&(i=function(){--o||r.call(a)}),n=O.is(n,z)?n:i;for(var l=O.animation(t,e,n,i),u=this.items[--s].animate(l);s--;)this.items[s]&&!this.items[s].removed&&this.items[s].animateWith(u,l,l),this.items[s]&&!this.items[s].removed||o--;return this},a.insertAfter=function(t){for(var e=this.items.length;e--;)this.items[e].insertAfter(t);return this},a.getBBox=function(){for(var t,e=[],n=[],r=[],i=[],o=this.items.length;o--;)this.items[o].removed||(t=this.items[o].getBBox(),e.push(t.x),n.push(t.y),r.push(t.x+t.width),i.push(t.y+t.height));return{x:e=C[b](0,e),y:n=C[b](0,n),x2:r=k[b](0,r),y2:i=k[b](0,i),width:r-e,height:i-n}},a.clone=function(t){t=this.paper.set();for(var e=0,n=this.items.length;e<n;e++)t.push(this.items[e].clone());return t},a.toString=function(){return"Raphaël‘s set"},a.glow=function(n){var r=this.paper.set();return this.forEach(function(t,e){t=t.glow(n);null!=t&&t.forEach(function(t,e){r.push(t)})}),r},a.isPointInside=function(e,n){var r=!1;return this.forEach(function(t){if(t.isPointInside(e,n))return!(r=!0)}),r},O.registerFont=function(t){if(!t.face)return t;this.fonts=this.fonts||{};var e,n={w:t.w,face:{},glyphs:{}},r=t.face["font-family"];for(e in t.face)t.face[N](e)&&(n.face[e]=t.face[e]);if(this.fonts[r]?this.fonts[r].push(n):this.fonts[r]=[n],!t.svg)for(var i in n.face["units-per-em"]=c(t.face["units-per-em"],10),t.glyphs)if(t.glyphs[N](i)){var o=t.glyphs[i];if(n.glyphs[i]={w:o.w,k:{},d:o.d&&"M"+o.d.replace(/[mlcxtrv]/g,function(t){return{l:"L",c:"C",x:"z",t:"m",r:"l",v:"c"}[t]||"M"})+"z"},o.k)for(var s in o.k)o[N](s)&&(n.glyphs[i].k[s]=o.k[s])}return t},t.getFont=function(t,e,n,r){if(r=r||"normal",n=n||"normal",e=+e||{normal:400,bold:700,lighter:300,bolder:800}[e]||400,O.fonts){var i,o=O.fonts[t];if(!o){var s,a=new RegExp("(^|\\s)"+t.replace(/[^\w\d\s+!~.:_-]/g,_)+"(\\s|$)","i");for(s in O.fonts)if(O.fonts[N](s)&&a.test(s)){o=O.fonts[s];break}}if(o)for(var l=0,u=o.length;l<u&&((i=o[l]).face["font-weight"]!=e||i.face["font-style"]!=n&&i.face["font-style"]||i.face["font-stretch"]!=r);l++);return i}},t.print=function(t,e,n,r,i,o,s,a){o=o||"middle",s=k(C(s||0,1),-1),a=k(C(a||1,3),1);var l=L(n)[j](_),u=0,c=0,h=_;if(r=O.is(r,"string")?this.getFont(r):r)for(var f,p,d=(i||16)/r.face["units-per-em"],n=r.face.bbox[j](W),m=+n[0],g=n[3]-n[1],y=0,v=+n[1]+("baseline"==o?g+ +r.face.descent:g/2),x=0,b=l.length;x<b;x++)"\n"==l[x]?(c=p=u=0,y+=g*a):(f=c&&r.glyphs[l[x-1]]||{},p=r.glyphs[l[x]],u+=c?(f.w||r.w)+(f.k&&f.k[l[x]]||0)+r.w*s:0,c=1),p&&p.d&&(h+=O.transformPath(p.d,["t",u*d,y*d,"s",d,d,m,v,"t",(t-m)/d,(e-v)/d]));return this.path(h).attr({fill:"#000",stroke:"none"})},t.add=function(t){if(O.is(t,"array"))for(var e,n=this.set(),r=0,i=t.length;r<i;r++)e=t[r]||{},p[N](e.type)&&n.push(this[e.type]().attr(e));return n},O.format=function(t,e){var n=O.is(e,v)?[0][A](e):arguments;return(t=t&&O.is(t,z)&&n.length-1?t.replace(y,function(t,e){return null==n[++e]?_:n[e]}):t)||_},O.fullfill=(ce=/\{([^\}]+)\}/g,he=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,function(t,r){return String(t).replace(ce,function(t,e){return t=t,o=n=r,e.replace(he,function(t,e,n,r,i){e=e||r,o&&"function"==typeof(o=e in o?o[e]:o)&&i&&(o=o())}),o=(null==o||o==n?t:o)+"";var n,o})}),O.ninja=function(){if(x.was)d.win.Raphael=x.is;else{window.Raphael=void 0;try{delete window.Raphael}catch(t){}}return O},O.st=a,E.on("raphael.DOMload",function(){f=!0}),s=document,fe="DOMContentLoaded",null==s.readyState&&s.addEventListener&&(s.addEventListener(fe,pe=function(){s.removeEventListener(fe,pe,!1),s.readyState="complete"},!1),s.readyState="loading"),function t(){/in/.test(s.readyState)?setTimeout(t,9):O.eve("raphael.DOMload")}(),O}.apply(e,n);void 0!==e&&(t.exports=e)},"./dev/raphael.svg.js":function(t,e,n){n=[n("./dev/raphael.core.js")],e=function(T){if(!T||T.svg){function D(t,e){var r="linear",n=t.id+e,i=.5,o=.5,s=t.node,a=t.paper,l=s.style,u=T._g.doc.getElementById(n);if(!u){if(e=(e=A(e).replace(T._radial_gradient,function(t,e,n){return r="radial",e&&n&&(i=m(e),e=2*(.5<(o=m(n)))-1,.25<y(i-.5,2)+y(o-.5,2)&&(o=g.sqrt(.25-y(i-.5,2))*e+.5)&&.5!=o&&(o=o.toFixed(5)-1e-5*e)),I})).split(/\s*\-\s*/),"linear"==r){var c=e.shift(),c=-m(c);if(isNaN(c))return null;var c=[0,0,g.cos(T.rad(c)),g.sin(T.rad(c))],h=1/(j(B(c[2]),B(c[3]))||1);c[2]*=h,c[3]*=h,c[2]<0&&(c[0]=-c[2],c[2]=0),c[3]<0&&(c[1]=-c[3],c[3]=0)}var f=T._parseDots(e);if(!f)return null;if(n=n.replace(/[\(\)\s,\xb0#]/g,"_"),t.gradient&&n!=t.gradient.id&&(a.defs.removeChild(t.gradient),delete t.gradient),!t.gradient){u=Y(r+"Gradient",{id:n}),t.gradient=u,Y(u,"radial"==r?{fx:i,fy:o}:{x1:c[0],y1:c[1],x2:c[2],y2:c[3],gradientTransform:t.matrix.invert()}),a.defs.appendChild(u);for(var p=0,d=f.length;p<d;p++)u.appendChild(Y("stop",{offset:f[p].offset||(p?"100%":"0%"),"stop-color":f[p].color||"#fff","stop-opacity":isFinite(f[p].opacity)?f[p].opacity:1}))}}return Y(s,{fill:v(n),opacity:1,"fill-opacity":1}),l.fill=I,l.opacity=1,l.fillOpacity=1}function M(t){var e=t.getBBox(1);Y(t.pattern,{patternTransform:t.matrix.invert()+" translate("+e.x+","+e.y+")"})}function E(t,e,n){if("path"==t.type){for(var r,i,o,s,a,l,u,c,h,f=A(e).toLowerCase().split("-"),p=t.paper,d=n?"end":"start",m=t.node,g=t.attrs,y=g["stroke-width"],v=f.length,x="classic",b=3,_=3,w=5;v--;)switch(f[v]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":x=f[v];break;case"wide":_=5;break;case"narrow":_=2;break;case"long":b=5;break;case"short":b=2}for(c in c="open"==x?(b+=2,_+=2,w+=2,i=1,l=n?4:1,{fill:"none",stroke:g.stroke}):(l=i=b/2,{fill:g.stroke,stroke:"none"}),t._.arrows?n?(t._.arrows.endPath&&S[t._.arrows.endPath]--,t._.arrows.endMarker&&S[t._.arrows.endMarker]--):(t._.arrows.startPath&&S[t._.arrows.startPath]--,t._.arrows.startMarker&&S[t._.arrows.startMarker]--):t._.arrows={},"none"!=x?(s="raphael-marker-"+d+x+b+_+"-obj"+t.id,T._g.doc.getElementById(o="raphael-marker-"+x)?S[o]++:(p.defs.appendChild(Y(Y("path"),{"stroke-linecap":"round",d:k[x],id:o})),S[o]=1),(u=T._g.doc.getElementById(s))?(S[s]++,a=u.getElementsByTagName("use")[0]):(u=Y(Y("marker"),{id:s,markerHeight:_,markerWidth:b,orient:"auto",refX:l,refY:_/2}),a=Y(Y("use"),{"xlink:href":"#"+o,transform:(n?"rotate(180 "+b/2+" "+_/2+") ":I)+"scale("+b/w+","+_/w+")","stroke-width":(1/((b/w+_/w)/2)).toFixed(4)}),u.appendChild(a),p.defs.appendChild(u),S[s]=1),Y(a,c),l=i*("diamond"!=x&&"oval"!=x),u=n?(r=t._.arrows.startdx*y||0,T.getTotalLength(g.path)-l*y):(r=l*y,T.getTotalLength(g.path)-(t._.arrows.enddx*y||0)),(c={})["marker-"+d]="url(#"+s+")",(u||r)&&(c.d=T.getSubpath(g.path,r,u)),Y(m,c),t._.arrows[d+"Path"]=o,t._.arrows[d+"Marker"]=s,t._.arrows[d+"dx"]=l,t._.arrows[d+"Type"]=x,t._.arrows[d+"String"]=e):(u=n?(r=t._.arrows.startdx*y||0,T.getTotalLength(g.path)-r):(r=0,T.getTotalLength(g.path)-(t._.arrows.enddx*y||0)),t._.arrows[d+"Path"]&&Y(m,{d:T.getSubpath(g.path,r,u)}),delete t._.arrows[d+"Path"],delete t._.arrows[d+"Marker"],delete t._.arrows[d+"dx"],delete t._.arrows[d+"Type"],delete t._.arrows[d+"String"]),S)!S[N](c)||S[c]||(h=T._g.doc.getElementById(c))&&h.parentNode.removeChild(h)}}function O(t,e,n){if(e=l[A(e).toLowerCase()]){for(var r=t.attrs["stroke-width"]||"1",i={round:r,square:r,butt:0}[t.attrs["stroke-linecap"]||n["stroke-linecap"]]||0,o=[],s=e.length;s--;)o[s]=e[s]*r+(s%2?1:-1)*i;Y(t.node,{"stroke-dasharray":o.join(",")})}else Y(t.node,{"stroke-dasharray":"none"})}function f(t,e){var n,r=t.node,i=t.attrs,o=r.style.visibility;for(n in r.style.visibility="hidden",e)if(e[N](n)&&T._availableAttrs[N](n)){var s=e[n];switch(i[n]=s,n){case"blur":t.blur(s);break;case"title":var a=r.getElementsByTagName("title");a.length&&(a=a[0])?a.firstChild.nodeValue=s:(a=Y("title"),l=T._g.doc.createTextNode(s),a.appendChild(l),r.appendChild(a));break;case"href":case"target":var l=r.parentNode;"a"!=l.tagName.toLowerCase()&&(a=Y("a"),l.insertBefore(a,r),a.appendChild(r),l=a),"target"==n?l.setAttributeNS(H,"show","blank"==s?"new":s):l.setAttributeNS(H,n,s);break;case"cursor":r.style.cursor=s;break;case"transform":t.transform(s);break;case"arrow-start":E(t,s);break;case"arrow-end":E(t,s,1);break;case"clip-rect":var u,c=A(s).split(R);4==c.length&&(t.clip&&t.clip.parentNode.parentNode.removeChild(t.clip.parentNode),f=Y("clipPath"),u=Y("rect"),f.id=T.createUUID(),Y(u,{x:c[0],y:c[1],width:c[2],height:c[3]}),f.appendChild(u),t.paper.defs.appendChild(f),Y(r,{"clip-path":"url(#"+f.id+")"}),t.clip=u),s||(c=r.getAttribute("clip-path"))&&((u=T._g.doc.getElementById(c.replace(/(^url\(#|\)$)/g,I)))&&u.parentNode.removeChild(u),Y(r,{"clip-path":I}),delete t.clip);break;case"path":"path"==t.type&&(Y(r,{d:s?i.path=T._pathToAbsolute(s):"M0,0"}),t._.dirty=1,t._.arrows&&("startString"in t._.arrows&&E(t,t._.arrows.startString),"endString"in t._.arrows&&E(t,t._.arrows.endString,1)));break;case"width":if(r.setAttribute(n,s),t._.dirty=1,!i.fx)break;n="x",s=i.x;case"x":i.fx&&(s=-i.x-(i.width||0));case"rx":if("rx"==n&&"rect"==t.type)break;case"cx":r.setAttribute(n,s),t.pattern&&M(t),t._.dirty=1;break;case"height":if(r.setAttribute(n,s),t._.dirty=1,!i.fy)break;n="y",s=i.y;case"y":i.fy&&(s=-i.y-(i.height||0));case"ry":if("ry"==n&&"rect"==t.type)break;case"cy":r.setAttribute(n,s),t.pattern&&M(t),t._.dirty=1;break;case"r":"rect"==t.type?Y(r,{rx:s,ry:s}):r.setAttribute(n,s),t._.dirty=1;break;case"src":"image"==t.type&&r.setAttributeNS(H,"href",s);break;case"stroke-width":1==t._.sx&&1==t._.sy||(s/=j(B(t._.sx),B(t._.sy))||1),r.setAttribute(n,s),i["stroke-dasharray"]&&O(t,i["stroke-dasharray"],e),t._.arrows&&("startString"in t._.arrows&&E(t,t._.arrows.startString),"endString"in t._.arrows&&E(t,t._.arrows.endString,1));break;case"stroke-dasharray":O(t,s,e);break;case"fill":var h=A(s).match(T._ISURL);if(h){var f=Y("pattern"),p=Y("image");f.id=T.createUUID(),Y(f,{x:0,y:0,patternUnits:"userSpaceOnUse",height:1,width:1}),Y(p,{x:0,y:0,"xlink:href":h[1]}),f.appendChild(p),!function(n){T._preload(h[1],function(){var t=this.offsetWidth,e=this.offsetHeight;Y(n,{width:t,height:e}),Y(p,{width:t,height:e})})}(f),t.paper.defs.appendChild(f),Y(r,{fill:"url(#"+f.id+")"}),t.pattern=f,t.pattern&&M(t);break}var d,m,g=T.getRGB(s);if(g.error){if(("circle"==t.type||"ellipse"==t.type||"r"!=A(s).charAt())&&D(t,s)){!("opacity"in i||"fill-opacity"in i)||(d=T._g.doc.getElementById(r.getAttribute("fill").replace(/^url\(#|\)$/g,I)))&&(m=d.getElementsByTagName("stop"),Y(m[m.length-1],{"stop-opacity":("opacity"in i?i.opacity:1)*("fill-opacity"in i?i["fill-opacity"]:1)})),i.gradient=s,i.fill="none";break}}else delete e.gradient,delete i.gradient,!T.is(i.opacity,"undefined")&&T.is(e.opacity,"undefined")&&Y(r,{opacity:i.opacity}),!T.is(i["fill-opacity"],"undefined")&&T.is(e["fill-opacity"],"undefined")&&Y(r,{"fill-opacity":i["fill-opacity"]});g[N]("opacity")&&Y(r,{"fill-opacity":1<g.opacity?g.opacity/100:g.opacity});case"stroke":g=T.getRGB(s),r.setAttribute(n,g.hex),"stroke"==n&&g[N]("opacity")&&Y(r,{"stroke-opacity":1<g.opacity?g.opacity/100:g.opacity}),"stroke"==n&&t._.arrows&&("startString"in t._.arrows&&E(t,t._.arrows.startString),"endString"in t._.arrows&&E(t,t._.arrows.endString,1));break;case"gradient":"circle"!=t.type&&"ellipse"!=t.type&&"r"==A(s).charAt()||D(t,s);break;case"opacity":i.gradient&&!i[N]("stroke-opacity")&&Y(r,{"stroke-opacity":1<s?s/100:s});case"fill-opacity":if(i.gradient){(d=T._g.doc.getElementById(r.getAttribute("fill").replace(/^url\(#|\)$/g,I)))&&(m=d.getElementsByTagName("stop"),Y(m[m.length-1],{"stop-opacity":s}));break}default:"font-size"==n&&(s=L(s,10)+"px");c=n.replace(/(\-.)/g,function(t){return t.substring(1).toUpperCase()});r.style[c]=s,t._.dirty=1,r.setAttribute(n,s)}}var y=t,v=e;if("text"==y.type&&(v[N]("text")||v[N]("font")||v[N]("font-size")||v[N]("x")||v[N]("y"))){var x=y.attrs,b=y.node,_=b.firstChild?L(T._g.doc.defaultView.getComputedStyle(b.firstChild,I).getPropertyValue("font-size"),10):10;if(v[N]("text")){for(x.text=v.text;b.firstChild;)b.removeChild(b.firstChild);for(var w,k=A(v.text).split("\n"),S=[],C=0,P=k.length;C<P;C++)w=Y("tspan"),C&&Y(w,{dy:_*F,x:x.x}),w.appendChild(T._g.doc.createTextNode(k[C])),b.appendChild(w),S[C]=w}else for(C=0,P=(S=b.getElementsByTagName("tspan")).length;C<P;C++)C?Y(S[C],{dy:_*F,x:x.x}):Y(S[0],{dy:0});Y(b,{x:x.x,y:x.y}),y._.dirty=1;v=y._getBBox(),y=x.y-(v.y+v.height/2);y&&T.is(y,"finite")&&Y(S[0],{dy:y})}r.style.visibility=o}function r(t){return t.parentNode&&"a"===t.parentNode.tagName.toLowerCase()?t.parentNode:t}function a(t,e){(this[0]=this.node=t).raphael=!0,this.id=("0000"+(Math.random()*Math.pow(36,5)<<0).toString(36)).slice(-5),t.raphaelid=this.id,this.matrix=T.matrix(),this.realPath=null,this.paper=e,this.attrs=this.attrs||{},this._={transform:[],sx:1,sy:1,deg:0,dx:0,dy:0,dirty:1},e.bottom||(e.bottom=this),this.prev=e.top,e.top&&(e.top.next=this),(e.top=this).next=null}var t,N="hasOwnProperty",A=String,m=parseFloat,L=parseInt,g=Math,j=g.max,B=g.abs,y=g.pow,R=/[, ]+/,p=T.eve,I="",H="http://www.w3.org/1999/xlink",k={block:"M5,0 0,2.5 5,5z",classic:"M5,0 0,2.5 5,5 3.5,3 3.5,2z",diamond:"M2.5,0 5,2.5 2.5,5 0,2.5z",open:"M6,1 1,3.5 6,6",oval:"M2.5,0A2.5,2.5,0,0,1,2.5,5 2.5,2.5,0,0,1,2.5,0z"},S={},Y=(T.toString=function(){return"Your browser supports SVG.\nYou are running Raphaël "+this.version},function(t,e){if(e)for(var n in"string"==typeof t&&(t=Y(t)),e)e[N](n)&&("xlink:"==n.substring(0,6)?t.setAttributeNS(H,n.substring(6),A(e[n])):t.setAttribute(n,A(e[n])));else(t=T._g.doc.createElementNS("http://www.w3.org/2000/svg",t)).style&&(t.style.webkitTapHighlightColor="rgba(0,0,0,0)");return t}),v=function(t){if((e=document.documentMode)&&(9===e||10===e))return"url('#"+t+"')";var e=document.location;return"url('"+(e.protocol+"//"+e.host+e.pathname+e.search)+"#"+t+"')"},l={"-":[3,1],".":[1,1],"-.":[3,1,1,1],"-..":[3,1,1,1,1,1],". ":[1,3],"- ":[4,3],"--":[8,3],"- .":[4,3,1,3],"--.":[8,3,1,3],"--..":[8,3,1,3,1,3]},F=1.2,e=T.el,n=((a.prototype=e).constructor=a,T._engine.path=function(t,e){var n=Y("path"),n=(e.canvas&&e.canvas.appendChild(n),new a(n,e));return n.type="path",f(n,{fill:"none",stroke:"#000",path:t}),n},e.rotate=function(t,e,n){return this.removed||((t=A(t).split(R)).length-1&&(e=m(t[1]),n=m(t[2])),t=m(t[0]),null!=(e=null==n?n:e)&&null!=n||(e=(r=this.getBBox(1)).x+r.width/2,n=r.y+r.height/2),this.transform(this._.transform.concat([["r",t,e,n]]))),this;var r},e.scale=function(t,e,n,r){return this.removed||((t=A(t).split(R)).length-1&&(e=m(t[1]),n=m(t[2]),r=m(t[3])),t=m(t[0]),null==e&&(e=t),null!=(n=null==r?r:n)&&null!=r||(i=this.getBBox(1)),n=null==n?i.x+i.width/2:n,r=null==r?i.y+i.height/2:r,this.transform(this._.transform.concat([["s",t,e,n,r]]))),this;var i},e.translate=function(t,e){return this.removed||((t=A(t).split(R)).length-1&&(e=m(t[1])),t=m(t[0])||0,this.transform(this._.transform.concat([["t",t,e=+e||0]]))),this},e.transform=function(t){var e=this._;return null==t?e.transform:(T._extractTransform(this,t),this.clip&&Y(this.clip,{transform:this.matrix.invert()}),this.pattern&&M(this),this.node&&Y(this.node,{transform:this.matrix}),1==e.sx&&1==e.sy||(t=this.attrs[N]("stroke-width")?this.attrs["stroke-width"]:1,this.attr({"stroke-width":t})),this)},e.hide=function(){return this.removed||(this.node.style.display="none"),this},e.show=function(){return this.removed||(this.node.style.display=""),this},e.remove=function(){var t=r(this.node);if(!this.removed&&t.parentNode){var e,n=this.paper;for(e in n.__set__&&n.__set__.exclude(this),p.unbind("raphael.*.*."+this.id),this.gradient&&n.defs.removeChild(this.gradient),T._tear(this,n),t.parentNode.removeChild(t),this.removeData(),this)this[e]="function"==typeof this[e]?T._removedFactory(e):null;this.removed=!0}},e._getBBox=function(){"none"==this.node.style.display&&(this.show(),t=!0);var t,e,n=!1,r=(this.paper.canvas.parentElement?e=this.paper.canvas.parentElement.style:this.paper.canvas.parentNode&&(e=this.paper.canvas.parentNode.style),e&&"none"==e.display&&(n=!0,e.display=""),{});try{r=this.node.getBBox()}catch(t){r={x:this.node.clientLeft,y:this.node.clientTop,width:this.node.clientWidth,height:this.node.clientHeight}}finally{r=r||{},n&&(e.display="none")}return t&&this.hide(),r},e.attr=function(t,e){if(this.removed)return this;if(null==t){var n,r={};for(n in this.attrs)this.attrs[N](n)&&(r[n]=this.attrs[n]);return r.gradient&&"none"==r.fill&&(r.fill=r.gradient)&&delete r.gradient,r.transform=this._.transform,r}if(null==e&&T.is(t,"string")){if("fill"==t&&"none"==this.attrs.fill&&this.attrs.gradient)return this.attrs.gradient;if("transform"==t)return this._.transform;for(var i=t.split(R),o={},s=0,a=i.length;s<a;s++)(t=i[s])in this.attrs?o[t]=this.attrs[t]:T.is(this.paper.customAttributes[t],"function")?o[t]=this.paper.customAttributes[t].def:o[t]=T._availableAttrs[t];return a-1?o:o[i[0]]}if(null==e&&T.is(t,"array")){for(o={},s=0,a=t.length;s<a;s++)o[t[s]]=this.attr(t[s]);return o}var l,u;for(u in null!=e?(l={})[t]=e:null!=t&&T.is(t,"object")&&(l=t),l)p("raphael.attr."+u+"."+this.id,this,l[u]);for(u in this.paper.customAttributes)if(this.paper.customAttributes[N](u)&&l[N](u)&&T.is(this.paper.customAttributes[u],"function")){var c,h=this.paper.customAttributes[u].apply(this,[].concat(l[u]));for(c in this.attrs[u]=l[u],h)h[N](c)&&(l[c]=h[c])}return f(this,l),this},e.toFront=function(){if(this.removed)return this;var t=r(this.node),t=(t.parentNode.appendChild(t),this.paper);return t.top!=this&&T._tofront(this,t),this},e.toBack=function(){if(this.removed)return this;var t=r(this.node),e=t.parentNode;e.insertBefore(t,e.firstChild),T._toback(this,this.paper),this.paper;return this},e.insertAfter=function(t){if(this.removed||!t)return this;var e=r(this.node),n=r(t.node||t[t.length-1].node);return n.nextSibling?n.parentNode.insertBefore(e,n.nextSibling):n.parentNode.appendChild(e),T._insertafter(this,t,this.paper),this},e.insertBefore=function(t){if(this.removed||!t)return this;var e=r(this.node),n=r(t.node||t[0].node);return n.parentNode.insertBefore(e,n),T._insertbefore(this,t,this.paper),this},e.blur=function(t){var e,n,r=this;return 0!=+t?(e=Y("filter"),n=Y("feGaussianBlur"),r.attrs.blur=t,e.id=T.createUUID(),Y(n,{stdDeviation:+t||1.5}),e.appendChild(n),r.paper.defs.appendChild(e),r._blur=e,Y(r.node,{filter:"url(#"+e.id+")"})):(r._blur&&(r._blur.parentNode.removeChild(r._blur),delete r._blur,delete r.attrs.blur),r.node.removeAttribute("filter")),r},T._engine.circle=function(t,e,n,r){var i=Y("circle"),t=(t.canvas&&t.canvas.appendChild(i),new a(i,t));return t.attrs={cx:e,cy:n,r:r,fill:"none",stroke:"#000"},t.type="circle",Y(i,t.attrs),t},T._engine.rect=function(t,e,n,r,i,o){var s=Y("rect"),t=(t.canvas&&t.canvas.appendChild(s),new a(s,t));return t.attrs={x:e,y:n,width:r,height:i,rx:o||0,ry:o||0,fill:"none",stroke:"#000"},t.type="rect",Y(s,t.attrs),t},T._engine.ellipse=function(t,e,n,r,i){var o=Y("ellipse"),t=(t.canvas&&t.canvas.appendChild(o),new a(o,t));return t.attrs={cx:e,cy:n,rx:r,ry:i,fill:"none",stroke:"#000"},t.type="ellipse",Y(o,t.attrs),t},T._engine.image=function(t,e,n,r,i,o){var s=Y("image"),s=(Y(s,{x:n,y:r,width:i,height:o,preserveAspectRatio:"none"}),s.setAttributeNS(H,"href",e),t.canvas&&t.canvas.appendChild(s),new a(s,t));return s.attrs={x:n,y:r,width:i,height:o,src:e},s.type="image",s},T._engine.text=function(t,e,n,r){var i=Y("text"),i=(t.canvas&&t.canvas.appendChild(i),new a(i,t));return i.attrs={x:e,y:n,"text-anchor":"middle",text:r,"font-family":T._availableAttrs["font-family"],"font-size":T._availableAttrs["font-size"],stroke:"none",fill:"#000"},i.type="text",f(i,i.attrs),i},T._engine.setSize=function(t,e){return this.width=t||this.width,this.height=e||this.height,this.canvas.setAttribute("width",this.width),this.canvas.setAttribute("height",this.height),this._viewBox&&this.setViewBox.apply(this,this._viewBox),this},T._engine.create=function(){var t=T._getContainer.apply(0,arguments),e=t&&t.container;if(!e)throw new Error("SVG container not found.");var n,r=t.x,i=t.y,o=t.width,t=t.height,s=Y("svg"),a="overflow:hidden;",r=r||0,i=i||0;return Y(s,{height:t=t||342,version:1.1,width:o=o||512,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}),1==e?(s.style.cssText=a+"position:absolute;left:"+r+"px;top:"+i+"px",T._g.doc.body.appendChild(s),n=1):(s.style.cssText=a+"position:relative",e.firstChild?e.insertBefore(s,e.firstChild):e.appendChild(s)),(e=new T._Paper).width=o,e.height=t,e.canvas=s,e.clear(),e._left=e._top=0,n&&(e.renderfix=function(){}),e.renderfix(),e},T._engine.setViewBox=function(t,e,n,r,i){p("raphael.setViewBox",this,this._viewBox,[t,e,n,r,i]);var o,s=this.getSize(),a=j(n/s.width,r/s.height),l=this.top,s=i?"xMidYMid meet":"xMinYMin",u=null==t?(this._vbSize&&(a=1),delete this._vbSize,"0 0 "+this.width+" "+this.height):(this._vbSize=a,t+" "+e+" "+n+" "+r);for(Y(this.canvas,{viewBox:u,preserveAspectRatio:s});a&&l;)o="stroke-width"in l.attrs?l.attrs["stroke-width"]:1,l.attr({"stroke-width":o}),l._.dirty=1,l._.dirtyT=1,l=l.prev;return this._viewBox=[t,e,n,r,!!i],this},T.prototype.renderfix=function(){var e=this.canvas,t=e.style;try{n=e.getScreenCTM()||e.createSVGMatrix()}catch(t){n=e.createSVGMatrix()}var e=-n.e%1,n=-n.f%1;(e||n)&&(e&&(this._left=(this._left+e)%1,t.left=this._left+"px"),n&&(this._top=(this._top+n)%1,t.top=this._top+"px"))},T.prototype.clear=function(){T.eve("raphael.clear",this);for(var t=this.canvas;t.firstChild;)t.removeChild(t.firstChild);this.bottom=this.top=null,(this.desc=Y("desc")).appendChild(T._g.doc.createTextNode("Created with Raphaël "+T.version)),t.appendChild(this.desc),t.appendChild(this.defs=Y("defs"))},T.prototype.remove=function(){for(var t in p("raphael.remove",this),this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this)this[t]="function"==typeof this[t]?T._removedFactory(t):null},T.st);for(t in e)e[N](t)&&!n[N](t)&&(n[t]=function(n){return function(){var e=arguments;return this.forEach(function(t){t[n].apply(t,e)})}}(t))}}.apply(e,n);void 0!==e&&(t.exports=e)},"./dev/raphael.vml.js":function(t,e,n){n=[n("./dev/raphael.core.js")],e=function(m){if(!m||m.vml){function g(t,e,n){var r=m.matrix();return r.rotate(-t,.5,.5),{dx:r.x(e,n),dy:r.y(e,n)}}function y(t,e,n,r,i,o){var s,a=t._,l=t.matrix,u=a.fillpos,t=t.node,c=t.style,h=1,f="",p=j/e,d=j/n;c.visibility="hidden",e&&n&&(t.coordsize=T(p)+E+T(d),c.rotation=o*(e*n<0?-1:1),o&&(r=(s=g(o,r,i)).dx,i=s.dy),e<0&&(f+="x"),n<0&&(f+=" y")&&(h=-1),c.flip=f,t.coordorigin=r*-p+E+i*-d,(u||a.fillsize)&&(f=(f=t.getElementsByTagName(D))&&f[0],t.removeChild(f),u&&(s=g(o,l.x(u[0],u[1]),l.y(u[0],u[1])),f.position=s.dx*h+E+s.dy*h),a.fillsize&&(f.size=a.fillsize[0]*T(e)+E+a.fillsize[1]*T(n)),t.appendChild(f)),c.visibility="visible")}function v(t,e,n){for(var r=_(e).toLowerCase().split("-"),e=n?"end":"start",i=r.length,o="classic",s="medium",a="medium";i--;)switch(r[i]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":o=r[i];break;case"wide":case"narrow":a=r[i];break;case"long":case"short":s=r[i]}n=t.node.getElementsByTagName("stroke")[0];n[e+"arrow"]=o,n[e+"arrowlength"]=s,n[e+"arrowwidth"]=a}function f(t,e){t.attrs=t.attrs||{};var n,r=t.node,i=t.attrs,o=r.style,s=B[t.type]&&(e.x!=i.x||e.y!=i.y||e.width!=i.width||e.height!=i.height||e.cx!=i.cx||e.cy!=i.cy||e.rx!=i.rx||e.ry!=i.ry||e.r!=i.r),a=R[t.type]&&(i.cx!=e.cx||i.cy!=e.cy||i.r!=e.r||i.rx!=e.rx||i.ry!=e.ry),l=t;for(n in e)e[b](n)&&(i[n]=e[n]);if(s&&(i.path=m._getPath[t.type](t),t._.dirty=1),e.href&&(r.href=e.href),e.title&&(r.title=e.title),e.target&&(r.target=e.target),e.cursor&&(o.cursor=e.cursor),"blur"in e&&t.blur(e.blur),(e.path&&"path"==t.type||s)&&(r.path=function(t){var e=/[ahqstv]/gi,n=m._pathToAbsolute;if(_(t).match(e)&&(n=m._path2curve),e=/[clmz]/g,n==m._pathToAbsolute&&!_(t).match(e))return s=_(t).replace(A,function(t,e,n){var r=[],i="m"==e.toLowerCase(),o=N[e];return n.replace(L,function(t){i&&2==r.length&&(o+=r+N["m"==e?"l":"L"],r=[]),r.push(S(t*j))}),o+r});for(var r,i,o=n(t),s=[],a=0,l=o.length;a<l;a++){r=o[a],"z"==(i=o[a][0].toLowerCase())&&(i="x");for(var u=1,c=r.length;u<c;u++)i+=S(r[u]*j)+(u!=c-1?",":O);s.push(i)}return s.join(E)}(~_(i.path).toLowerCase().indexOf("r")?m._pathToAbsolute(i.path):i.path),t._.dirty=1,"image"==t.type&&(t._.fillpos=[i.x,i.y],t._.fillsize=[i.width,i.height],y(t,1,1,0,0,0))),"transform"in e&&t.transform(e.transform),a&&(s=+i.cx,a=+i.cy,u=+i.rx||+i.r||0,h=+i.ry||+i.r||0,r.path=m.format("ar{0},{1},{2},{3},{4},{1},{4},{1}x",S((s-u)*j),S((a-h)*j),S((s+u)*j),S((a+h)*j),S(s*j)),t._.dirty=1),"clip-rect"in e&&(4==(u=_(e["clip-rect"]).split(M)).length&&(u[2]=+u[2]+ +u[0],u[3]=+u[3]+ +u[1],(h=(a=r.clipRect||m._g.doc.createElement("div")).style).clip=m.format("rect({1}px {2}px {3}px {0}px)",u),r.clipRect||(h.position="absolute",h.top=0,h.left=0,h.width=t.paper.width+"px",h.height=t.paper.height+"px",r.parentNode.insertBefore(a,r),a.appendChild(r),r.clipRect=a)),e["clip-rect"]||r.clipRect&&(r.clipRect.style.clip="auto")),t.textpath&&(s=t.textpath.style,e.font&&(s.font=e.font),e["font-family"]&&(s.fontFamily='"'+e["font-family"].split(",")[0].replace(/^['"]+|['"]+$/g,O)+'"'),e["font-size"]&&(s.fontSize=e["font-size"]),e["font-weight"]&&(s.fontWeight=e["font-weight"]),e["font-style"]&&(s.fontStyle=e["font-style"])),"arrow-start"in e&&v(l,e["arrow-start"]),"arrow-end"in e&&v(l,e["arrow-end"],1),null==e.opacity&&null==e.fill&&null==e.src&&null==e.stroke&&null==e["stroke-width"]&&null==e["stroke-opacity"]&&null==e["fill-opacity"]&&null==e["stroke-dasharray"]&&null==e["stroke-miterlimit"]&&null==e["stroke-linejoin"]&&null==e["stroke-linecap"]||((u=(u=r.getElementsByTagName(D))&&u[0])||(u=x(D)),"image"==t.type&&e.src&&(u.src=e.src),e.fill&&(u.on=!0),null!=u.on&&"none"!=e.fill&&null!==e.fill||(u.on=!1),u.on&&e.fill&&((h=_(e.fill).match(m._ISURL))?(u.parentNode==r&&r.removeChild(u),u.rotate=!0,u.src=h[1],u.type="tile",a=t.getBBox(1),u.position=a.x+E+a.y,t._.fillpos=[a.x,a.y],m._preload(h[1],function(){t._.fillsize=[this.offsetWidth,this.offsetHeight]})):(u.color=m.getRGB(e.fill).hex,u.src=O,u.type="solid",m.getRGB(e.fill).error&&(l.type in{circle:1,ellipse:1}||"r"!=_(e.fill).charAt())&&function(t,e,n){t.attrs=t.attrs||{};var r=t.attrs,i=Math.pow,o,s,a="linear",l=".5 .5";if(t.attrs.gradient=e,e=(e=_(e).replace(m._radial_gradient,function(t,e,n){a="radial";if(e&&n){e=w(e);n=w(n);i(e-.5,2)+i(n-.5,2)>.25&&(n=k.sqrt(.25-i(e-.5,2))*((n>.5)*2-1)+.5);l=e+E+n}return O})).split(/\s*\-\s*/),a=="linear"){var u=e.shift();u=-w(u);if(isNaN(u))return null}var c=m._parseDots(e);if(!c)return null;if(t=t.shape||t.node,c.length){t.removeChild(n);n.on=true;n.method="none";n.color=c[0].color;n.color2=c[c.length-1].color;var h=[];for(var f=0,p=c.length;f<p;f++)c[f].offset&&h.push(c[f].offset+E+c[f].color);n.colors=h.length?h.join():"0% "+n.color;if(a=="radial"){n.type="gradientTitle";n.focus="100%";n.focussize="0 0";n.focusposition=l;n.angle=0}else{n.type="gradient";n.angle=(270-u)%360}t.appendChild(n)}return 1}(l,e.fill,u)&&(i.fill="none",i.gradient=e.fill,u.rotate=!1))),("fill-opacity"in e||"opacity"in e)&&(c=((+i["fill-opacity"]+1||2)-1)*((+i.opacity+1||2)-1)*((+m.getRGB(e.fill).o+1||2)-1),c=P(C(c,0),1),u.opacity=c,u.src&&(u.color="none")),r.appendChild(u),s=!1,(a=r.getElementsByTagName("stroke")&&r.getElementsByTagName("stroke")[0])||(s=a=x("stroke")),(e.stroke&&"none"!=e.stroke||e["stroke-width"]||null!=e["stroke-opacity"]||e["stroke-dasharray"]||e["stroke-miterlimit"]||e["stroke-linejoin"]||e["stroke-linecap"])&&(a.on=!0),"none"!=e.stroke&&null!==e.stroke&&null!=a.on&&0!=e.stroke&&0!=e["stroke-width"]||(a.on=!1),h=m.getRGB(e.stroke),a.on&&e.stroke&&(a.color=h.hex),c=((+i["stroke-opacity"]+1||2)-1)*((+i.opacity+1||2)-1)*((+h.o+1||2)-1),u=.75*(w(e["stroke-width"])||1),c=P(C(c,0),1),null==e["stroke-width"]&&(u=i["stroke-width"]),e["stroke-width"]&&(a.weight=u),u&&u<1&&(c*=u)&&(a.weight=1),a.opacity=c,e["stroke-linejoin"]&&(a.joinstyle=e["stroke-linejoin"]||"miter"),a.miterlimit=e["stroke-miterlimit"]||8,e["stroke-linecap"]&&(a.endcap="butt"==e["stroke-linecap"]?"flat":"square"==e["stroke-linecap"]?"square":"round"),"stroke-dasharray"in e&&(a.dashstyle=(h={"-":"shortdash",".":"shortdot","-.":"shortdashdot","-..":"shortdashdotdot",". ":"dot","- ":"dash","--":"longdash","- .":"dashdot","--.":"longdashdot","--..":"longdashdotdot"})[b](e["stroke-dasharray"])?h[e["stroke-dasharray"]]:O),s&&r.appendChild(a)),"text"==l.type){l.paper.canvas.style.display=O;var u=l.paper.span,c=i.font&&i.font.match(/\d+(?:\.\d*)?(?=px)/),o=u.style;i.font&&(o.font=i.font),i["font-family"]&&(o.fontFamily=i["font-family"]),i["font-weight"]&&(o.fontWeight=i["font-weight"]),i["font-style"]&&(o.fontStyle=i["font-style"]),c=w(i["font-size"]||c&&c[0])||10,o.fontSize=100*c+"px",l.textpath.string&&(u.innerHTML=_(l.textpath.string).replace(/</g,"&#60;").replace(/&/g,"&#38;").replace(/\n/g,"<br>"));for(var h=u.getBoundingClientRect(),f=(l.W=i.w=(h.right-h.left)/100,l.H=i.h=(h.bottom-h.top)/100,l.X=i.x,l.Y=i.y+l.H/2,("x"in e||"y"in e)&&(l.path.v=m.format("m{0},{1}l{2},{1}",S(i.x*j),S(i.y*j),S(i.x*j)+1)),["x","y","text","font","font-family","font-weight","font-style","font-size"]),p=0,d=f.length;p<d;p++)if(f[p]in e){l._.dirty=1;break}switch(i["text-anchor"]){case"start":l.textpath.style["v-text-align"]="left",l.bbx=l.W/2;break;case"end":l.textpath.style["v-text-align"]="right",l.bbx=-l.W/2;break;default:l.textpath.style["v-text-align"]="center",l.bbx=0}l.textpath.style["v-text-kern"]=!0}}function u(t,e){(this[0]=this.node=t).raphael=!0,this.id=m._oid++,t.raphaelid=this.id,this.X=0,this.Y=0,this.attrs={},this.paper=e,this.matrix=m.matrix(),this._={transform:[],sx:1,sy:1,dx:0,dy:0,deg:0,dirty:1,dirtyT:1},e.bottom||(e.bottom=this),this.prev=e.top,e.top&&(e.top.next=this),(e.top=this).next=null}var x,t,b="hasOwnProperty",_=String,w=parseFloat,k=Math,S=k.round,C=k.max,P=k.min,T=k.abs,D="fill",M=/[, ]+/,p=m.eve,E=" ",O="",N={M:"m",L:"l",C:"c",Z:"x",m:"t",l:"r",c:"v",z:"x"},A=/([clmz]),?([^clmz]*)/gi,r=/ progid:\S+Blur\([^\)]+\)/g,L=/-?[^,\s-]+/g,c="position:absolute;left:0;top:0;width:1px;height:1px;behavior:url(#default#VML)",j=21600,B={path:1,rect:1,image:1},R={circle:1,ellipse:1},e=(m.toString=function(){return"Your browser doesn’t support SVG. Falling down to VML.\nYou are running Raphaël "+this.version},m.el),n=((u.prototype=e).constructor=u,e.transform=function(t){if(null==t)return this._.transform;var e,n,r,i=this.paper._viewBoxShift,o=i?"s"+[i.scale,i.scale]+"-1-1t"+[i.dx,i.dy]:O,i=(i&&(e=t=_(t).replace(/\.{3}|\u2026/g,this._.transform||O)),m._extractTransform(this,o+t),this.matrix.clone()),o=this.skew,t=this.node,s=~_(this.attrs.fill).indexOf("-"),a=!_(this.attrs.fill).indexOf("url(");return i.translate(1,1),a||s||"image"==this.type?(o.matrix="1 0 0 1",o.offset="0 0",a=i.split(),s&&a.noRotation||!a.isSimple?(t.style.filter=i.toFilter(),s=this.getBBox(),n=this.getBBox(1),r=s.x-n.x,s=s.y-n.y,t.coordorigin=r*-j+E+s*-j,y(this,1,1,r,s,0)):(t.style.filter=O,y(this,a.scalex,a.scaley,a.dx,a.dy,a.rotate))):(t.style.filter=O,o.matrix=_(i),o.offset=i.offset()),null!==e&&(this._.transform=e,m._extractTransform(this,e)),this},e.rotate=function(t,e,n){if(this.removed)return this;var r;if(null!=t)return(t=_(t).split(M)).length-1&&(e=w(t[1]),n=w(t[2])),t=w(t[0]),null!=(e=null==n?n:e)&&null!=n||(e=(r=this.getBBox(1)).x+r.width/2,n=r.y+r.height/2),this._.dirtyT=1,this.transform(this._.transform.concat([["r",t,e,n]])),this},e.translate=function(t,e){return this.removed||((t=_(t).split(M)).length-1&&(e=w(t[1])),t=w(t[0])||0,e=+e||0,this._.bbox&&(this._.bbox.x+=t,this._.bbox.y+=e),this.transform(this._.transform.concat([["t",t,e]]))),this},e.scale=function(t,e,n,r){return this.removed||((t=_(t).split(M)).length-1&&(e=w(t[1]),n=w(t[2]),r=w(t[3]),isNaN(n)&&(n=null),isNaN(r)&&(r=null)),t=w(t[0]),null==e&&(e=t),null!=(n=null==r?r:n)&&null!=r||(i=this.getBBox(1)),n=null==n?i.x+i.width/2:n,r=null==r?i.y+i.height/2:r,this.transform(this._.transform.concat([["s",t,e,n,r]])),this._.dirtyT=1),this;var i},e.hide=function(){return this.removed||(this.node.style.display="none"),this},e.show=function(){return this.removed||(this.node.style.display=O),this},e.auxGetBBox=m.el.getBBox,e.getBBox=function(){var t,e,n=this.auxGetBBox();return this.paper&&this.paper._viewBoxShift?(e=1/this.paper._viewBoxShift.scale,(t={}).x=n.x-this.paper._viewBoxShift.dx,t.x*=e,t.y=n.y-this.paper._viewBoxShift.dy,t.y*=e,t.width=n.width*e,t.height=n.height*e,t.x2=t.x+t.width,t.y2=t.y+t.height,t):n},e._getBBox=function(){return this.removed?{}:{x:this.X+(this.bbx||0)-this.W/2,y:this.Y-this.H,width:this.W,height:this.H}},e.remove=function(){if(!this.removed&&this.node.parentNode){for(var t in this.paper.__set__&&this.paper.__set__.exclude(this),m.eve.unbind("raphael.*.*."+this.id),m._tear(this,this.paper),this.node.parentNode.removeChild(this.node),this.shape&&this.shape.parentNode.removeChild(this.shape),this)this[t]="function"==typeof this[t]?m._removedFactory(t):null;this.removed=!0}},e.attr=function(t,e){if(this.removed)return this;if(null==t){var n,r={};for(n in this.attrs)this.attrs[b](n)&&(r[n]=this.attrs[n]);return r.gradient&&"none"==r.fill&&(r.fill=r.gradient)&&delete r.gradient,r.transform=this._.transform,r}if(null==e&&m.is(t,"string")){if(t==D&&"none"==this.attrs.fill&&this.attrs.gradient)return this.attrs.gradient;for(var i=t.split(M),o={},s=0,a=i.length;s<a;s++)(t=i[s])in this.attrs?o[t]=this.attrs[t]:m.is(this.paper.customAttributes[t],"function")?o[t]=this.paper.customAttributes[t].def:o[t]=m._availableAttrs[t];return a-1?o:o[i[0]]}if(this.attrs&&null==e&&m.is(t,"array")){for(o={},s=0,a=t.length;s<a;s++)o[t[s]]=this.attr(t[s]);return o}var l,u;for(u in null!=e&&((l={})[t]=e),l=null==e&&m.is(t,"object")?t:l)p("raphael.attr."+u+"."+this.id,this,l[u]);if(l){for(u in this.paper.customAttributes)if(this.paper.customAttributes[b](u)&&l[b](u)&&m.is(this.paper.customAttributes[u],"function")){var c,h=this.paper.customAttributes[u].apply(this,[].concat(l[u]));for(c in this.attrs[u]=l[u],h)h[b](c)&&(l[c]=h[c])}l.text&&"text"==this.type&&(this.textpath.string=l.text),f(this,l)}return this},e.toFront=function(){return this.removed||this.node.parentNode.appendChild(this.node),this.paper&&this.paper.top!=this&&m._tofront(this,this.paper),this},e.toBack=function(){return this.removed||this.node.parentNode.firstChild!=this.node&&(this.node.parentNode.insertBefore(this.node,this.node.parentNode.firstChild),m._toback(this,this.paper)),this},e.insertAfter=function(t){return this.removed||((t=t.constructor==m.st.constructor?t[t.length-1]:t).node.nextSibling?t.node.parentNode.insertBefore(this.node,t.node.nextSibling):t.node.parentNode.appendChild(this.node),m._insertafter(this,t,this.paper)),this},e.insertBefore=function(t){return this.removed||((t=t.constructor==m.st.constructor?t[0]:t).node.parentNode.insertBefore(this.node,t.node),m._insertbefore(this,t,this.paper)),this},e.blur=function(t){var e=this.node.runtimeStyle,n=(n=e.filter).replace(r,O);return 0!=+t?(this.attrs.blur=t,e.filter=n+E+" progid:DXImageTransform.Microsoft.Blur(pixelradius="+(+t||1.5)+")",e.margin=m.format("-{0}px 0 0 -{0}px",S(+t||1.5))):(e.filter=n,e.margin=0,delete this.attrs.blur),this},m._engine.path=function(t,e){var n=x("shape"),r=(n.style.cssText=c,n.coordsize=j+E+j,n.coordorigin=e.coordorigin,new u(n,e)),i={fill:"none",stroke:"#000"},t=(t&&(i.path=t),r.type="path",r.path=[],r.Path=O,f(r,i),e.canvas&&e.canvas.appendChild(n),x("skew"));return t.on=!0,n.appendChild(t),r.skew=t,r.transform(O),r},m._engine.rect=function(t,e,n,r,i,o){var s=m._rectPath(e,n,r,i,o),t=t.path(s),a=t.attrs;return t.X=a.x=e,t.Y=a.y=n,t.W=a.width=r,t.H=a.height=i,a.r=o,a.path=s,t.type="rect",t},m._engine.ellipse=function(t,e,n,r,i){t=t.path();t.attrs;return t.X=e-r,t.Y=n-i,t.W=2*r,t.H=2*i,t.type="ellipse",f(t,{cx:e,cy:n,rx:r,ry:i}),t},m._engine.circle=function(t,e,n,r){t=t.path();t.attrs;return t.X=e-r,t.Y=n-r,t.W=t.H=2*r,t.type="circle",f(t,{cx:e,cy:n,r:r}),t},m._engine.image=function(t,e,n,r,i,o){var s=m._rectPath(n,r,i,o),t=t.path(s).attr({stroke:"none"}),a=t.attrs,l=t.node,u=l.getElementsByTagName(D)[0];return a.src=e,t.X=a.x=n,t.Y=a.y=r,t.W=a.width=i,t.H=a.height=o,a.path=s,t.type="image",u.parentNode==l&&l.removeChild(u),u.rotate=!0,u.src=e,u.type="tile",t._.fillpos=[n,r],t._.fillsize=[i,o],l.appendChild(u),y(t,1,1,0,0,0),t},m._engine.text=function(t,e,n,r){var i=x("shape"),o=x("path"),s=x("textpath"),a=(n=n||0,r=r||"",o.v=m.format("m{0},{1}l{2},{1}",S((e=e||0)*j),S(n*j),S(e*j)+1),o.textpathok=!0,s.string=_(r),s.on=!0,i.style.cssText=c,i.coordsize=j+E+j,i.coordorigin="0 0",new u(i,t)),l={fill:"#000",stroke:"none",font:m._availableAttrs.font,text:r},r=(a.shape=i,a.path=o,a.textpath=s,a.type="text",a.attrs.text=_(r),a.attrs.x=e,a.attrs.y=n,a.attrs.w=1,a.attrs.h=1,f(a,l),i.appendChild(s),i.appendChild(o),t.canvas.appendChild(i),x("skew"));return r.on=!0,i.appendChild(r),a.skew=r,a.transform(O),a},m._engine.setSize=function(t,e){var n=this.canvas.style;return(this.width=t)==+t&&(t+="px"),(this.height=e)==+e&&(e+="px"),n.width=t,n.height=e,n.clip="rect(0 "+t+" "+e+" 0)",this._viewBox&&m._engine.setViewBox.apply(this,this._viewBox),this},m._engine.setViewBox=function(t,e,n,r,i){m.eve("raphael.setViewBox",this,this._viewBox,[t,e,n,r,i]);var o,s=this.getSize(),a=s.width,l=s.height;return i&&(n*(o=l/r)<a&&(t-=(a-n*o)/2/o),r*(o=a/n)<l&&(e-=(l-r*o)/2/o)),this._viewBox=[t,e,n,r,!!i],this._viewBoxShift={dx:-t,dy:-e,scale:s},this.forEach(function(t){t.transform("...")}),this},m._engine.initWin=function(t){var e=t.document;(e.styleSheets.length<31?e.createStyleSheet():e.styleSheets[0]).addRule(".rvml","behavior:url(#default#VML)");try{e.namespaces.rvml||e.namespaces.add("rvml","urn:schemas-microsoft-com:vml"),x=function(t){return e.createElement("<rvml:"+t+' class="rvml">')}}catch(t){x=function(t){return e.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}},m._engine.initWin(m._g.win),m._engine.create=function(){var t=m._getContainer.apply(0,arguments),e=t.container,n=t.height,r=t.width,i=t.x,t=t.y;if(!e)throw new Error("VML container not found.");var o=new m._Paper,s=o.canvas=m._g.doc.createElement("div"),a=s.style,i=i||0,t=t||0,r=r||512,n=n||342;return(o.width=r)==+r&&(r+="px"),(o.height=n)==+n&&(n+="px"),o.coordsize=1e3*j+E+1e3*j,o.coordorigin="0 0",o.span=m._g.doc.createElement("span"),o.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;",s.appendChild(o.span),a.cssText=m.format("top:0;left:0;width:{0};height:{1};display:inline-block;position:relative;clip:rect(0 {0} {1} 0);overflow:hidden",r,n),1==e?(m._g.doc.body.appendChild(s),a.left=i+"px",a.top=t+"px",a.position="absolute"):e.firstChild?e.insertBefore(s,e.firstChild):e.appendChild(s),o.renderfix=function(){},o},m.prototype.clear=function(){m.eve("raphael.clear",this),this.canvas.innerHTML=O,this.span=m._g.doc.createElement("span"),this.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;display:inline;",this.canvas.appendChild(this.span),this.bottom=this.top=null},m.prototype.remove=function(){for(var t in m.eve("raphael.remove",this),this.canvas.parentNode.removeChild(this.canvas),this)this[t]="function"==typeof this[t]?m._removedFactory(t):null;return!0},m.st);for(t in e)e[b](t)&&!n[b](t)&&(n[t]=function(n){return function(){var e=arguments;return this.forEach(function(t){t[n].apply(t,e)})}}(t))}}.apply(e,n);void 0!==e&&(t.exports=e)},"./node_modules/eve-raphael/eve.js":function(t,e,n){var p,d,f,m,g,y,r,v,x;function b(t,e){return t-e}function _(){for(var t=0,e=this.length;t<e;t++)if(void 0!==this[t])return this[t]}function w(){for(var t=this.length;--t;)if(void 0!==this[t])return this[t]}function k(t,e){var n,r=d,i=Array.prototype.slice.call(arguments,2),o=k.listeners(t),s=0,a=[],l={},u=[],c=p;u.firstDefined=_,u.lastDefined=w,p=t;for(var h=d=0,f=o.length;h<f;h++)"zIndex"in o[h]&&(a.push(o[h].zIndex),o[h].zIndex<0&&(l[o[h].zIndex]=o[h]));for(a.sort(b);a[s]<0;)if(n=l[a[s++]],u.push(n.apply(e,i)),d)return d=r,u;for(h=0;h<f;h++)if("zIndex"in(n=o[h]))if(n.zIndex==a[s]){if(u.push(n.apply(e,i)),d)break;do{if((n=l[a[++s]])&&u.push(n.apply(e,i)),d)break}while(n)}else l[n.zIndex]=n;else if(u.push(n.apply(e,i)),d)break;return d=r,p=c,u}f="hasOwnProperty",m=/[\.\/]/,g=/\s*,\s*/,y={n:{}},r=Object.prototype.toString,v=String,x=Array.isArray||function(t){return t instanceof Array||"[object Array]"==r.call(t)},k._events=y,k.listeners=function(t){for(var e,n,r,i,o,s,a,l=x(t)?t:t.split(m),u=[y],c=[],h=0,f=l.length;h<f;h++){for(a=[],o=0,s=u.length;o<s;o++)for(r=[(e=u[o].n)[l[h]],e["*"]],i=2;i--;)(n=r[i])&&(a.push(n),c=c.concat(n.f||[]));u=a}return c},k.separator=function(t){m=t?(t="["+(t=v(t).replace(/(?=[\.\^\]\[\-])/g,"\\"))+"]",new RegExp(t)):/[\.\/]/},k.on=function(t,e){if("function"!=typeof e)return function(){};for(var n=x(t)?x(t[0])?t:[t]:v(t).split(g),r=0,i=n.length;r<i;r++){c=u=o=l=a=void 0;for(var o,s=n[r],a=x(s)?s:v(s).split(m),l=y,u=0,c=a.length;u<c;u++)l=(l=l.n).hasOwnProperty(a[u])&&l[a[u]]||(l[a[u]]={n:{}});for(l.f=l.f||[],u=0,c=l.f.length;u<c;u++)if(l.f[u]==e){o=!0;break}o||l.f.push(e)}return function(t){+t==+t&&(e.zIndex=+t)}},k.f=function(t){var e=[].slice.call(arguments,1);return function(){k.apply(null,[t,null].concat(e).concat([].slice.call(arguments,0)))}},k.stop=function(){d=1},k.nt=function(t){var e=x(p)?p.join("."):p;return t?new RegExp("(?:\\.|\\/|^)"+t+"(?:\\.|\\/|$)").test(e):e},k.nts=function(){return x(p)?p:p.split(m)},k.off=k.unbind=function(t,e){if(t)if(1<(a=x(t)?x(t[0])?t:[t]:v(t).split(g)).length)for(var n=0,r=a.length;n<r;n++)k.off(a[n],e);else{var i,o,s,a=x(t)?t:v(t).split(m),l=[y];for(n=0,r=a.length;n<r;n++)for(c=0;c<l.length;c+=s.length-2){if(s=[c,1],i=l[c].n,"*"!=a[n])i[a[n]]&&s.push(i[a[n]]);else for(o in i)i[f](o)&&s.push(i[o]);l.splice.apply(l,s)}for(n=0,r=l.length;n<r;n++)for(i=l[n];i.n;){if(e){if(i.f){for(c=0,h=i.f.length;c<h;c++)if(i.f[c]==e){i.f.splice(c,1);break}i.f.length||delete i.f}for(o in i.n)if(i.n[f](o)&&i.n[o].f){for(var u=i.n[o].f,c=0,h=u.length;c<h;c++)if(u[c]==e){u.splice(c,1);break}u.length||delete i.n[o].f}}else for(o in delete i.f,i.n)i.n[f](o)&&i.n[o].f&&delete i.n[o].f;i=i.n}}else k._events=y={n:{}}},k.once=function(t,e){function n(){return k.off(t,n),e.apply(this,arguments)}return k.on(t,n)},k.version="0.5.0",k.toString=function(){return"You are running Eve 0.5.0"},t.exports?t.exports=k:void 0!==(e=function(){return k}.apply(e,[]))&&(t.exports=e)}},r={},i.m=n,i.c=r,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s="./dev/raphael.amd.js");function i(t){if(r[t])return r[t].exports;var e=r[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var n,r},"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Raphael=e():t.Raphael=e(),t=this,e=function(){return r=[function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0;var i=r(n(2)),o=r(n(45)),s=n(46),a=n(51),l=r(n(52)),u=r(n(49)),r=r(n(44)),c=i.default.create;function h(){var n=c();return n.compile=function(t,e){return a.compile(t,e,n)},n.precompile=function(t,e){return a.precompile(t,e,n)},n.AST=o.default,n.Compiler=a.Compiler,n.JavaScriptCompiler=l.default,n.Parser=s.parser,n.parse=s.parse,n.parseWithoutProcessing=s.parseWithoutProcessing,n}n=h();n.create=h,r.default(n),n.Visitor=u.default,n.default=n,e.default=n,t.exports=e.default},function(t,e){"use strict";e.default=function(t){return t&&t.__esModule?t:{default:t}},e.__esModule=!0},function(t,e,n){"use strict";var r=n(3).default,i=n(1).default;e.__esModule=!0;var o=r(n(4)),s=i(n(37)),a=i(n(6)),l=r(n(5)),u=r(n(38)),r=i(n(44));function c(){var e=new o.HandlebarsEnvironment;return l.extend(e,o),e.SafeString=s.default,e.Exception=a.default,e.Utils=l,e.escapeExpression=l.escapeExpression,e.VM=u,e.template=function(t){return u.template(t,e)},e}i=c();i.create=c,r.default(i),i.default=i,e.default=i,t.exports=e.default},function(t,e){"use strict";e.default=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e},e.__esModule=!0},function(t,e,n){"use strict";var r=n(1).default,i=(e.__esModule=!0,e.HandlebarsEnvironment=c,n(5)),o=r(n(6)),s=n(10),a=n(30),r=r(n(32)),l=n(33),u=(e.VERSION="4.7.7",e.COMPILER_REVISION=8,e.LAST_COMPATIBLE_COMPILER_REVISION=7,e.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"},"[object Object]");function c(t,e,n){this.helpers=t||{},this.partials=e||{},this.decorators=n||{},s.registerDefaultHelpers(this),a.registerDefaultDecorators(this)}c.prototype={constructor:c,logger:r.default,log:r.default.log,registerHelper:function(t,e){if(i.toString.call(t)===u){if(e)throw new o.default("Arg not supported with multiple helpers");i.extend(this.helpers,t)}else this.helpers[t]=e},unregisterHelper:function(t){delete this.helpers[t]},registerPartial:function(t,e){if(i.toString.call(t)===u)i.extend(this.partials,t);else{if(void 0===e)throw new o.default('Attempting to register a partial called "'+t+'" as undefined');this.partials[t]=e}},unregisterPartial:function(t){delete this.partials[t]},registerDecorator:function(t,e){if(i.toString.call(t)===u){if(e)throw new o.default("Arg not supported with multiple decorators");i.extend(this.decorators,t)}else this.decorators[t]=e},unregisterDecorator:function(t){delete this.decorators[t]},resetLoggedPropertyAccesses:function(){l.resetLoggedProperties()}};n=r.default.log;e.log=n,e.createFrame=i.createFrame,e.logger=r.default},function(t,e){"use strict";e.__esModule=!0,e.extend=s,e.indexOf=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},e.escapeExpression=function(t){if("string"!=typeof t){if(t&&t.toHTML)return t.toHTML();if(null==t)return"";if(!t)return t+"";t=""+t}return i.test(t)?t.replace(r,o):t},e.isEmpty=function(t){return!t&&0!==t||!(!u(t)||0!==t.length)},e.createFrame=function(t){var e=s({},t);return e._parent=t,e},e.blockParams=function(t,e){return t.path=e,t},e.appendContextPath=function(t,e){return(t?t+".":"")+e};var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},r=/[&<>"'`=]/g,i=/[&<>"'`=]/;function o(t){return n[t]}function s(t){for(var e=1;e<arguments.length;e++)for(var n in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],n)&&(t[n]=arguments[e][n]);return t}var a=Object.prototype.toString,l=(e.toString=a,function(t){return"function"==typeof t}),u=(l(/x/)&&(e.isFunction=l=function(t){return"function"==typeof t&&"[object Function]"===a.call(t)}),e.isFunction=l,Array.isArray||function(t){return!(!t||"object"!=typeof t)&&"[object Array]"===a.call(t)});e.isArray=u},function(t,e,n){"use strict";var l=n(7).default,u=(e.__esModule=!0,["description","fileName","lineNumber","endLineNumber","message","name","number","stack"]);function c(t,e){for(var e=e&&e.loc,n=void 0,r=void 0,i=void 0,o=void 0,s=(e&&(n=e.start.line,r=e.end.line,i=e.start.column,o=e.end.column,t+=" - "+n+":"+i),Error.prototype.constructor.call(this,t)),a=0;a<u.length;a++)this[u[a]]=s[u[a]];Error.captureStackTrace&&Error.captureStackTrace(this,c);try{e&&(this.lineNumber=n,this.endLineNumber=r,l?(Object.defineProperty(this,"column",{value:i,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:o,enumerable:!0})):(this.column=i,this.endColumn=o))}catch(t){}}c.prototype=new Error,e.default=c,t.exports=e.default},function(t,e,n){t.exports={default:n(8),__esModule:!0}},function(t,e,n){var r=n(9);t.exports=function(t,e,n){return r.setDesc(t,e,n)}},function(t,e){var n=Object;t.exports={create:n.create,getProto:n.getPrototypeOf,isEnum:{}.propertyIsEnumerable,getDesc:n.getOwnPropertyDescriptor,setDesc:n.defineProperty,setDescs:n.defineProperties,getKeys:n.keys,getNames:n.getOwnPropertyNames,getSymbols:n.getOwnPropertySymbols,each:[].forEach}},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0,e.registerDefaultHelpers=function(t){i.default(t),o.default(t),s.default(t),a.default(t),l.default(t),u.default(t),c.default(t)},e.moveHelperToHooks=function(t,e,n){t.helpers[e]&&(t.hooks[e]=t.helpers[e],n||delete t.helpers[e])};var i=r(n(11)),o=r(n(12)),s=r(n(25)),a=r(n(26)),l=r(n(27)),u=r(n(28)),c=r(n(29))},function(t,e,n){"use strict";e.__esModule=!0;var o=n(5);e.default=function(i){i.registerHelper("blockHelperMissing",function(t,e){var n=e.inverse,r=e.fn;return!0===t?r(this):!1===t||null==t?n(this):o.isArray(t)?0<t.length?(e.ids&&(e.ids=[e.name]),i.helpers.each(t,e)):n(this):(e.data&&e.ids&&((n=o.createFrame(e.data)).contextPath=o.appendContextPath(e.data.contextPath,e.name),e={data:n}),r(t,e))})},t.exports=e.default},function(e,n,r){!function(d){"use strict";var m=r(13).default,t=r(1).default,g=(n.__esModule=!0,r(5)),y=t(r(6));n.default=function(t){t.registerHelper("each",function(r,t){if(!t)throw new y.default("Must pass iterator to #each");var e,i=t.fn,n=t.inverse,o=0,s="",a=void 0,l=void 0;function u(t,e,n){a&&(a.key=t,a.index=e,a.first=0===e,a.last=!!n,l&&(a.contextPath=l+t)),s+=i(r[t],{data:a,blockParams:g.blockParams([r[t],t],[l+t,null])})}if(t.data&&t.ids&&(l=g.appendContextPath(t.data.contextPath,t.ids[0])+"."),g.isFunction(r)&&(r=r.call(this)),t.data&&(a=g.createFrame(t.data)),r&&"object"==typeof r)if(g.isArray(r))for(var c=r.length;o<c;o++)o in r&&u(o,o,o===r.length-1);else if(d.Symbol&&r[d.Symbol.iterator]){for(var h=[],f=r[d.Symbol.iterator](),p=f.next();!p.done;p=f.next())h.push(p.value);for(c=(r=h).length;o<c;o++)u(o,o,o===r.length-1)}else e=void 0,m(r).forEach(function(t){void 0!==e&&u(e,o-1),e=t,o++}),void 0!==e&&u(e,o-1,!0);return s=0===o?n(this):s})},e.exports=n.default}.call(n,function(){return this}())},function(t,e,n){t.exports={default:n(14),__esModule:!0}},function(t,e,n){n(15),t.exports=n(21).Object.keys},function(t,e,n){var r=n(16);n(18)("keys",function(e){return function(t){return e(r(t))}})},function(t,e,n){var r=n(17);t.exports=function(t){return Object(r(t))}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e,n){var i=n(19),o=n(21),s=n(24);t.exports=function(t,e){var n=(o.Object||{})[t]||Object[t],r={};r[t]=e(n),i(i.S+i.F*s(function(){n(1)}),"Object",r)}},function(t,e,n){function d(t,e,n){var r,i,o,s=t&d.F,a=t&d.G,l=t&d.S,u=t&d.P,c=t&d.B,h=t&d.W,f=a?g:g[e]||(g[e]={}),p=a?m:l?m[e]:(m[e]||{})[v];for(r in n=a?e:n)(i=!s&&p&&r in p)&&r in f||(o=(i?p:n)[r],f[r]=a&&"function"!=typeof p[r]?n[r]:c&&i?y(o,m):h&&p[r]==o?function(e){function t(t){return this instanceof e?new e(t):e(t)}return t[v]=e[v],t}(o):u&&"function"==typeof o?y(Function.call,o):o,u&&((f[v]||(f[v]={}))[r]=o))}var m=n(20),g=n(21),y=n(22),v="prototype";d.F=1,d.G=2,d.S=4,d.P=8,d.B=16,d.W=32,t.exports=d},function(t,e){t=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},function(t,e){t=t.exports={version:"1.2.6"};"number"==typeof __e&&(__e=t)},function(t,e,n){var o=n(23);t.exports=function(r,i,t){if(o(r),void 0===i)return r;switch(t){case 1:return function(t){return r.call(i,t)};case 2:return function(t,e){return r.call(i,t,e)};case 3:return function(t,e,n){return r.call(i,t,e,n)}}return function(){return r.apply(i,arguments)}}},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0;var i=r(n(6));e.default=function(t){t.registerHelper("helperMissing",function(){if(1!==arguments.length)throw new i.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default,i=(e.__esModule=!0,n(5)),o=r(n(6));e.default=function(n){n.registerHelper("if",function(t,e){if(2!=arguments.length)throw new o.default("#if requires exactly one argument");return i.isFunction(t)&&(t=t.call(this)),!e.hash.includeZero&&!t||i.isEmpty(t)?e.inverse(this):e.fn(this)}),n.registerHelper("unless",function(t,e){if(2!=arguments.length)throw new o.default("#unless requires exactly one argument");return n.helpers.if.call(this,t,{fn:e.inverse,inverse:e.fn,hash:e.hash})})},t.exports=e.default},function(t,e){"use strict";e.__esModule=!0,e.default=function(i){i.registerHelper("log",function(){for(var t=[void 0],e=arguments[arguments.length-1],n=0;n<arguments.length-1;n++)t.push(arguments[n]);var r=1;null!=e.hash.level?r=e.hash.level:e.data&&null!=e.data.level&&(r=e.data.level),t[0]=r,i.log.apply(i,t)})},t.exports=e.default},function(t,e){"use strict";e.__esModule=!0,e.default=function(t){t.registerHelper("lookup",function(t,e,n){return t&&n.lookupProperty(t,e)})},t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default,i=(e.__esModule=!0,n(5)),o=r(n(6));e.default=function(t){t.registerHelper("with",function(t,e){if(2!=arguments.length)throw new o.default("#with requires exactly one argument");i.isFunction(t)&&(t=t.call(this));var n,r=e.fn;return i.isEmpty(t)?e.inverse(this):(n=e.data,e.data&&e.ids&&((n=i.createFrame(e.data)).contextPath=i.appendContextPath(e.data.contextPath,e.ids[0])),r(t,{data:n,blockParams:i.blockParams([t],[n&&n.contextPath])}))})},t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0,e.registerDefaultDecorators=function(t){i.default(t)};var i=r(n(31))},function(t,e,n){"use strict";e.__esModule=!0;var s=n(5);e.default=function(t){t.registerDecorator("inline",function(r,i,o,t){var e=r;return i.partials||(i.partials={},e=function(t,e){var n=o.partials,t=(o.partials=s.extend({},n,i.partials),r(t,e));return o.partials=n,t}),i.partials[t.args[0]]=t.fn,e})},t.exports=e.default},function(t,e,n){"use strict";e.__esModule=!0;var r=n(5),i={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(t){var e;return t="string"==typeof t?0<=(e=r.indexOf(i.methodMap,t.toLowerCase()))?e:parseInt(t,10):t},log:function(t){if(t=i.lookupLevel(t),"undefined"!=typeof console&&i.lookupLevel(i.level)<=t){t=i.methodMap[t];console[t]||(t="log");for(var e=arguments.length,n=Array(1<e?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];console[t].apply(console,n)}}};e.default=i,t.exports=e.default},function(t,e,n){"use strict";var r=n(34).default,i=n(13).default,o=n(3).default,s=(e.__esModule=!0,e.createProtoAccessControl=function(t){var e=r(null),n=(e.constructor=!1,e.__defineGetter__=!1,e.__defineSetter__=!1,e.__lookupGetter__=!1,r(null));return n.__proto__=!1,{properties:{whitelist:s.createNewLookupObject(n,t.allowedProtoProperties),defaultValue:t.allowProtoPropertiesByDefault},methods:{whitelist:s.createNewLookupObject(e,t.allowedProtoMethods),defaultValue:t.allowProtoMethodsByDefault}}},e.resultIsAllowed=function(t,e,n){return u("function"==typeof t?e.methods:e.properties,n)},e.resetLoggedProperties=function(){i(l).forEach(function(t){delete l[t]})},n(36)),a=o(n(32)),l=r(null);function u(t,e){return void 0!==t.whitelist[e]?!0===t.whitelist[e]:void 0!==t.defaultValue?t.defaultValue:(!0!==l[t=e]&&(l[t]=!0,a.log("error",'Handlebars: Access has been denied to resolve the property "'+t+'" because it is not an "own property" of its parent.\nYou can add a runtime option to disable the check or this warning:\nSee https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details')),!1)}},function(t,e,n){t.exports={default:n(35),__esModule:!0}},function(t,e,n){var r=n(9);t.exports=function(t,e){return r.create(t,e)}},function(t,e,n){"use strict";var r=n(34).default,i=(e.__esModule=!0,e.createNewLookupObject=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return i.extend.apply(void 0,[r(null)].concat(e))},n(5))},function(t,e){"use strict";function n(t){this.string=t}e.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},e.default=n,t.exports=e.default},function(t,e,n){"use strict";var r=n(39).default,o=n(13).default,i=n(3).default,s=n(1).default;e.__esModule=!0,e.checkRevision=function(t){var e=t&&t[0]||1,n=f.COMPILER_REVISION;if(!(e>=f.LAST_COMPATIBLE_COMPILER_REVISION&&e<=f.COMPILER_REVISION))throw e<f.LAST_COMPATIBLE_COMPILER_REVISION?(n=f.REVISION_CHANGES[n],e=f.REVISION_CHANGES[e],new h.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+n+") or downgrade your runtime to an older version ("+e+").")):new h.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+t[1]+").")},e.template=function(l,u){if(!u)throw new h.default("No environment passed to template");if(!l||!l.main)throw new h.default("Unknown template object: "+typeof l);l.main.decorator=l.main_d,u.VM.checkRevision(l.compiler);var n=l.compiler&&7===l.compiler[0];var s={strict:function(t,e,n){if(t&&e in t)return s.lookupProperty(t,e);throw new h.default('"'+e+'" not defined in '+t,{loc:n})},lookupProperty:function(t,e){var n=t[e];return null==n||Object.prototype.hasOwnProperty.call(t,e)||m.resultIsAllowed(n,s.protoAccessControl,e)?n:void 0},lookup:function(t,e){for(var n=t.length,r=0;r<n;r++)if(null!=(t[r]&&s.lookupProperty(t[r],e)))return t[r][e]},lambda:function(t,e){return"function"==typeof t?t.call(e):t},escapeExpression:c.escapeExpression,invokePartial:function(t,e,n){n.hash&&(e=c.extend({},e,n.hash),n.ids&&(n.ids[0]=!0)),t=u.VM.resolvePartial.call(this,t,e,n);var r=c.extend({},n,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),i=u.VM.invokePartial.call(this,t,e,r);if(null==i&&u.compile&&(n.partials[n.name]=u.compile(t,l.compilerOptions,u),i=n.partials[n.name](e,r)),null==i)throw new h.default("The partial "+n.name+" could not be compiled when running in runtime-only mode");if(n.indent){for(var o=i.split("\n"),s=0,a=o.length;s<a&&(o[s]||s+1!==a);s++)o[s]=n.indent+o[s];i=o.join("\n")}return i},fn:function(t){var e=l[t];return e.decorator=l[t+"_d"],e},programs:[],program:function(t,e,n,r,i){var o=this.programs[t],s=this.fn(t);return o=e||i||r||n?g(this,t,s,e,n,r,i):o||(this.programs[t]=g(this,t,s))},data:function(t,e){for(;t&&e--;)t=t._parent;return t},mergeIfNeeded:function(t,e){var n=t||e;return n=t&&e&&t!==e?c.extend({},e,t):n},nullContext:r({}),noop:u.VM.noop,compilerInfo:l.compiler};function a(t){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],n=e.data,r=(a._setup(e),!e.partial&&l.useData&&(n=function(t,e){e&&"root"in e||((e=e?f.createFrame(e):{}).root=t);return e}(t,n)),void 0),i=l.useBlockParams?[]:void 0;function o(t){return""+l.main(s,t,s.helpers,s.partials,n,i,r)}return l.useDepths&&(r=e.depths?t!=e.depths[0]?[t].concat(e.depths):e.depths:[t]),(o=y(l.main,o,s,e.depths||[],n,i))(t,e)}return a.isTop=!0,a._setup=function(t){var e,r,i;t.partial?(s.protoAccessControl=t.protoAccessControl,s.helpers=t.helpers,s.partials=t.partials,s.decorators=t.decorators,s.hooks=t.hooks):(e=c.extend({},u.helpers,t.helpers),i=s,o(r=e).forEach(function(t){var e,n=r[t];r[t]=(e=i.lookupProperty,d.wrapHelper(n,function(t){return c.extend({lookupProperty:e},t)}))}),s.helpers=e,l.usePartial&&(s.partials=s.mergeIfNeeded(t.partials,u.partials)),(l.usePartial||l.useDecorators)&&(s.decorators=c.extend({},u.decorators,t.decorators)),s.hooks={},s.protoAccessControl=m.createProtoAccessControl(t),e=t.allowCallsToHelperMissing||n,p.moveHelperToHooks(s,"helperMissing",e),p.moveHelperToHooks(s,"blockHelperMissing",e))},a._child=function(t,e,n,r){if(l.useBlockParams&&!n)throw new h.default("must pass block params");if(l.useDepths&&!r)throw new h.default("must pass parent depths");return g(s,t,l[t],e,0,n,r)},a},e.wrapProgram=g,e.resolvePartial=function(t,e,n){t?t.call||n.name||(n.name=t,t=n.partials[t]):t="@partial-block"===n.name?n.data["partial-block"]:n.partials[n.name];return t},e.invokePartial=function(t,e,r){var i=r.data&&r.data["partial-block"];r.partial=!0,r.ids&&(r.data.contextPath=r.ids[0]||r.data.contextPath);var o=void 0;r.fn&&r.fn!==a&&!function(){r.data=f.createFrame(r.data);var n=r.fn;o=r.data["partial-block"]=function(t){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1];return e.data=f.createFrame(e.data),e.data["partial-block"]=i,n(t,e)},n.partials&&(r.partials=c.extend({},r.partials,n.partials))}();void 0===t&&o&&(t=o);{if(void 0===t)throw new h.default("The partial "+r.name+" could not be found");if(t instanceof Function)return t(e,r)}},e.noop=a;var c=i(n(5)),h=s(n(6)),f=n(4),p=n(10),d=n(43),m=n(33);function g(r,t,i,o,e,s,a){function n(t){var e=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],n=a;return!a||t==a[0]||t===r.nullContext&&null===a[0]||(n=[t].concat(a)),i(r,t,r.helpers,r.partials,e.data||o,s&&[e.blockParams].concat(s),n)}return(n=y(i,n,r,a,o,s)).program=t,n.depth=a?a.length:0,n.blockParams=e||0,n}function a(){return""}function y(t,e,n,r,i,o){return t.decorator&&(e=t.decorator(e,t={},n,r&&r[0],i,o,r),c.extend(e,t)),e}},function(t,e,n){t.exports={default:n(40),__esModule:!0}},function(t,e,n){n(41),t.exports=n(21).Object.seal},function(t,e,n){var r=n(42);n(18)("seal",function(e){return function(t){return e&&r(t)?e(t):t}})},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e){"use strict";e.__esModule=!0,e.wrapHelper=function(e,n){return"function"==typeof e?function(){var t=arguments[arguments.length-1];return arguments[arguments.length-1]=n(t),e.apply(this,arguments)}:e}},function(t,e){!function(r){"use strict";e.__esModule=!0,e.default=function(t){var e=void 0!==r?r:window,n=e.Handlebars;t.noConflict=function(){return e.Handlebars===t&&(e.Handlebars=n),t}},t.exports=e.default}.call(e,function(){return this}())},function(t,e){"use strict";e.__esModule=!0;var n={helpers:{helperExpression:function(t){return"SubExpression"===t.type||("MustacheStatement"===t.type||"BlockStatement"===t.type)&&!!(t.params&&t.params.length||t.hash)},scopedId:function(t){return/^\.|this\b/.test(t.original)},simpleId:function(t){return 1===t.parts.length&&!n.helpers.scopedId(t)&&!t.depth}}};e.default=n,t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default,i=n(3).default;e.__esModule=!0,e.parseWithoutProcessing=l,e.parse=function(t,e){t=l(t,e);return new s.default(e).accept(t)};var o=r(n(47)),s=r(n(48)),r=i(n(50)),i=n(5),a=(e.parser=o.default,{});function l(t,e){return"Program"===t.type?t:((o.default.yy=a).locInfo=function(t){return new a.SourceLocation(e&&e.srcName,t)},o.default.parse(t))}i.extend(a,r)},function(t,e){"use strict";e.__esModule=!0;r={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)},setInput:function(t){return this._input=t,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,n=t.split(/(?:\r\n?|\n)/g),t=(this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e-1),this.offset-=e,this.match.split(/(?:\r\n?|\n)/g)),r=(this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),n.length-1&&(this.yylineno-=n.length-1),this.yylloc.range);return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===t.length?this.yylloc.first_column:0)+t[t.length-n.length].length-n[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[r[0],r[0]+this.yyleng-e]),this},more:function(){return this._more=!0,this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(20<t.length?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(20<t.length?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,n,r,i=this._currentRules(),o=0;o<i.length&&(!(e=this._input.match(this.rules[i[o]]))||t&&!(e[0].length>t[0].length)||(t=e,n=o,this.options.flex));o++);return t?((r=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],r=this.performAction.call(this,this.yy,this,i[n],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r||void 0):""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var t=this.next();return void 0!==t?t:this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(t){this.begin(t)},options:{},performAction:function(t,n,e,r){function i(t,e){return n.yytext=n.yytext.substring(t,n.yyleng-e+t)}switch(e){case 0:if("\\\\"===n.yytext.slice(-2)?(i(0,1),this.begin("mu")):"\\"===n.yytext.slice(-1)?(i(0,1),this.begin("emu")):this.begin("mu"),n.yytext)return 15;break;case 1:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),"raw"===this.conditionStack[this.conditionStack.length-1]?15:(i(5,9),"END_RAW_BLOCK");case 5:return 15;case 6:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:return 48;case 21:this.unput(n.yytext),this.popState(),this.begin("com");break;case 22:return this.popState(),14;case 23:return 48;case 24:return 73;case 25:case 26:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return n.yytext=i(1,2).replace(/\\"/g,'"'),80;case 32:return n.yytext=i(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 41:return 72;case 42:return n.yytext=n.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^\/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!(n={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(t,e,n,r,i,o,s){var a=o.length-1;switch(i){case 1:return o[a-1];case 2:this.$=r.prepareProgram(o[a]);break;case 3:case 4:case 5:case 6:case 7:case 8:this.$=o[a];break;case 9:this.$={type:"CommentStatement",value:r.stripComment(o[a]),strip:r.stripFlags(o[a],o[a]),loc:r.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:o[a],value:o[a],loc:r.locInfo(this._$)};break;case 11:this.$=r.prepareRawBlock(o[a-2],o[a-1],o[a],this._$);break;case 12:this.$={path:o[a-3],params:o[a-2],hash:o[a-1]};break;case 13:this.$=r.prepareBlock(o[a-3],o[a-2],o[a-1],o[a],!1,this._$);break;case 14:this.$=r.prepareBlock(o[a-3],o[a-2],o[a-1],o[a],!0,this._$);break;case 15:this.$={open:o[a-5],path:o[a-4],params:o[a-3],hash:o[a-2],blockParams:o[a-1],strip:r.stripFlags(o[a-5],o[a])};break;case 16:case 17:this.$={path:o[a-4],params:o[a-3],hash:o[a-2],blockParams:o[a-1],strip:r.stripFlags(o[a-5],o[a])};break;case 18:this.$={strip:r.stripFlags(o[a-1],o[a-1]),program:o[a]};break;case 19:var l=r.prepareBlock(o[a-2],o[a-1],o[a],o[a],!1,this._$),l=r.prepareProgram([l],o[a-1].loc);l.chained=!0,this.$={strip:o[a-2].strip,program:l,chain:!0};break;case 20:this.$=o[a];break;case 21:this.$={path:o[a-1],strip:r.stripFlags(o[a-2],o[a])};break;case 22:case 23:this.$=r.prepareMustache(o[a-3],o[a-2],o[a-1],o[a-4],r.stripFlags(o[a-4],o[a]),this._$);break;case 24:this.$={type:"PartialStatement",name:o[a-3],params:o[a-2],hash:o[a-1],indent:"",strip:r.stripFlags(o[a-4],o[a]),loc:r.locInfo(this._$)};break;case 25:this.$=r.preparePartialBlock(o[a-2],o[a-1],o[a],this._$);break;case 26:this.$={path:o[a-3],params:o[a-2],hash:o[a-1],strip:r.stripFlags(o[a-4],o[a])};break;case 27:case 28:this.$=o[a];break;case 29:this.$={type:"SubExpression",path:o[a-3],params:o[a-2],hash:o[a-1],loc:r.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:o[a],loc:r.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:r.id(o[a-2]),value:o[a],loc:r.locInfo(this._$)};break;case 32:this.$=r.id(o[a-1]);break;case 33:case 34:this.$=o[a];break;case 35:this.$={type:"StringLiteral",value:o[a],original:o[a],loc:r.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(o[a]),original:Number(o[a]),loc:r.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:"true"===o[a],original:"true"===o[a],loc:r.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:r.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:r.locInfo(this._$)};break;case 40:case 41:this.$=o[a];break;case 42:this.$=r.preparePath(!0,o[a],this._$);break;case 43:this.$=r.preparePath(!1,o[a],this._$);break;case 44:o[a-2].push({part:r.id(o[a]),original:o[a],separator:o[a-1]}),this.$=o[a-2];break;case 45:this.$=[{part:r.id(o[a]),original:o[a]}];break;case 46:this.$=[];break;case 47:o[a-1].push(o[a]);break;case 48:this.$=[];break;case 49:o[a-1].push(o[a]);break;case 50:this.$=[];break;case 51:o[a-1].push(o[a]);break;case 58:this.$=[];break;case 59:o[a-1].push(o[a]);break;case 64:this.$=[];break;case 65:o[a-1].push(o[a]);break;case 70:this.$=[];break;case 71:o[a-1].push(o[a]);break;case 78:this.$=[];break;case 79:o[a-1].push(o[a]);break;case 82:this.$=[];break;case 83:o[a-1].push(o[a]);break;case 86:this.$=[];break;case 87:o[a-1].push(o[a]);break;case 90:this.$=[];break;case 91:o[a-1].push(o[a]);break;case 94:this.$=[];break;case 95:o[a-1].push(o[a]);break;case 98:this.$=[o[a]];break;case 99:o[a-1].push(o[a]);break;case 100:this.$=[o[a]];break;case 101:o[a-1].push(o[a])}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(t,e){throw new Error(t)},parse:function(t){var e=this,n=[0],r=[null],i=[],o=this.table,s="",a=0,l=0,u=0,c=(this.lexer.setInput(t),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,void 0===(this.yy.parser=this).lexer.yylloc&&(this.lexer.yylloc={}),this.lexer.yylloc),h=(i.push(c),this.lexer.options&&this.lexer.options.ranges);"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var f,p,d,m,g,y,v,x,b={};;){if(d=n[n.length-1],void 0===(m=this.defaultActions[d]||(null==f&&(_=void 0,f=_="number"!=typeof(_=e.lexer.lex()||1)&&e.symbols_[_]||_),o[d]&&o[d][f]))||!m.length||!m[0]){var _="";if(!u){for(g in x=[],o[d])this.terminals_[g]&&2<g&&x.push("'"+this.terminals_[g]+"'");_=this.lexer.showPosition?"Parse error on line "+(a+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+x.join(", ")+", got '"+(this.terminals_[f]||f)+"'":"Parse error on line "+(a+1)+": Unexpected "+(1==f?"end of input":"'"+(this.terminals_[f]||f)+"'"),this.parseError(_,{text:this.lexer.match,token:this.terminals_[f]||f,line:this.lexer.yylineno,loc:c,expected:x})}}if(m[0]instanceof Array&&1<m.length)throw new Error("Parse Error: multiple actions possible at state: "+d+", token: "+f);switch(m[0]){case 1:n.push(f),r.push(this.lexer.yytext),i.push(this.lexer.yylloc),n.push(m[1]),f=null,p?(f=p,p=null):(l=this.lexer.yyleng,s=this.lexer.yytext,a=this.lexer.yylineno,c=this.lexer.yylloc,0<u&&u--);break;case 2:if(y=this.productions_[m[1]][1],b.$=r[r.length-y],b._$={first_line:i[i.length-(y||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(y||1)].first_column,last_column:i[i.length-1].last_column},h&&(b._$.range=[i[i.length-(y||1)].range[0],i[i.length-1].range[1]]),void 0!==(v=this.performAction.call(b,s,l,a,this.yy,m[1],r,i)))return v;y&&(n=n.slice(0,-1*y*2),r=r.slice(0,-1*y),i=i.slice(0,-1*y)),n.push(this.productions_[m[1]][0]),r.push(b.$),i.push(b._$),v=o[n[n.length-2]][n[n.length-1]],n.push(v);break;case 3:return!0}}return!0}})},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}},n.lexer=r;var n,r=new((i.prototype=n).Parser=i);function i(){this.yy={}}e.default=r,t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0;r=r(n(49));function i(){this.options=arguments.length<=0||void 0===arguments[0]?{}:arguments[0]}function f(t,e,n){var r=t[(e=void 0===e?t.length:e)-1],t=t[e-2];return r?"ContentStatement"===r.type?(t||!n?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(r.original):void 0:n}function p(t,e,n){var r=t[(e=void 0===e?-1:e)+1],t=t[e+2];return r?"ContentStatement"===r.type?(t||!n?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(r.original):void 0:n}function d(t,e,n){t=t[null==e?0:e+1];!t||"ContentStatement"!==t.type||!n&&t.rightStripped||(e=t.value,t.value=t.value.replace(n?/^\s+/:/^[ \t]*\r?\n?/,""),t.rightStripped=t.value!==e)}function m(t,e,n){t=t[null==e?t.length-1:e-1];if(t&&"ContentStatement"===t.type&&(n||!t.leftStripped))return e=t.value,t.value=t.value.replace(n?/\s+$/:/[ \t]+$/,""),t.leftStripped=t.value!==e,t.leftStripped}(i.prototype=new r.default).Program=function(t){for(var e=!this.options.ignoreStandalone,n=!this.isRootSeen,r=(this.isRootSeen=!0,t.body),i=0,o=r.length;i<o;i++){var s,a,l,u,c=r[i],h=this.accept(c);h&&(u=f(r,i,n),s=p(r,i,n),a=h.openStandalone&&u,l=h.closeStandalone&&s,u=h.inlineStandalone&&u&&s,h.close&&d(r,i,!0),h.open&&m(r,i,!0),e&&u&&(d(r,i),m(r,i)&&"PartialStatement"===c.type&&(c.indent=/([ \t]+$)/.exec(r[i-1].original)[1])),e&&a&&(d((c.program||c.inverse).body),m(r,i)),e&&l&&(d(r,i),m((c.inverse||c.program).body)))}return t},i.prototype.BlockStatement=i.prototype.DecoratorBlock=i.prototype.PartialBlockStatement=function(t){this.accept(t.program),this.accept(t.inverse);var e=t.program||t.inverse,n=t.program&&t.inverse,r=n,i=n;if(n&&n.chained)for(r=n.body[0].program;i.chained;)i=i.body[i.body.length-1].program;var o={open:t.openStrip.open,close:t.closeStrip.close,openStandalone:p(e.body),closeStandalone:f((r||e).body)};return t.openStrip.close&&d(e.body,null,!0),n?((n=t.inverseStrip).open&&m(e.body,null,!0),n.close&&d(r.body,null,!0),t.closeStrip.open&&m(i.body,null,!0),!this.options.ignoreStandalone&&f(e.body)&&p(r.body)&&(m(e.body),d(r.body))):t.closeStrip.open&&m(e.body,null,!0),o},i.prototype.Decorator=i.prototype.MustacheStatement=function(t){return t.strip},i.prototype.PartialStatement=i.prototype.CommentStatement=function(t){t=t.strip||{};return{inlineStandalone:!0,open:t.open,close:t.close}},e.default=i,t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0;var i=r(n(6));function o(){this.parents=[]}function s(t){this.acceptRequired(t,"path"),this.acceptArray(t.params),this.acceptKey(t,"hash")}function a(t){s.call(this,t),this.acceptKey(t,"program"),this.acceptKey(t,"inverse")}function l(t){this.acceptRequired(t,"name"),this.acceptArray(t.params),this.acceptKey(t,"hash")}o.prototype={constructor:o,mutating:!1,acceptKey:function(t,e){var n=this.accept(t[e]);if(this.mutating){if(n&&!o.prototype[n.type])throw new i.default('Unexpected node type "'+n.type+'" found when accepting '+e+" on "+t.type);t[e]=n}},acceptRequired:function(t,e){if(this.acceptKey(t,e),!t[e])throw new i.default(t.type+" requires "+e)},acceptArray:function(t){for(var e=0,n=t.length;e<n;e++)this.acceptKey(t,e),t[e]||(t.splice(e,1),e--,n--)},accept:function(t){if(t){if(!this[t.type])throw new i.default("Unknown type: "+t.type,t);this.current&&this.parents.unshift(this.current),this.current=t;var e=this[t.type](t);return this.current=this.parents.shift(),!this.mutating||e?e:!1!==e?t:void 0}},Program:function(t){this.acceptArray(t.body)},MustacheStatement:s,Decorator:s,BlockStatement:a,DecoratorBlock:a,PartialStatement:l,PartialBlockStatement:function(t){l.call(this,t),this.acceptKey(t,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:s,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(t){this.acceptArray(t.pairs)},HashPair:function(t){this.acceptRequired(t,"value")}},e.default=o,t.exports=e.default},function(t,e,n){"use strict";var r=n(1).default;e.__esModule=!0,e.SourceLocation=function(t,e){this.source=t,this.start={line:e.first_line,column:e.first_column},this.end={line:e.last_line,column:e.last_column}},e.id=function(t){return/^\[.*\]$/.test(t)?t.substring(1,t.length-1):t},e.stripFlags=function(t,e){return{open:"~"===t.charAt(2),close:"~"===e.charAt(e.length-3)}},e.stripComment=function(t){return t.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},e.preparePath=function(t,e,n){n=this.locInfo(n);for(var r=t?"@":"",i=[],o=0,s=0,a=e.length;s<a;s++){var l=e[s].part,u=e[s].original!==l;if(r+=(e[s].separator||"")+l,u||".."!==l&&"."!==l&&"this"!==l)i.push(l);else{if(0<i.length)throw new c.default("Invalid path: "+r,{loc:n});".."===l&&o++}}return{type:"PathExpression",data:t,depth:o,parts:i,original:r,loc:n}},e.prepareMustache=function(t,e,n,r,i,o){var s=r.charAt(3)||r.charAt(2),s="{"!==s&&"&"!==s;return{type:/\*/.test(r)?"Decorator":"MustacheStatement",path:t,params:e,hash:n,escaped:s,strip:i,loc:this.locInfo(o)}},e.prepareRawBlock=function(t,e,n,r){u(t,n),r=this.locInfo(r);n={type:"Program",body:e,strip:{},loc:r};return{type:"BlockStatement",path:t.path,params:t.params,hash:t.hash,program:n,openStrip:{},inverseStrip:{},closeStrip:{},loc:r}},e.prepareBlock=function(t,e,n,r,i,o){r&&r.path&&u(t,r);var s=/\*/.test(t.open),a=void(e.blockParams=t.blockParams),l=void 0;if(n){if(s)throw new c.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=r.strip),l=n.strip,a=n.program}i&&(i=a,a=e,e=i);return{type:s?"DecoratorBlock":"BlockStatement",path:t.path,params:t.params,hash:t.hash,program:e,inverse:a,openStrip:t.strip,inverseStrip:l,closeStrip:r&&r.strip,loc:this.locInfo(o)}},e.prepareProgram=function(t,e){{var n,r;!e&&t.length&&(n=t[0].loc,r=t[t.length-1].loc,n&&r&&(e={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:r.end.line,column:r.end.column}}))}return{type:"Program",body:t,strip:{},loc:e}},e.preparePartialBlock=function(t,e,n,r){return u(t,n),{type:"PartialBlockStatement",name:t.path,params:t.params,hash:t.hash,program:e,openStrip:t.strip,closeStrip:n&&n.strip,loc:this.locInfo(r)}};var c=r(n(6));function u(t,e){var n;if(e=e.path?e.path.original:e,t.path.original!==e)throw n={loc:t.path.loc},new c.default(t.path.original+" doesn't match "+e,n)}},function(t,e,n){"use strict";var r=n(34).default,i=n(1).default;e.__esModule=!0,e.Compiler=u,e.precompile=function(t,e,n){if(null==t||"string"!=typeof t&&"Program"!==t.type)throw new s.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+t);"data"in(e=e||{})||(e.data=!0);e.compat&&(e.useDepths=!0);t=n.parse(t,e),t=(new n.Compiler).compile(t,e);return(new n.JavaScriptCompiler).compile(t,e)},e.compile=function(e,n,r){void 0===n&&(n={});if(null==e||"string"!=typeof e&&"Program"!==e.type)throw new s.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(n=a.extend({},n))||(n.data=!0);n.compat&&(n.useDepths=!0);var i=void 0;function o(){var t=r.parse(e,n),t=(new r.Compiler).compile(t,n),t=(new r.JavaScriptCompiler).compile(t,n,void 0,!0);return r.template(t)}function t(t,e){return(i=i||o()).call(this,t,e)}return t._setup=function(t){return(i=i||o())._setup(t)},t._child=function(t,e,n,r){return(i=i||o())._child(t,e,n,r)},t};var s=i(n(6)),a=n(5),o=i(n(45)),l=[].slice;function u(){}function c(t){var e;t.path.parts||(e=t.path,t.path={type:"PathExpression",data:!1,depth:0,parts:[e.original+""],original:e.original+"",loc:e.loc})}u.prototype={compiler:u,equals:function(t){var e=this.opcodes.length;if(t.opcodes.length!==e)return!1;for(var n=0;n<e;n++){var r=this.opcodes[n],i=t.opcodes[n];if(r.opcode!==i.opcode||!function t(e,n){if(e===n)return!0;if(a.isArray(e)&&a.isArray(n)&&e.length===n.length){for(var r=0;r<e.length;r++)if(!t(e[r],n[r]))return!1;return!0}}(r.args,i.args))return!1}for(e=this.children.length,n=0;n<e;n++)if(!this.children[n].equals(t.children[n]))return!1;return!0},guid:0,compile:function(t,e){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=e,this.stringParams=e.stringParams,this.trackIds=e.trackIds,e.blockParams=e.blockParams||[],e.knownHelpers=a.extend(r(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},e.knownHelpers),this.accept(t)},compileProgram:function(t){var t=(new this.compiler).compile(t,this.options),e=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[e]=t,this.useDepths=this.useDepths||t.useDepths,e},accept:function(t){if(!this[t.type])throw new s.default("Unknown type: "+t.type,t);this.sourceNode.unshift(t);t=this[t.type](t);return this.sourceNode.shift(),t},Program:function(t){this.options.blockParams.unshift(t.blockParams);for(var e=t.body,n=e.length,r=0;r<n;r++)this.accept(e[r]);return this.options.blockParams.shift(),this.isSimple=1===n,this.blockParams=t.blockParams?t.blockParams.length:0,this},BlockStatement:function(t){c(t);var e=t.program,n=t.inverse,e=e&&this.compileProgram(e),n=n&&this.compileProgram(n),r=this.classifySexpr(t);"helper"===r?this.helperSexpr(t,e,n):"simple"===r?(this.simpleSexpr(t),this.opcode("pushProgram",e),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",t.path.original)):(this.ambiguousSexpr(t,e,n),this.opcode("pushProgram",e),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(t){var e=t.program&&this.compileProgram(t.program),e=this.setupFullMustacheParams(t,e,void 0),t=t.path;this.useDecorators=!0,this.opcode("registerDecorator",e.length,t.original)},PartialStatement:function(t){this.usePartial=!0;var e=(e=t.program)&&this.compileProgram(t.program),n=t.params;if(1<n.length)throw new s.default("Unsupported number of partial arguments: "+n.length,t);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var n=t.name.original,r="SubExpression"===t.name.type,e=(r&&this.accept(t.name),this.setupFullMustacheParams(t,e,void 0,!0),t.indent||"");this.options.preventIndent&&e&&(this.opcode("appendContent",e),e=""),this.opcode("invokePartial",r,n,e),this.opcode("append")},PartialBlockStatement:function(t){this.PartialStatement(t)},MustacheStatement:function(t){this.SubExpression(t),t.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(t){this.DecoratorBlock(t)},ContentStatement:function(t){t.value&&this.opcode("appendContent",t.value)},CommentStatement:function(){},SubExpression:function(t){c(t);var e=this.classifySexpr(t);"simple"===e?this.simpleSexpr(t):"helper"===e?this.helperSexpr(t):this.ambiguousSexpr(t)},ambiguousSexpr:function(t,e,n){var t=t.path,r=t.parts[0],i=null!=e||null!=n;this.opcode("getContext",t.depth),this.opcode("pushProgram",e),this.opcode("pushProgram",n),t.strict=!0,this.accept(t),this.opcode("invokeAmbiguous",r,i)},simpleSexpr:function(t){t=t.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(t,e,n){var e=this.setupFullMustacheParams(t,e,n),n=t.path,r=n.parts[0];if(this.options.knownHelpers[r])this.opcode("invokeKnownHelper",e.length,r);else{if(this.options.knownHelpersOnly)throw new s.default("You specified knownHelpersOnly, but used the unknown helper "+r,t);n.strict=!0,n.falsy=!0,this.accept(n),this.opcode("invokeHelper",e.length,n.original,o.default.helpers.simpleId(n))}},PathExpression:function(t){this.addDepth(t.depth),this.opcode("getContext",t.depth);var e=t.parts[0],n=o.default.helpers.scopedId(t),r=!t.depth&&!n&&this.blockParamIndex(e);r?this.opcode("lookupBlockParam",r,t.parts):e?t.data?(this.options.data=!0,this.opcode("lookupData",t.depth,t.parts,t.strict)):this.opcode("lookupOnContext",t.parts,t.falsy,t.strict,n):this.opcode("pushContext")},StringLiteral:function(t){this.opcode("pushString",t.value)},NumberLiteral:function(t){this.opcode("pushLiteral",t.value)},BooleanLiteral:function(t){this.opcode("pushLiteral",t.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(t){var e=t.pairs,n=0,r=e.length;for(this.opcode("pushHash");n<r;n++)this.pushParam(e[n].value);for(;n--;)this.opcode("assignToHash",e[n].key);this.opcode("popHash")},opcode:function(t){this.opcodes.push({opcode:t,args:l.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(t){t&&(this.useDepths=!0)},classifySexpr:function(t){var e=o.default.helpers.simpleId(t.path),n=e&&!!this.blockParamIndex(t.path.parts[0]),r=!n&&o.default.helpers.helperExpression(t),n=!n&&(r||e);return n&&!r&&(e=t.path.parts[0],(t=this.options).knownHelpers[e]?r=!0:t.knownHelpersOnly&&(n=!1)),r?"helper":n?"ambiguous":"simple"},pushParams:function(t){for(var e=0,n=t.length;e<n;e++)this.pushParam(t[e])},pushParam:function(t){var e,n,r=null!=t.value?t.value:t.original||"";this.stringParams?(r.replace&&(r=r.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),t.depth&&this.addDepth(t.depth),this.opcode("getContext",t.depth||0),this.opcode("pushStringParam",r,t.type),"SubExpression"===t.type&&this.accept(t)):(this.trackIds&&(e=void 0,(e=!t.parts||o.default.helpers.scopedId(t)||t.depth?e:this.blockParamIndex(t.parts[0]))?(n=t.parts.slice(1).join("."),this.opcode("pushId","BlockParam",e,n)):((r=t.original||r).replace&&(r=r.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",t.type,r))),this.accept(t))},setupFullMustacheParams:function(t,e,n,r){var i=t.params;return this.pushParams(i),this.opcode("pushProgram",e),this.opcode("pushProgram",n),t.hash?this.accept(t.hash):this.opcode("emptyHash",r),i},blockParamIndex:function(t){for(var e=0,n=this.options.blockParams.length;e<n;e++){var r=this.options.blockParams[e],i=r&&a.indexOf(r,t);if(r&&0<=i)return[e,i]}}}},function(t,e,n){"use strict";var s=n(13).default,r=n(1).default,i=(e.__esModule=!0,n(4)),f=r(n(6)),o=n(5),a=r(n(53));function l(t){this.value=t}function u(){}u.prototype={nameLookup:function(t,e){return this.internalNameLookup(t,e)},depthedLookup:function(t){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(t),")"]},compilerInfo:function(){var t=i.COMPILER_REVISION;return[t,i.REVISION_CHANGES[t]]},appendToBuffer:function(t,e,n){return o.isArray(t)||(t=[t]),t=this.source.wrap(t,e),this.environment.isSimple?["return ",t,";"]:n?["buffer += ",t,";"]:(t.appendToBuffer=!0,t)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(t,e){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",t,",",JSON.stringify(e),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(t,e,n,r){this.environment=t,this.options=e,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!r,this.name=this.environment.name,this.isChild=!!n,this.context=n||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(t,e),this.useDepths=this.useDepths||t.useDepths||t.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||t.useBlockParams;var i,o=t.opcodes,s=void 0,a=void 0,l=void 0;for(a=0,l=o.length;a<l;a++)i=o[a],this.source.currentLocation=i.loc,s=s||i.loc,this[i.opcode].apply(this,i.args);if(this.source.currentLocation=s,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new f.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),";\n"]),this.decorators.push("return fn;"),r?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend("function(fn, props, container, depth0, data, blockParams, depths) {\n"),this.decorators.push("}\n"),this.decorators=this.decorators.merge()));n=this.createFunctionContext(r);if(this.isChild)return n;for(var u={compiler:this.compilerInfo(),main:n},t=(this.decorators&&(u.main_d=this.decorators,u.useDecorators=!0),this.context),c=t.programs,h=t.decorators,a=0,l=c.length;a<l;a++)c[a]&&(u[a]=c[a],h[a]&&(u[a+"_d"]=h[a],u.useDecorators=!0));return this.environment.usePartial&&(u.usePartial=!0),this.options.data&&(u.useData=!0),this.useDepths&&(u.useDepths=!0),this.useBlockParams&&(u.useBlockParams=!0),this.options.compat&&(u.compat=!0),r?u.compilerOptions=this.options:(u.compiler=JSON.stringify(u.compiler),this.source.currentLocation={start:{line:1,column:0}},u=this.objectLiteral(u),e.srcName?(u=u.toStringWithSourceMap({file:e.destName})).map=u.map&&u.map.toString():u=u.toString()),u},preamble:function(){this.lastContext=0,this.source=new a.default(this.options.srcName),this.decorators=new a.default(this.options.srcName)},createFunctionContext:function(t){var n=this,r="",e=this.stackVars.concat(this.registers.list),i=(0<e.length&&(r+=", "+e.join(", ")),0),e=(s(this.aliases).forEach(function(t){var e=n.aliases[t];e.children&&1<e.referenceCount&&(r+=", alias"+ ++i+"="+t,e.children[0]="alias"+i)}),this.lookupPropertyFunctionIsUsed&&(r+=", "+this.lookupPropertyFunctionVarDeclaration()),["container","depth0","helpers","partials","data"]),o=((this.useBlockParams||this.useDepths)&&e.push("blockParams"),this.useDepths&&e.push("depths"),this.mergeSource(r));return t?(e.push(o),Function.apply(this,e)):this.source.wrap(["function(",e.join(","),") {\n  ",o,"}"])},mergeSource:function(t){var e=this.environment.isSimple,n=!this.forceBuffer,r=void 0,i=void 0,o=void 0,s=void 0;return this.source.each(function(t){t.appendToBuffer?(o?t.prepend("  + "):o=t,s=t):(o&&(i?o.prepend("buffer += "):r=!0,s.add(";"),o=s=void 0),i=!0,e||(n=!1))}),n?o?(o.prepend("return "),s.add(";")):i||this.source.push('return "";'):(t+=", buffer = "+(r?"":this.initializeBuffer()),o?(o.prepend("return buffer + "),s.add(";")):this.source.push("return buffer;")),t&&this.source.prepend("var "+t.substring(2)+(r?"":";\n")),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return"\n      lookupProperty = container.lookupProperty || function(parent, propertyName) {\n        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {\n          return parent[propertyName];\n        }\n        return undefined\n    }\n    ".trim()},blockValue:function(t){var e=this.aliasable("container.hooks.blockHelperMissing"),n=[this.contextName(0)],t=(this.setupHelperArgs(t,0,n),this.popStack());n.splice(1,0,t),this.push(this.source.functionCall(e,"call",n))},ambiguousBlockValue:function(){var t=this.aliasable("container.hooks.blockHelperMissing"),e=[this.contextName(0)],n=(this.setupHelperArgs("",0,e,!0),this.flushInline(),this.topStack());e.splice(1,0,n),this.pushSource(["if (!",this.lastHelper,") { ",n," = ",this.source.functionCall(t,"call",e),"}"])},appendContent:function(t){this.pendingContent?t=this.pendingContent+t:this.pendingLocation=this.source.currentLocation,this.pendingContent=t},append:function(){var t;this.isInline()?(this.replaceStack(function(t){return[" != null ? ",t,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()))):(t=this.popStack(),this.pushSource(["if (",t," != null) { ",this.appendToBuffer(t,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"]))},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(t){this.lastContext=t},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(t,e,n,r){var i=0;r||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(t[i++])),this.resolvePath("context",t,i,e,n)},lookupBlockParam:function(t,e){this.useBlockParams=!0,this.push(["blockParams[",t[0],"][",t[1],"]"]),this.resolvePath("context",e,1)},lookupData:function(t,e,n){t?this.pushStackLiteral("container.data(data, "+t+")"):this.pushStackLiteral("data"),this.resolvePath("data",e,0,!0,n)},resolvePath:function(n,r,i,o,t){var s=this;if(this.options.strict||this.options.assumeObjects)this.push(function(t,e,n,r){var i=e.popStack(),o=0,s=n.length;t&&s--;for(;o<s;o++)i=e.nameLookup(i,n[o],r);return t?[e.aliasable("container.strict"),"(",i,", ",e.quotedString(n[o]),", ",JSON.stringify(e.source.currentLocation)," )"]:i}(this.options.strict&&t,this,r,n));else for(var e=r.length;i<e;i++)this.replaceStack(function(t){var e=s.nameLookup(t,r[i],n);return o?[" && ",e]:[" != null ? ",e," : ",t]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(t,e){this.pushContext(),this.pushString(e),"SubExpression"!==e&&("string"==typeof t?this.pushString(t):this.pushStackLiteral(t))},emptyHash:function(t){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(t?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var t=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(t.ids)),this.stringParams&&(this.push(this.objectLiteral(t.contexts)),this.push(this.objectLiteral(t.types))),this.push(this.objectLiteral(t.values))},pushString:function(t){this.pushStackLiteral(this.quotedString(t))},pushLiteral:function(t){this.pushStackLiteral(t)},pushProgram:function(t){null!=t?this.pushStackLiteral(this.programExpression(t)):this.pushStackLiteral(null)},registerDecorator:function(t,e){var n=this.nameLookup("decorators",e,"decorator"),e=this.setupHelperArgs(e,t);this.decorators.push(["fn = ",this.decorators.functionCall(n,"",["fn","props","container",e])," || fn;"])},invokeHelper:function(t,e,n){var r=this.popStack(),t=this.setupHelper(t,e),e=[],n=(n&&e.push(t.name),e.push(r),this.options.strict||e.push(this.aliasable("container.hooks.helperMissing")),["(",this.itemsSeparatedBy(e,"||"),")"]),r=this.source.functionCall(n,"call",t.callParams);this.push(r)},itemsSeparatedBy:function(t,e){var n=[];n.push(t[0]);for(var r=1;r<t.length;r++)n.push(e,t[r]);return n},invokeKnownHelper:function(t,e){t=this.setupHelper(t,e);this.push(this.source.functionCall(t.name,"call",t.callParams))},invokeAmbiguous:function(t,e){this.useRegister("helper");var n=this.popStack(),e=(this.emptyHash(),this.setupHelper(0,t,e)),t=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",t,"helper")," || ",n,")"];this.options.strict||(t[0]="(helper = ",t.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",t,e.paramsInit?["),(",e.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",e.callParams)," : helper))"])},invokePartial:function(t,e,n){var r=[],i=this.setupParams(e,1,r);t&&(e=this.popStack(),delete i.name),n&&(i.indent=JSON.stringify(n)),i.helpers="helpers",i.partials="partials",i.decorators="container.decorators",t?r.unshift(e):r.unshift(this.nameLookup("partials",e,"partial")),this.options.compat&&(i.depths="depths"),i=this.objectLiteral(i),r.push(i),this.push(this.source.functionCall("container.invokePartial","",r))},assignToHash:function(t){var e=this.popStack(),n=void 0,r=void 0,i=void 0,o=(this.trackIds&&(i=this.popStack()),this.stringParams&&(r=this.popStack(),n=this.popStack()),this.hash);n&&(o.contexts[t]=n),r&&(o.types[t]=r),i&&(o.ids[t]=i),o.values[t]=e},pushId:function(t,e,n){"BlockParam"===t?this.pushStackLiteral("blockParams["+e[0]+"].path["+e[1]+"]"+(n?" + "+JSON.stringify("."+n):"")):"PathExpression"===t?this.pushString(e):"SubExpression"===t?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:u,compileChildren:function(t,e){for(var n=t.children,r=void 0,i=void 0,o=0,s=n.length;o<s;o++){var a,r=n[o],i=new this.compiler,l=this.matchExistingProgram(r);null==l?(this.context.programs.push(""),a=this.context.programs.length,r.index=a,r.name="program"+a,this.context.programs[a]=i.compile(r,e,this.context,!this.precompile),this.context.decorators[a]=i.decorators,this.context.environments[a]=r,this.useDepths=this.useDepths||i.useDepths,this.useBlockParams=this.useBlockParams||i.useBlockParams,r.useDepths=this.useDepths,r.useBlockParams=this.useBlockParams):(r.index=l.index,r.name="program"+l.index,this.useDepths=this.useDepths||l.useDepths,this.useBlockParams=this.useBlockParams||l.useBlockParams)}},matchExistingProgram:function(t){for(var e=0,n=this.context.environments.length;e<n;e++){var r=this.context.environments[e];if(r&&r.equals(t))return r}},programExpression:function(t){t=this.environment.children[t],t=[t.index,"data",t.blockParams];return(this.useBlockParams||this.useDepths)&&t.push("blockParams"),this.useDepths&&t.push("depths"),"container.program("+t.join(", ")+")"},useRegister:function(t){this.registers[t]||(this.registers[t]=!0,this.registers.list.push(t))},push:function(t){return t instanceof l||(t=this.source.wrap(t)),this.inlineStack.push(t),t},pushStackLiteral:function(t){this.push(new l(t))},pushSource:function(t){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),t&&this.source.push(t)},replaceStack:function(t){var e=["("],n=void 0,r=void 0,i=void 0;if(!this.isInline())throw new f.default("replaceStack on non-inline");var o=this.popStack(!0),s=(o instanceof l?(e=["(",n=[o.value]],i=!0):(r=!0,s=this.incrStack(),e=["((",this.push(s)," = ",o,")"],n=this.topStack()),t.call(this,n));i||this.popStack(),r&&this.stackSlot--,this.push(e.concat(s,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var t=this.inlineStack;this.inlineStack=[];for(var e=0,n=t.length;e<n;e++){var r,i=t[e];i instanceof l?this.compileStack.push(i):(r=this.incrStack(),this.pushSource([r," = ",i,";"]),this.compileStack.push(r))}},isInline:function(){return this.inlineStack.length},popStack:function(t){var e=this.isInline(),n=(e?this.inlineStack:this.compileStack).pop();if(!t&&n instanceof l)return n.value;if(!e){if(!this.stackSlot)throw new f.default("Invalid stack pop");this.stackSlot--}return n},topStack:function(){var t=this.isInline()?this.inlineStack:this.compileStack,t=t[t.length-1];return t instanceof l?t.value:t},contextName:function(t){return this.useDepths&&t?"depths["+t+"]":"depth"+t},quotedString:function(t){return this.source.quotedString(t)},objectLiteral:function(t){return this.source.objectLiteral(t)},aliasable:function(t){var e=this.aliases[t];return e?e.referenceCount++:((e=this.aliases[t]=this.source.wrap(t)).aliasable=!0,e.referenceCount=1),e},setupHelper:function(t,e,n){var r=[];return{params:r,paramsInit:this.setupHelperArgs(e,t,r,n),name:this.nameLookup("helpers",e,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(r)}},setupParams:function(t,e,n){for(var r,i={},o=[],s=[],a=[],l=!n,t=(l&&(n=[]),i.name=this.quotedString(t),i.hash=this.popStack(),this.trackIds&&(i.hashIds=this.popStack()),this.stringParams&&(i.hashTypes=this.popStack(),i.hashContexts=this.popStack()),this.popStack()),u=this.popStack(),c=((u||t)&&(i.fn=u||"container.noop",i.inverse=t||"container.noop"),e);c--;)r=this.popStack(),n[c]=r,this.trackIds&&(a[c]=this.popStack()),this.stringParams&&(s[c]=this.popStack(),o[c]=this.popStack());return l&&(i.args=this.source.generateArray(n)),this.trackIds&&(i.ids=this.source.generateArray(a)),this.stringParams&&(i.types=this.source.generateArray(s),i.contexts=this.source.generateArray(o)),this.options.data&&(i.data="data"),this.useBlockParams&&(i.blockParams="blockParams"),i},setupHelperArgs:function(t,e,n,r){t=this.setupParams(t,e,n);return t.loc=JSON.stringify(this.source.currentLocation),t=this.objectLiteral(t),r?(this.useRegister("options"),n.push("options"),["options=",t]):n?(n.push(t),""):t}};for(var c="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),h=u.RESERVED_WORDS={},p=0,d=c.length;p<d;p++)h[c[p]]=!0;u.isValidJavaScriptVariableName=function(t){return!u.RESERVED_WORDS[t]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(t)},e.default=u,t.exports=e.default},function(t,e,n){"use strict";var o=n(13).default,s=(e.__esModule=!0,n(5)),r=void 0;function a(t,e,n){if(s.isArray(t)){for(var r=[],i=0,o=t.length;i<o;i++)r.push(e.wrap(t[i],n));return r}return"boolean"==typeof t||"number"==typeof t?t+"":t}function i(t){this.srcFile=t,this.source=[]}r||((r=function(t,e,n,r){this.src="",r&&this.add(r)}).prototype={add:function(t){s.isArray(t)&&(t=t.join("")),this.src+=t},prepend:function(t){s.isArray(t)&&(t=t.join("")),this.src=t+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),i.prototype={isEmpty:function(){return!this.source.length},prepend:function(t,e){this.source.unshift(this.wrap(t,e))},push:function(t,e){this.source.push(this.wrap(t,e))},merge:function(){var e=this.empty();return this.each(function(t){e.add(["  ",t,"\n"])}),e},each:function(t){for(var e=0,n=this.source.length;e<n;e++)t(this.source[e])},empty:function(){var t=this.currentLocation||{start:{}};return new r(t.start.line,t.start.column,this.srcFile)},wrap:function(t){var e=arguments.length<=1||void 0===arguments[1]?this.currentLocation||{start:{}}:arguments[1];return t instanceof r?t:(t=a(t,this,e),new r(e.start.line,e.start.column,this.srcFile,t))},functionCall:function(t,e,n){return n=this.generateList(n),this.wrap([t,e?"."+e+"(":"(",n,")"])},quotedString:function(t){return'"'+(t+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(n){var r=this,i=[],t=(o(n).forEach(function(t){var e=a(n[t],r);"undefined"!==e&&i.push([r.quotedString(t),":",e])}),this.generateList(i));return t.prepend("{"),t.add("}"),t},generateList:function(t){for(var e=this.empty(),n=0,r=t.length;n<r;n++)n&&e.add(","),e.add(a(t[n],this));return e},generateArray:function(t){t=this.generateList(t);return t.prepend("["),t.add("]"),t}},e.default=i,t.exports=e.default}],i={},n.m=r,n.c=i,n.p="",n(0);function n(t){if(i[t])return i[t].exports;var e=i[t]={exports:{},id:t,loaded:!1};return r[t].call(e.exports,e,e.exports,n),e.loaded=!0,e.exports}var r,i},"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Handlebars=e():t.Handlebars=e();