@charset "UTF-8";

@keyframes opaque {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes resizeanim {
    from {
        opacity: 0
    }

    to {
        opacity: 0
    }
}

.apexcharts-canvas {
    position: relative;
    user-select: none;
    overflow: visible
}

.apexcharts-canvas ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: .45rem
}

.apexcharts-canvas ::-webkit-scrollbar-thumb {
    background: #bdbdbd;
    opacity: .65;
    border-radius: .45rem
}

.apexcharts-inner {
    position: relative
}

.apexcharts-text tspan {
    font-family: inherit
}

.legend-mouseover-inactive {
    opacity: .2
}

.apexcharts-series-collapsed {
    opacity: 0
}

.apexcharts-svg {
    direction: ltr
}

.apexcharts-svg.apexcharts-zoomable.hovering-zoom {
    cursor: crosshair
}

.apexcharts-svg.apexcharts-zoomable.hovering-pan {
    cursor: move
}

.svg_select_points {
    fill: #bdbdbd;
    stroke: #bdbdbd
}

.svg_select_boundingRect,
.svg_select_points_rot {
    pointer-events: none;
    opacity: 0;
    visibility: hidden
}

.svg_select_points_l,
.svg_select_points_r {
    opacity: 1;
    fill: #bdbdbd;
    visibility: visible;
    cursor: ew-resize
}

.apexcharts-selection-rect {
    cursor: move
}

.apexcharts-selection-rect+g .svg_select_boundingRect,
.apexcharts-selection-rect+g .svg_select_points_rot {
    opacity: 0;
    visibility: hidden
}

.apexcharts-selection-rect+g .svg_select_points_l,
.apexcharts-selection-rect+g .svg_select_points_r {
    cursor: ew-resize;
    opacity: 1;
    visibility: visible
}

.apexcharts-menu-icon,
.apexcharts-pan-icon,
.apexcharts-reset-icon,
.apexcharts-selection-icon,
.apexcharts-toolbar-custom-icon,
.apexcharts-zoom-icon,
.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    padding: .125rem;
    cursor: pointer;
    color: #bdbdbd
}

.apexcharts-menu-icon svg,
.apexcharts-pan-icon svg,
.apexcharts-reset-icon svg,
.apexcharts-selection-icon svg,
.apexcharts-toolbar-custom-icon svg,
.apexcharts-zoom-icon svg,
.apexcharts-zoomin-icon svg,
.apexcharts-zoomout-icon svg {
    fill: currentColor
}

.apexcharts-menu-icon:hover,
.apexcharts-reset-icon:hover,
.apexcharts-selection-icon:not(.apexcharts-selected):hover,
.apexcharts-zoom-icon:not(.apexcharts-selected):hover,
.apexcharts-zoomin-icon:hover,
.apexcharts-zoomout-icon:hover {
    color: var(--bs-text-level-1)
}

.apexcharts-menu-icon,
.apexcharts-selection-icon {
    position: relative
}

.apexcharts-selection-icon {
    transform: scale(.76)
}

.apexcharts-reset-icon {
    margin-left: 5px
}

.apexcharts-menu-icon,
.apexcharts-reset-icon,
.apexcharts-zoom-icon {
    transform: scale(.85)
}

.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
    transform: scale(.7)
}

.apexcharts-zoomout-icon {
    margin-right: 3px
}

.apexcharts-pan-icon {
    transform: scale(.62);
    position: relative;
    left: 1px;
    top: 0;
    color: #bdbdbd
}

.apexcharts-pan-icon:not(.apexcharts-selected):hover {
    color: var(--bs-text-level-1)
}

.apexcharts-pan-icon.apexcharts-selected {
    color: #2196f3
}

.apexcharts-canvas .apexcharts-reset-zoom-icon .apexcharts-selected,
.apexcharts-canvas .apexcharts-selection-icon .apexcharts-selected,
.apexcharts-canvas .apexcharts-zoom-icon .apexcharts-selected {
    color: #2196f3
}

.apexcharts-toolbar {
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: right;
    padding: .25rem .5rem;
    z-index: 11;
    max-width: 25rem;
    border-radius: .25rem
}

.apexcharts-menu {
    position: absolute;
    min-width: 10rem;
    padding: .25rem 0;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    opacity: 0;
    pointer-events: none;
    top: 100%;
    right: 0;
    border-radius: .25rem;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-menu {
        transition: none
    }
}

.apexcharts-menu.apexcharts-menu-open {
    opacity: 1;
    pointer-events: all;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-menu.apexcharts-menu-open {
        transition: none
    }
}

.apexcharts-menu-item {
    display: flex;
    align-items: center;
    color: var(--bs-text-level-2);
    font-size: 1rem;
    font-weight: 400;
    padding: .5rem 1rem;
    white-space: nowrap;
    cursor: pointer
}

.apexcharts-menu-item:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

@media (min-width:768px) {
    .apexcharts-canvas:hover .apexcharts-toolbar {
        opacity: 1
    }
}

.apexcharts-datalabel.apexcharts-element-hidden {
    opacity: 0
}

.apexcharts-datalabel,
.apexcharts-datalabel-label,
.apexcharts-datalabel-value,
.apexcharts-datalabels,
.apexcharts-pie-label {
    cursor: default;
    pointer-events: none
}

.apexcharts-pie-label-delay {
    opacity: 0;
    animation-name: opaque;
    animation-duration: .3s;
    animation-fill-mode: forwards;
    animation-timing-function: ease
}

.apexcharts-canvas .apexcharts-element-hidden {
    opacity: 0
}

.apexcharts-hide .apexcharts-series-points {
    opacity: 0
}

.apexcharts-annotation-rect,
.apexcharts-area-series .apexcharts-area,
.apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-gridline,
.apexcharts-line,
.apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-radar-series path,
.apexcharts-radar-series polygon,
.apexcharts-toolbar svg,
.apexcharts-tooltip .apexcharts-marker,
.apexcharts-zoom-rect {
    pointer-events: none
}

.apexcharts-marker {
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-marker {
        transition: none
    }
}

.resize-triggers {
    animation: 1ms resizeanim;
    visibility: hidden;
    opacity: 0
}

.resize-triggers>div {
    background: #bdbdbd;
    overflow: auto
}

.contract-trigger:before,
.resize-triggers,
.resize-triggers>div {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    overflow: hidden
}

.contract-trigger:before {
    width: 200%;
    height: 200%
}

.apexcharts-xcrosshairs,
.apexcharts-ycrosshairs {
    opacity: 0;
    pointer-events: none;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {

    .apexcharts-xcrosshairs,
    .apexcharts-ycrosshairs {
        transition: none
    }
}

.apexcharts-xcrosshairs.apexcharts-active,
.apexcharts-ycrosshairs.apexcharts-active {
    opacity: 1;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {

    .apexcharts-xcrosshairs.apexcharts-active,
    .apexcharts-ycrosshairs.apexcharts-active {
        transition: none
    }
}

.apexcharts-ycrosshairs-hidden {
    opacity: 0
}

.apexcharts-tooltip {
    display: flex;
    flex-direction: column;
    position: absolute;
    white-space: nowrap;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
    z-index: 12;
    cursor: default;
    font-size: 1rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    left: 5rem;
    top: 5rem;
    transition: all .2s ease-in-out;
    border-radius: .25rem
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-tooltip {
        transition: none
    }
}

.apexcharts-tooltip.apexcharts-active {
    opacity: 1;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-tooltip.apexcharts-active {
        transition: none
    }
}

.apexcharts-tooltip * {
    font-family: inherit
}

.apexcharts-tooltip-title {
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-2);
    font-size: 1rem;
    font-weight: 500;
    padding: .75rem 1rem;
    margin-bottom: .5rem
}

.apexcharts-tooltip-goals-group,
.apexcharts-tooltip-text-goals-label,
.apexcharts-tooltip-text-goals-value {
    display: flex
}

.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-z-value {
    display: inline-block;
    font-weight: 500;
    margin-left: .5rem
}

.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-z-value {
    display: inline-block;
    font-size: 1rem;
    font-weight: 500;
    margin-left: .5rem
}

.apexcharts-tooltip-text-goals-label:empty,
.apexcharts-tooltip-text-goals-value:empty,
.apexcharts-tooltip-text-y-label:empty,
.apexcharts-tooltip-text-y-value:empty,
.apexcharts-tooltip-text-z-value:empty {
    display: none
}

.apexcharts-tooltip-text-goals-label,
.apexcharts-tooltip-text-goals-value {
    padding: .5rem 0 .5rem
}

.apexcharts-tooltip-text-goals-label:not(:empty),
.apexcharts-tooltip-text-goals-value:not(:empty) {
    margin-top: -.5rem
}

.apexcharts-tooltip-marker {
    position: relative;
    top: 0;
    width: .75rem;
    height: .75rem;
    margin-right: .5rem;
    border-radius: 50%
}

.apexcharts-tooltip-series-group {
    display: none;
    justify-content: left;
    text-align: left;
    align-items: center;
    padding: 0 .75rem
}

.apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-marker {
    opacity: 1
}

.apexcharts-tooltip-series-group.apexcharts-active,
.apexcharts-tooltip-series-group:last-child {
    padding-bottom: .25rem
}

.apexcharts-tooltip-series-group-hidden {
    opacity: 0;
    height: 0;
    line-height: 0;
    padding: 0 !important
}

.apexcharts-tooltip-y-group {
    padding: .5rem 0
}

.apexcharts-custom-tooltip,
.apexcharts-tooltip-box {
    padding: .25rem .5rem
}

.apexcharts-tooltip-boxPlot {
    display: flex;
    flex-direction: column-reverse
}

.apexcharts-tooltip-box>div {
    margin: .5rem 0
}

.apexcharts-tooltip-box span.value {
    font-weight: 500
}

.apexcharts-tooltip-rangebar {
    padding: .5rem .75rem
}

.apexcharts-tooltip-rangebar .category {
    color: var(--bs-text-level-3);
    font-weight: 500
}

.apexcharts-tooltip-rangebar .series-name {
    display: block;
    font-weight: 500;
    margin-right: .5rem
}

.apexcharts-xaxistooltip {
    position: absolute;
    font-size: 1rem;
    padding: .5rem .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    text-align: center;
    opacity: 0;
    z-index: 10;
    pointer-events: none;
    border-radius: .25rem;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-xaxistooltip {
        transition: none
    }
}

.apexcharts-xaxistooltip.apexcharts-active {
    opacity: 1;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-xaxistooltip.apexcharts-active {
        transition: none
    }
}

.apexcharts-xaxistooltip:after,
.apexcharts-xaxistooltip:before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    left: 50%;
    border-color: transparent;
    border-style: solid;
    pointer-events: none
}

.apexcharts-xaxistooltip:after {
    border-width: .4rem;
    margin-left: -.4rem
}

.apexcharts-xaxistooltip:before {
    border-width: calc(.4rem + 1px);
    margin-left: calc((.4rem + 1px) * -1)
}

.apexcharts-xaxistooltip-bottom:after,
.apexcharts-xaxistooltip-bottom:before {
    bottom: 100%
}

.apexcharts-xaxistooltip-bottom:after {
    border-bottom-color: var(--bs-bg-level-1)
}

.apexcharts-xaxistooltip-bottom:before {
    border-bottom-color: var(--bs-border-color)
}

.apexcharts-xaxistooltip-top:after,
.apexcharts-xaxistooltip-top:before {
    top: 100%
}

.apexcharts-xaxistooltip-top:after {
    border-top-color: var(--bs-bg-level-1)
}

.apexcharts-xaxistooltip-top:before {
    border-top-color: var(--bs-border-color)
}

.apexcharts-yaxistooltip {
    position: absolute;
    font-size: 1rem;
    padding: .5rem .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    text-align: center;
    opacity: 0;
    z-index: 10;
    pointer-events: none;
    border-radius: .25rem;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .apexcharts-yaxistooltip {
        transition: none
    }
}

.apexcharts-yaxistooltip.apexcharts-active {
    opacity: 1
}

.apexcharts-yaxistooltip:after,
.apexcharts-yaxistooltip:before {
    content: "";
    position: absolute;
    top: 50%;
    height: 0;
    width: 0;
    border-color: transparent;
    border-style: solid;
    pointer-events: none
}

.apexcharts-yaxistooltip:after {
    border-width: .4rem;
    margin-top: -.4rem
}

.apexcharts-yaxistooltip:before {
    border-width: calc(.4rem + 1px);
    margin-top: calc((.4rem + 1px) * -1)
}

.apexcharts-yaxistooltip-left:after,
.apexcharts-yaxistooltip-left:before {
    left: 100%
}

.apexcharts-yaxistooltip-left:after {
    border-left-color: var(--bs-bg-level-1)
}

.apexcharts-yaxistooltip-left:before {
    border-left-color: var(--bs-border-color)
}

.apexcharts-yaxistooltip-right:after,
.apexcharts-yaxistooltip-right:before {
    right: 100%
}

.apexcharts-yaxistooltip-right:after {
    border-right-color: var(--bs-bg-level-1)
}

.apexcharts-yaxistooltip-right:before {
    border-right-color: var(--bs-border-color)
}

.apexcharts-yaxistooltip-hidden {
    display: none
}

.blockui.blockui-overlay {
    background: #212121;
    opacity: .65
}

.blockui.blockui-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    font-size: 1.25rem;
    font-weight: 500;
    padding: .5rem 1rem;
    border-radius: .25rem
}

.blockui.blockui-message .blockui-title {
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

.blockui.blockui-message [class*=spinner] {
    margin-right: .75rem
}

.blockui.blockui-message.blockui-block-page {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

.datepicker {
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-1);
    min-width: 17.5rem;
    font-size: 1rem;
    padding: .5rem;
    margin: 0;
    z-index: 1110 !important;
    border-radius: .3rem
}

.datepicker table {
    width: 100%;
    margin: 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.datepicker td,
.datepicker th {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    border: none;
    border-radius: .25rem
}

.datepicker table tr td.day {
    cursor: pointer
}

.datepicker table tr td.day.focused,
.datepicker table tr td.day:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datepicker table tr td.new,
.datepicker table tr td.old {
    background: 0 0
}

.datepicker table tr td.new,
.datepicker table tr td.new:hover,
.datepicker table tr td.old,
.datepicker table tr td.old:hover {
    color: #bdbdbd
}

.datepicker table tr td.today {
    position: relative
}

.datepicker table tr td.today,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today:hover {
    color: #2196f3;
    background: rgba(33, 150, 243, .1)
}

.datepicker table tr td.today:after {
    content: "";
    position: absolute;
    display: inline-block;
    border-bottom: 8px solid currentColor;
    border-left: 8px solid transparent;
    bottom: 2px;
    right: 2px
}

.datepicker table tr td.highlighted {
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-2);
    border-radius: 0
}

.datepicker table tr td.range,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range:hover {
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-3);
    border-radius: 0
}

.datepicker table tr td.range-start {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.datepicker table tr td.range-end {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.datepicker table tr td.disabled {
    cursor: default
}

.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
    color: var(--bs-bg-level-4);
    background: 0 0
}

.datepicker table tr td.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover,
.datepicker table tr td.selected,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected:hover {
    color: #fff;
    background: #2196f3
}

.datepicker table tr td span {
    display: block;
    width: calc(100% / 4 - 2%);
    height: 4rem;
    line-height: 4rem;
    margin: 1%;
    float: left;
    cursor: pointer;
    border-radius: .25rem
}

.datepicker table tr td span.focused,
.datepicker table tr td span:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datepicker table tr td span.disabled {
    color: var(--bs-bg-level-4);
    background: var(--bs-bg-level-2);
    cursor: default
}

.datepicker table tr td span.active {
    color: #fff;
    background: #2196f3
}

.datepicker table tr td span.new,
.datepicker table tr td span.old {
    color: #bdbdbd;
    background: 0 0
}

.datepicker .datepicker-switch,
.datepicker .next,
.datepicker .prev,
.datepicker tfoot tr th {
    cursor: pointer
}

.datepicker .datepicker-switch:hover,
.datepicker .next:hover,
.datepicker .prev:hover,
.datepicker tfoot tr th:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datepicker .datepicker-switch {
    width: 12.5rem
}

.datepicker .next,
.datepicker .prev {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    width: 2.5rem
}

.datepicker .next.disabled,
.datepicker .prev.disabled {
    visibility: hidden
}

.datepicker .prev {
    position: relative
}

.datepicker .prev:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-225deg);
    bottom: 1px;
    left: 2px
}

@media (prefers-reduced-motion:reduce) {
    .datepicker .prev:after {
        transition: none
    }
}

.datepicker .next {
    position: relative
}

.datepicker .next:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-45deg);
    bottom: 1px;
    right: 2px
}

@media (prefers-reduced-motion:reduce) {
    .datepicker .next:after {
        transition: none
    }
}

.datepicker .cw {
    color: #bdbdbd;
    background: 0 0;
    font-size: .875rem;
    vertical-align: middle
}

.datepicker-dropdown {
    margin: 0
}

.datepicker-inline {
    width: fit-content;
    border: 1px solid var(--bs-border-color)
}

.input-daterange input {
    text-align: center
}

.daterangepicker {
    position: absolute;
    display: none;
    width: auto;
    padding: 0;
    margin: 0;
    font-size: 1rem;
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.daterangepicker .drp-calendar {
    display: none;
    padding: .5rem
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0
}

.daterangepicker .calendar-table td,
.daterangepicker .calendar-table th {
    width: 2.5rem;
    max-width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    padding: 0;
    margin: 0;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
    border-radius: .25rem
}

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.daterangepicker td.week,
.daterangepicker th.week {
    color: #bdbdbd;
    background: 0 0;
    font-size: .875rem
}

.daterangepicker td.in-range {
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-3);
    border-radius: 0
}

.daterangepicker td.start-date {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.daterangepicker td.end-date {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem
}

.daterangepicker td.start-date.end-date {
    border-radius: .25rem
}

.daterangepicker td.disabled,
.daterangepicker td.disabled:hover {
    cursor: default;
    color: var(--bs-bg-level-4);
    background: 0 0
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
    color: #fff;
    background: #2196f3
}

.daterangepicker td.off,
.daterangepicker td.off.active,
.daterangepicker td.off.end-date,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off:hover {
    color: #bdbdbd;
    background: 0 0
}

.daterangepicker select.monthselect {
    margin-right: .5rem
}

.daterangepicker select.ampmselect,
.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.monthselect,
.daterangepicker select.secondselect,
.daterangepicker select.yearselect {
    width: auto
}

.daterangepicker .calendar-time {
    text-align: center;
    margin-top: .5rem
}

.daterangepicker .drp-buttons {
    display: none;
    text-align: right;
    padding: .5rem;
    clear: both;
    border-top: 1px solid var(--bs-border-color)
}

.daterangepicker .drp-buttons .btn {
    margin-left: .5rem
}

.daterangepicker .next,
.daterangepicker .prev {
    display: flex;
    align-items: center;
    justify-content: center
}

.daterangepicker .next>span,
.daterangepicker .prev>span {
    display: none
}

.daterangepicker .prev {
    position: relative
}

.daterangepicker .prev:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-225deg);
    bottom: 1px;
    left: 1px
}

@media (prefers-reduced-motion:reduce) {
    .daterangepicker .prev:after {
        transition: none
    }
}

.daterangepicker .next {
    position: relative
}

.daterangepicker .next:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-45deg);
    bottom: 1px;
    right: 2px
}

@media (prefers-reduced-motion:reduce) {
    .daterangepicker .next:after {
        transition: none
    }
}

.daterangepicker .drp-selected {
    display: none;
    padding-right: .5rem
}

.daterangepicker .ranges {
    margin: 0;
    text-align: left
}

.daterangepicker .ranges ul {
    width: 100%;
    padding: .25rem 0;
    margin: 0;
    list-style: none
}

.daterangepicker .ranges li {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: var(--bs-text-level-2);
    padding: .5rem 1rem;
    cursor: pointer
}

.daterangepicker .ranges li:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.daterangepicker .ranges li.active,
.daterangepicker .ranges li:active {
    color: #fff;
    background: #2196f3
}

.daterangepicker.single .drp-selected {
    display: none
}

.daterangepicker.show-calendar .drp-buttons,
.daterangepicker.show-calendar .drp-calendar {
    display: block
}

.daterangepicker.auto-apply .drp-buttons {
    display: none
}

@media (min-width:1025px) {
    .daterangepicker .drp-selected {
        display: inline-block
    }

    .daterangepicker.show-ranges .drp-calendar.left {
        border-left: 1px solid var(--bs-border-color)
    }

    .daterangepicker .ranges ul {
        width: 12rem
    }

    .daterangepicker .drp-calendar,
    .daterangepicker .ranges {
        float: left
    }
}

.datetimepicker {
    min-width: 17.5rem;
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-1);
    font-size: 1rem;
    padding: .5rem;
    margin: 0;
    border-radius: .3rem
}

.datetimepicker>div {
    display: none
}

.datetimepicker .datetimepicker-days,
.datetimepicker .datetimepicker-hours,
.datetimepicker .datetimepicker-minutes,
.datetimepicker .datetimepicker-months,
.datetimepicker .datetimepicker-years {
    display: block
}

.datetimepicker table {
    width: 100%;
    margin: 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.datetimepicker table tr td {
    width: 2.5rem;
    height: 2.5rem
}

.datetimepicker table tr td.day,
.datetimepicker table tr td.hour,
.datetimepicker table tr td.minute {
    cursor: pointer
}

.datetimepicker table tr td.day:hover,
.datetimepicker table tr td.hour:hover,
.datetimepicker table tr td.minute:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datetimepicker table tr td.new,
.datetimepicker table tr td.old {
    background: 0 0
}

.datetimepicker table tr td.new,
.datetimepicker table tr td.new:hover,
.datetimepicker table tr td.old,
.datetimepicker table tr td.old:hover {
    color: #bdbdbd
}

.datetimepicker table tr td.today {
    position: relative
}

.datetimepicker table tr td.today,
.datetimepicker table tr td.today.disabled,
.datetimepicker table tr td.today:hover {
    color: #2196f3;
    background: rgba(33, 150, 243, .1)
}

.datetimepicker table tr td.today:after {
    content: "";
    position: absolute;
    display: inline-block;
    border-bottom: 8px solid currentColor;
    border-left: 8px solid transparent;
    bottom: 2px;
    right: 2px
}

.datetimepicker table tr td.disabled {
    cursor: default
}

.datetimepicker table tr td.disabled,
.datetimepicker table tr td.disabled:hover {
    color: var(--bs-bg-level-4);
    background: 0 0
}

.datetimepicker table tr td.active,
.datetimepicker table tr td.active.disabled,
.datetimepicker table tr td.active:hover {
    color: #fff;
    background: #2196f3
}

.datetimepicker table tr td span {
    display: block;
    width: 23%;
    height: 4rem;
    line-height: 4rem;
    margin: 1%;
    float: left;
    cursor: pointer;
    border-radius: .25rem
}

.datetimepicker table tr td span:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datetimepicker table tr td span.disabled {
    color: var(--bs-bg-level-4);
    background: var(--bs-bg-level-2);
    cursor: default
}

.datetimepicker table tr td span.active {
    color: #fff;
    background: #2196f3
}

.datetimepicker table tr td span.new,
.datetimepicker table tr td span.old {
    color: #bdbdbd;
    background: 0 0
}

.datetimepicker td,
.datetimepicker th {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    border: none;
    border-radius: .25rem
}

.datetimepicker .datetimepicker-hours span,
.datetimepicker .datetimepicker-minutes span {
    height: 2.5rem;
    line-height: 2.5rem
}

.datetimepicker .datetimepicker-hours fieldset legend,
.datetimepicker .datetimepicker-minutes fieldset legend {
    color: var(--bs-text-level-1);
    font-size: calc(1.275rem + .3vw);
    font-weight: 600;
    line-height: 2.5rem;
    margin-bottom: 0
}

@media (min-width:1200px) {

    .datetimepicker .datetimepicker-hours fieldset legend,
    .datetimepicker .datetimepicker-minutes fieldset legend {
        font-size: 1.5rem
    }
}

.datetimepicker .datetimepicker-hours table tr td span.hour_am,
.datetimepicker .datetimepicker-hours table tr td span.hour_pm {
    width: 14.6666666667%;
    margin: 1%
}

.datetimepicker tfoot tr:first-child th,
.datetimepicker thead tr:first-child th {
    height: 2.5rem;
    width: 2.5rem;
    cursor: pointer
}

.datetimepicker tfoot tr:first-child th.switch,
.datetimepicker thead tr:first-child th.switch {
    width: 12.5rem
}

.datetimepicker tfoot tr:first-child th:hover,
.datetimepicker thead tr:first-child th:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.datetimepicker .next,
.datetimepicker .prev {
    display: flex;
    align-items: center;
    justify-content: center
}

.datetimepicker .next>span,
.datetimepicker .prev>span {
    display: none
}

.datetimepicker .prev {
    position: relative
}

.datetimepicker .prev:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-225deg);
    bottom: 1px;
    left: 2px
}

@media (prefers-reduced-motion:reduce) {
    .datetimepicker .prev:after {
        transition: none
    }
}

.datetimepicker .next {
    position: relative
}

.datetimepicker .next:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-45deg);
    bottom: 1px;
    right: 2px
}

@media (prefers-reduced-motion:reduce) {
    .datetimepicker .next:after {
        transition: none
    }
}

.datetimepicker-inline {
    width: fit-content;
    border: 1px solid var(--bs-border-color)
}

.datetimepicker-dropdown,
.datetimepicker-dropdown-left {
    top: 0;
    left: 0
}

.bootstrap-touchspin-down,
.bootstrap-touchspin-up {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: calc(1.5em + (.75rem + 2px));
    font-size: 1rem;
    font-weight: 600
}

.bootstrap-touchspin-down>i,
.bootstrap-touchspin-up>i {
    font-size: 1rem
}

.bootstrap-touchspin-down>svg,
.bootstrap-touchspin-up>svg {
    font-size: 1rem;
    height: 1em
}

.bootstrap-touchspin-down>i,
.bootstrap-touchspin-down>svg,
.bootstrap-touchspin-up>i,
.bootstrap-touchspin-up>svg {
    line-height: 0
}

.bootstrap-touchspin .input-group-btn-vertical {
    display: flex;
    flex-direction: column
}

.bootstrap-touchspin .input-group-btn-vertical>.btn {
    flex: 1;
    line-height: 0
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: .25rem
}

.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: .25rem
}

.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-down,
.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-up {
    width: calc(1.5em + (.5rem + 2px))
}

.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-down>i,
.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-up>i {
    font-size: .875rem
}

.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-down>svg,
.bootstrap-touchspin.input-group-sm .bootstrap-touchspin-up>svg {
    font-size: .875rem;
    height: 1em
}

.bootstrap-touchspin.input-group-sm .input-group-btn-vertical .bootstrap-touchspin-up {
    border-top-right-radius: .2rem
}

.bootstrap-touchspin.input-group-sm .input-group-btn-vertical .bootstrap-touchspin-down {
    border-bottom-right-radius: .2rem
}

.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-down,
.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-up {
    width: calc(1.5em + (1rem + 2px))
}

.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-down>i,
.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-up>i {
    font-size: 1.25rem
}

.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-down>svg,
.bootstrap-touchspin.input-group-lg .bootstrap-touchspin-up>svg {
    font-size: 1.25rem;
    height: 1em
}

.bootstrap-touchspin.input-group-lg .input-group-btn-vertical .bootstrap-touchspin-up {
    border-top-right-radius: .3rem
}

.bootstrap-touchspin.input-group-lg .input-group-btn-vertical .bootstrap-touchspin-down {
    border-bottom-right-radius: .3rem
}

.dt-control {
    background: url(../../../../www.datatables.net/examples/resources/details_open.png) no-repeat center center
}

.dt-hasChild .dt-control {
    background: url(https://www.datatables.net/examples/resources/details_close.png) no-repeat center center
}

.dataTable {
    width: 100% !important;
    max-width: none !important;
    margin-top: .5rem !important;
    margin-bottom: .5rem !important;
    border-collapse: separate !important;
    border-spacing: 0;
    clear: both
}

.dataTable .dt-left {
    text-align: left
}

.dataTable .dt-right {
    text-align: right
}

.dataTable .dataTables_empty,
.dataTable .dt-center {
    text-align: center
}

.dataTable .dt-justify {
    text-align: justify
}

.dataTable .dt-nowrap {
    white-space: nowrap
}

.dataTable tfoot .dt-head-left,
.dataTable thead .dt-head-left {
    text-align: left
}

.dataTable tfoot .dt-head-right,
.dataTable thead .dt-head-right {
    text-align: right
}

.dataTable tfoot .dt-head-center,
.dataTable thead .dt-head-center {
    text-align: center
}

.dataTable tfoot .dt-head-justify,
.dataTable thead .dt-head-justify {
    text-align: justify
}

.dataTable tfoot .dt-head-nowrap,
.dataTable thead .dt-head-nowrap {
    white-space: nowrap
}

.dataTable tbody .dt-body-left {
    text-align: left
}

.dataTable tbody .dt-body-right {
    text-align: right
}

.dataTable tbody .dt-body-center {
    text-align: center
}

.dataTable tbody .dt-body-justify {
    text-align: justify
}

.dataTable tbody .dt-body-nowrap {
    white-space: nowrap
}

.dataTable td,
.dataTable th {
    box-sizing: content-box
}

.nowrap .dataTable td,
.nowrap .dataTable th {
    white-space: nowrap
}

.dataTable td.dataTables_empty,
.dataTable th.dataTables_empty {
    text-align: center
}

.dataTable>thead>tr>td:active,
.dataTable>thead>tr>th:active {
    outline: 0
}

.dataTable>thead>tr>td:not(.sorting_disabled),
.dataTable>thead>tr>th:not(.sorting_disabled) {
    padding-right: 2rem
}

.dataTable>thead .sorting,
.dataTable>thead .sorting_asc,
.dataTable>thead .sorting_asc_disabled,
.dataTable>thead .sorting_desc,
.dataTable>thead .sorting_desc_disabled {
    cursor: pointer;
    position: relative;
    vertical-align: middle
}

.dataTable>thead .sorting_asc,
.dataTable>thead .sorting_asc_disabled,
.dataTable>thead .sorting_desc,
.dataTable>thead .sorting_desc_disabled {
    position: relative;
    cursor: pointer
}

.dataTable>thead .sorting_desc,
.dataTable>thead .sorting_desc_disabled {
    position: relative
}

.dataTable>thead .sorting_desc:after,
.dataTable>thead .sorting_desc_disabled:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(45deg);
    bottom: 2px;
    position: absolute;
    bottom: 45%;
    color: var(--bs-text-level-1);
    right: .75rem;
    transition: none !important
}

@media (prefers-reduced-motion:reduce) {

    .dataTable>thead .sorting_desc:after,
    .dataTable>thead .sorting_desc_disabled:after {
        transition: none
    }
}

.dataTable>thead .sorting_asc,
.dataTable>thead .sorting_asc_disabled {
    position: relative
}

.dataTable>thead .sorting_asc:after,
.dataTable>thead .sorting_asc_disabled:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-135deg);
    bottom: 2px;
    position: absolute;
    top: 45%;
    color: var(--bs-text-level-1);
    right: .75rem;
    transition: none !important
}

@media (prefers-reduced-motion:reduce) {

    .dataTable>thead .sorting_asc:after,
    .dataTable>thead .sorting_asc_disabled:after {
        transition: none
    }
}

.dataTable>thead .sorting_asc:after,
.dataTable>thead .sorting_desc:after {
    opacity: 1
}

.dataTable>thead .sorting_asc_disabled:after,
.dataTable>thead .sorting_desc_disabled:after {
    opacity: 0
}

.dataTables_wrapper .dataTables_length {
    margin-bottom: .5rem
}

.dataTables_wrapper .dataTables_length label {
    text-align: left;
    white-space: nowrap
}

.dataTables_wrapper .dataTables_length select {
    width: auto;
    display: inline-block;
    margin: 0 .5rem
}

.dataTables_wrapper .dataTables_filter {
    text-align: right;
    margin-bottom: .5rem
}

.dataTables_wrapper .dataTables_filter label {
    text-align: left;
    white-space: nowrap
}

.dataTables_wrapper .dataTables_filter input {
    width: auto;
    display: inline-block;
    margin-left: .5rem
}

.dataTables_wrapper .dataTables_info {
    padding-top: .75rem;
    white-space: nowrap
}

.dataTables_wrapper .dataTables_paginate {
    margin: 0;
    text-align: right;
    white-space: nowrap
}

.dataTables_wrapper .dataTables_paginate .pagination {
    white-space: nowrap;
    justify-content: flex-end;
    margin: .5rem 0
}

.dataTables_wrapper .dataTables_processing {
    position: absolute;
    width: 17.5rem;
    padding: 1rem 1rem;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, 50%);
    z-index: 1;
    display: none !important;
}

.dataTables_scrollHead .dataTable {
    margin-bottom: 0 !important
}

.dataTables_scrollHead .table-bordered {
    border-bottom-width: 0
}

.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

.dataTables_scrollBody table thead .sorting:after,
.dataTables_scrollBody table thead .sorting_asc:after,
.dataTables_scrollBody table thead .sorting_desc:after {
    display: none
}

.dataTables_scrollBody table tbody tr:first-child td,
.dataTables_scrollBody table tbody tr:first-child th {
    border-top: none
}

.dataTables_scrollFoot>.dataTables_scrollFootInner {
    box-sizing: content-box
}

.dataTables_scrollFoot>.dataTables_scrollFootInner>table {
    margin-top: 0 !important;
    border-top: none
}

.dataTable.table-sm>thead>tr>th:not(.sorting_disabled) {
    padding-right: 1rem
}

.dataTable.table-sm .sorting:after,
.dataTable.table-sm .sorting_asc:after,
.dataTable.table-sm .sorting_desc:after {
    right: .25rem
}

.dataTable.table-bordered {
    border-right-width: 0
}

.dataTable.table-bordered td,
.dataTable.table-bordered th {
    border-left-width: 0;
    border-bottom-width: 1px
}

.dataTable.table-bordered td:first-child,
.dataTable.table-bordered th:first-child {
    border-left-width: 1px
}

.dataTable.table-bordered td:last-child,
.dataTable.table-bordered th:last-child {
    border-right-width: 1px
}

.dataTable.table-bordered thead tr:first-child td,
.dataTable.table-bordered thead tr:first-child th {
    border-top-width: 1px
}

.table-responsive>.dataTables_wrapper>.row {
    margin: 0
}

.table-responsive>.dataTables_wrapper>.row>[class^=col-]:first-child {
    padding-left: 0
}

.table-responsive>.dataTables_wrapper>.row>[class^=col-]:last-child {
    padding-right: 0
}

@media (max-width:575.98px) {

    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_paginate {
        text-align: center
    }

    .dataTables_wrapper .dataTables_paginate .pagination {
        justify-content: center;
        margin-top: .5rem
    }
}

.dt-autofill-handle {
    position: absolute;
    height: .75rem;
    width: .75rem;
    background: #2196f3;
    cursor: pointer;
    z-index: 102
}

.dtk-focus-alt .dt-autofill-handle {
    background: #f44336
}

.dt-autofill-select {
    background: #2196f3;
    position: absolute;
    z-index: 1001
}

.dt-autofill-select.bottom,
.dt-autofill-select.top {
    height: 2px;
    transform: translateY(-50%)
}

.dt-autofill-select.left,
.dt-autofill-select.right {
    width: 2px;
    transform: translateX(-50%)
}

.dt-autofill-list {
    position: fixed;
    width: 45rem;
    padding: 1rem;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1101;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.dt-autofill-list ul {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
    list-style: none
}

.dt-autofill-list ul li {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: .5rem 1rem;
    border-bottom: 1px solid var(--bs-border-color)
}

.dt-autofill-list ul li .dt-autofill-input {
    display: inline-block;
    width: 5rem;
    margin: 0 .5rem
}

.dt-autofill-list ul li .dt-autofill-question {
    color: var(--bs-text-level-1);
    font-size: 1rem;
    font-weight: 500
}

.dt-autofill-list ul li .dt-autofill-question input[type=number] {
    display: inline-block;
    width: 5rem;
    margin: 0 .5rem;
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--bs-text-level-3);
    background-color: var(--bs-bg-level-1);
    background-clip: padding-box;
    border: 1px solid var(--bs-bg-level-3);
    appearance: none;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .dt-autofill-list ul li .dt-autofill-question input[type=number] {
        transition: none
    }
}

.dt-autofill-list ul li .dt-autofill-question input[type=number]:focus {
    border-color: #2196f3;
    outline: 0
}

.dt-autofill-list ul li .dt-autofill-question input[type=number]::placeholder {
    color: var(--bs-text-level-1);
    opacity: 1
}

.dt-autofill-list ul li:last-child {
    border-bottom: none
}

.dt-autofill-background {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #212121;
    opacity: .65;
    top: 0;
    left: 0;
    z-index: 1100
}

@keyframes dtb-spinner {
    100% {
        transform: rotate(360deg)
    }
}

.dt-button-info {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1100;
    width: 30rem;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.dt-button-info h2,
.dt-button-info>div {
    padding: .5rem .5rem;
    margin: 0
}

.dt-button-info h2 {
    color: var(--bs-text-level-3);
    font-size: calc(1.275rem + .3vw);
    font-weight: 400;
    border-bottom: 1px solid var(--bs-border-color)
}

@media (min-width:1200px) {
    .dt-button-info h2 {
        font-size: 1.5rem
    }
}

.dt-button-info>div {
    color: var(--bs-text-level-1);
    font-size: 1rem;
    font-weight: 400
}

.dtb-hide-drop {
    display: none !important
}

.dt-button-spacer {
    display: inline-block;
    margin: .5rem;
    white-space: nowrap
}

.dt-button-spacer.bar {
    vertical-align: middle;
    border-left: 1px solid var(--bs-border-color);
    padding-left: .5rem
}

.dt-button-spacer.bar:empty {
    height: 1rem;
    width: 1px;
    padding-left: 0
}

.dt-button-collection {
    position: absolute;
    left: 0 !important;
    right: auto !important;
    padding: .5rem 0;
    z-index: 1000;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.dt-button-collection .dt-button {
    border-radius: 0
}

.dt-button-collection .dt-btn-split-wrapper {
    width: 100%
}

.dt-button-collection .btn-group {
    border-radius: .25rem
}

.dt-button-collection .btn-group button,
.dt-button-collection .btn-group button:last-child:first-child {
    border-radius: .25rem
}

.dt-button-collection .btn-group button:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.dt-button-collection .btn-group button:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.dt-button-collection .btn-group .dt-btn-split-drop:last-child {
    border: 1px solid var(--bs-border-color)
}

.dt-button-collection .btn-group .dt-btn-split-wrapper {
    border: none
}

.dt-button-collection .dt-button-spacer {
    width: 100%;
    text-align: center;
    font-size: 1rem;
    margin: .5rem 0
}

.dt-button-collection .dt-button-spacer.bar {
    border-left: none;
    border-bottom: 1px solid var(--bs-border-color);
    padding-left: 0
}

.dt-button-collection .dt-button-spacer:empty {
    height: 0;
    width: 100%
}

.dt-button-collection .dropdown-menu {
    position: static;
    display: block !important;
    min-width: 100%;
    background: 0 0;
    margin: 0;
    padding: 0 !important;
    border: 0 !important;
    z-index: 1001
}

.dt-button-collection .dropdown-menu .btn-group {
    width: 100%
}

.dt-button-collection.fixed {
    position: fixed;
    top: 50%;
    left: 50%;
    max-width: none;
    transform: translate(-50%, -50%)
}

.dt-button-collection.fixed::after,
.dt-button-collection.fixed::before {
    display: none
}

.dt-button-collection.fixed.two-column {
    margin-left: -25rem
}

.dt-button-collection.fixed.three-column {
    margin-left: -30rem
}

.dt-button-collection.fixed.four-column {
    margin-left: -35rem
}

.dt-button-collection>:last-child {
    display: block !important;
    column-gap: .75rem
}

.dt-button-collection>:last-child>* {
    break-inside: avoid
}

.dt-button-collection.two-column {
    width: 25rem
}

.dt-button-collection.two-column>:last-child {
    column-count: 2
}

.dt-button-collection.three-column {
    width: 30rem
}

.dt-button-collection.three-column>:last-child {
    column-count: 3
}

.dt-button-collection.four-column {
    width: 35rem
}

.dt-button-collection.four-column>:last-child {
    column-count: 4
}

.dt-button-collection-title {
    color: var(--bs-text-level-3);
    font-size: 1rem;
    font-weight: 500;
    padding: .75rem 1rem;
    text-align: center
}

.dt-button-collection-title:empty {
    display: none
}

.dt-button-background {
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 999
}

.dt-buttons .btn-group>.btn {
    border-top-right-radius: .25rem !important;
    border-bottom-right-radius: .25rem !important
}

.dt-buttons .btn.processing {
    position: relative;
    color: var(--bs-text-level-1)
}

.dt-buttons .btn.processing:after {
    content: "";
    position: absolute;
    display: block;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: -.625rem;
    margin-top: -.625rem;
    border: 2px solid #2196f3;
    border-left-color: transparent;
    border-right-color: transparent;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    animation: dtb-spinner 1.25s infinite linear
}

.dt-btn-split-wrapper .dt-btn-split-drop {
    border-top-right-radius: .25rem !important;
    border-bottom-right-radius: .25rem !important
}

.dataTables_wrapper .dt-buttons.btn-group .btn-group,
.dataTables_wrapper .dt-buttons.btn-group .btn-group:last-child:first-child {
    border-radius: .25rem
}

.dataTables_wrapper .dt-buttons.btn-group .btn-group:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.dataTables_wrapper .dt-buttons.btn-group .btn-group:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.dataTables_wrapper .dt-buttons.btn-group .btn-group .dt-btn-split-wrapper {
    border: none
}

.dataTables_wrapper .dt-buttons.btn-group .dt-btn-split-drop:last-child {
    border: 1px solid var(--bs-border-color)
}

.dt-button-spacer.bar:empty {
    height: inherit
}

.DTCR_clonedTable.dataTable {
    position: absolute !important;
    width: auto !important;
    opacity: .65;
    z-index: 202
}

.DTCR_pointer {
    background: #2196f3;
    width: 1px;
    z-index: 1
}

td.dtfc-fixed-left,
td.dtfc-fixed-right,
th.dtfc-fixed-left,
th.dtfc-fixed-right {
    z-index: 1
}

.dtfc-left-top-blocker,
.dtfc-right-top-blocker {
    margin-top: .5rem;
    border-bottom: 1px solid var(--bs-bg-level-3)
}

.dataTable.table-bordered.dtfc-has-left,
.dataTables_scroll.dtfc-has-left .table-bordered {
    border-left: none
}

.dataTables_scrollFootInner .table-bordered tr th:first-child,
.dataTables_scrollHeadInner .table-bordered tr th:first-child {
    border-left: 1px solid var(--bs-bg-level-3) !important
}

.dt-rowReorder-moving .dtfc-fixed-left,
.dt-rowReorder-moving .dtfc-fixed-right {
    border-top: 2px solid #2196f3 !important;
    border-bottom: 2px solid #2196f3 !important
}

.dt-rowReorder-moving .dtfc-fixed-left:first-child {
    border-left: 2px solid #2196f3 !important
}

.dt-rowReorder-moving .dtfc-fixed-right:last-child {
    border-right: 2px solid #2196f3 !important
}

.dataTable th {
    border-bottom-color: var(--bs-bg-level-3)
}

.dataTable.table-bordered.dtfc-has-left tr td {
    border-left-color: var(--bs-bg-level-3)
}

.DTFC_LeftBodyLiner thead tr,
.DTFC_RightBodyLiner thead tr,
.dataTables_scrollBody thead tr {
    height: 0 !important
}

.dataTable.fixedHeader-floating,
.dataTable.fixedHeader-locked {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    background: var(--bs-bg-level-1)
}

.dataTable.fixedHeader-locked {
    position: absolute !important
}

@media print {
    .fixedHeader-floating {
        display: none
    }
}

.dataTable tbody td.focus,
.dataTable tbody th.focus {
    box-shadow: inset 0 0 0 2px #2196f3
}

.dtk-focus-alt .dataTable tbody td.focus,
.dtk-focus-alt .dataTable tbody th.focus {
    box-shadow: inset 0 0 0 2px #f44336
}

.dataTable.dtr-inline.collapsed>tbody>tr .child,
.dataTable.dtr-inline.collapsed>tbody>tr .dataTables_empty {
    cursor: default !important
}

.dataTable.dtr-inline.collapsed>tbody>tr .child:before,
.dataTable.dtr-inline.collapsed>tbody>tr .dataTables_empty:before {
    display: none !important
}

.dataTable.dtr-inline.collapsed>tbody>tr .dtr-control {
    position: relative;
    white-space: nowrap;
    padding-left: .5rem;
    cursor: pointer
}

.dataTable.dtr-inline.collapsed>tbody>tr .dtr-control:before {
    display: inline-block;
    position: relative;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900;
    content: "\f0fe";
    color: var(--bs-text-level-1);
    font-size: 1.25rem;
    margin-right: .5rem
}

.dataTable.dtr-inline.collapsed>tbody>tr.parent>.dtr-control:before {
    color: #2196f3;
    content: "\f146"
}

.dataTable.dtr-column tbody tr .control {
    cursor: pointer
}

.dataTable.dtr-column tbody tr .control:before {
    display: inline-block;
    position: relative;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900;
    content: "\f0fe";
    color: var(--bs-text-level-1);
    font-size: 1.25rem;
    margin-right: .5rem
}

.dataTable.dtr-column tbody tr.parent .control:before {
    content: "\f146";
    color: #2196f3
}

.dataTable .child .dtr-details {
    display: inline-block;
    width: 100%;
    margin: 0;
    padding: 0;
    list-style-type: none
}

.dataTable .child .dtr-details>li {
    padding: .5rem 0;
    border-bottom: 1px solid var(--bs-border-color)
}

.dataTable .child .dtr-details>li:last-child {
    border-bottom: none
}

.dataTable .child .dtr-title {
    display: inline-block;
    min-width: 8rem;
    color: var(--bs-text-level-3);
    font-size: 1rem;
    font-weight: 500
}

.dataTable .child .dtr-data {
    display: inline-block;
    color: var(--bs-text-level-1);
    font-size: 1rem;
    font-weight: 400
}

.dtr-modal {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1100
}

.dtr-modal-display {
    position: absolute;
    width: 50%;
    height: 50%;
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1101;
    border-radius: .3rem
}

.dtr-modal-content {
    position: relative;
    padding: 1rem
}

.dtr-modal-close {
    position: absolute;
    width: calc(1.5em + (.75rem + 2px));
    height: calc(1.5em + (.75rem + 2px));
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-2);
    text-align: center;
    top: .5rem;
    right: .5rem;
    cursor: pointer;
    border-radius: .25rem
}

.dtr-modal-close:hover {
    color: #fff;
    background: #2196f3
}

.dtr-modal-background {
    position: fixed;
    background: #212121;
    opacity: .65;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1100
}

.dtr-bs-modal .table tr:first-child td {
    border-top: none
}

@media (max-width:767.98px) {
    .dtr-modal-display {
        width: 95%
    }
}

.dataTable .dtrg-group.dtrg-level-1 td,
.dataTable .dtrg-group.dtrg-level-2 td,
.dataTable .dtrg-group.dtrg-level-3 td,
.dataTable .dtrg-group.dtrg-level-4 td,
.dataTable .dtrg-group.dtrg-level-5 td {
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-3);
    font-size: 1rem;
    font-weight: 500;
    padding-bottom: .3rem;
    padding-top: .3rem;
    box-shadow: none !important
}

.dataTable .dtrg-group.dtrg-level-0 td {
    background: var(--bs-bg-level-2);
    box-shadow: none !important
}

.dataTable .dtrg-group.dtrg-level-1 td {
    padding-left: 1.5em
}

.dataTable .dtrg-group.dtrg-level-2 td {
    padding-left: 2em
}

.dataTable .dtrg-group.dtrg-level-3 td {
    padding-left: 2.5em
}

.dataTable .dtrg-group.dtrg-level-4 td {
    padding-left: 3em
}

.dataTable .dtrg-group.dtrg-level-5 td {
    padding-left: 3.5em
}

.dt-rowReorder-float {
    position: absolute !important;
    width: auto !important;
    table-layout: fixed;
    z-index: 2001;
    opacity: .65;
    outline-offset: -1px;
    outline: 1px solid #2196f3
}

.dt-rowReorder-moving {
    outline-offset: -1px;
    outline: 1px solid #2196f3
}

body.dt-rowReorder-noOverflow {
    overflow-x: hidden
}

.dataTable .reorder {
    text-align: center;
    cursor: move
}

.dts {
    display: block !important
}

.dts tbody td,
.dts tbody th {
    white-space: nowrap
}

.dts .dts_loading {
    z-index: 1
}

.dts .dts_label {
    display: none;
    position: absolute;
    font-size: 1rem;
    padding: .5rem .75rem;
    right: .5rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    z-index: 2;
    border-radius: .25rem
}

.dts .dataTables_scrollBody {
    background: var(--bs-bg-level-1)
}

.dts .dataTables_scrollBody table {
    z-index: 2
}

.dts .dataTables_length,
.dts .dataTables_paginate {
    display: none
}

.DTS .dataTables_scrollBody table {
    background: var(--bs-bg-level-1)
}

.dtsp-panes {
    font-size: 1rem;
    padding: 1rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.dtsp-titleRow {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem
}

.dtsp-titleRow>.btn {
    margin-left: .5rem
}

.dtsp-title {
    flex: 1;
    color: var(--bs-text-level-3);
    font-size: 1.25rem;
    font-weight: 500
}

.dtsp-topRow {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: nowrap;
    margin-bottom: 1rem
}

.dtsp-subRow1 {
    flex: 1;
    margin-right: 1rem
}

.dtsp-subRow2 {
    white-space: nowrap
}

.dtsp-collapseButton {
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.dtsp-collapseButton .dtsp-caret {
    position: relative
}

.dtsp-collapseButton .dtsp-caret:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-135deg);
    bottom: 2px;
    bottom: 0;
    transition: none
}

@media (prefers-reduced-motion:reduce) {
    .dtsp-collapseButton .dtsp-caret:after {
        transition: none
    }
}

.dtsp-collapseButton.dtsp-rotated .dtsp-caret::after {
    transform: rotate(45deg);
    bottom: 2px
}

.dtsp-searchIcon {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.dtsp-searchPanes {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin: -.5rem
}

.dtsp-searchPane {
    flex: 1;
    margin: .5rem
}

.dtsp-searchPane .dataTables_scrollBody {
    padding: .25rem .25rem;
    border: 1px solid var(--bs-border-color) !important;
    border-radius: .25rem
}

.dtsp-searchPane .dataTables_scrollBody:hover {
    border-color: #2196f3 !important
}

.dtsp-searchPane .dataTables_scrollBody table tbody tr {
    cursor: pointer
}

.dtsp-searchPane .dataTables_scrollBody table tbody tr .badge {
    min-width: 2.5rem;
    float: right
}

.dtsp-searchPane .dataTables_scrollBody table tbody tr .dtsp-countColumn {
    text-align: right
}

.dtsp-searchPane .dataTables_filter,
.dtsp-searchPane .dataTables_scrollHead,
.dtsp-searchPane thead {
    display: none
}

.dtsp-hidden {
    display: none !important
}

@media (max-width:767.98px) {
    .dtsp-searchPanes {
        flex-direction: column
    }
}

.dataTable tbody .selected,
.dataTable tbody .selected td,
.dataTable tbody .selected th {
    background: var(--bs-bg-level-4) !important;
    box-shadow: none !important
}

.dataTables_wrapper .select-info,
.dataTables_wrapper .select-item {
    margin-left: .75rem
}

.feather {
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round
}

/*!
 * Font Awesome Free 6.1.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2022 Fonticons, Inc.
 */
.fa {
    font-family: var(--fa-style-family, "Font Awesome 6 Free");
    font-weight: var(--fa-style, 900)
}

.fa,
.fa-brands,
.fa-duotone,
.fa-light,
.fa-regular,
.fa-solid,
.fa-thin,
.fab,
.fad,
.fal,
.far,
.fas,
.fat {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: var(--fa-display, inline-block);
    font-style: normal;
    font-variant: normal;
    line-height: 1;
    text-rendering: auto
}

.fa-1x {
    font-size: 1em
}

.fa-2x {
    font-size: 2em
}

.fa-3x {
    font-size: 3em
}

.fa-4x {
    font-size: 4em
}

.fa-5x {
    font-size: 5em
}

.fa-6x {
    font-size: 6em
}

.fa-7x {
    font-size: 7em
}

.fa-8x {
    font-size: 8em
}

.fa-9x {
    font-size: 9em
}

.fa-10x {
    font-size: 10em
}

.fa-2xs {
    font-size: .625em;
    line-height: .1em;
    vertical-align: .225em
}

.fa-xs {
    font-size: .75em;
    line-height: .08333em;
    vertical-align: .125em
}

.fa-sm {
    font-size: .875em;
    line-height: .07143em;
    vertical-align: .05357em
}

.fa-lg {
    font-size: 1.25em;
    line-height: .05em;
    vertical-align: -.075em
}

.fa-xl {
    font-size: 1.5em;
    line-height: .04167em;
    vertical-align: -.125em
}

.fa-2xl {
    font-size: 2em;
    line-height: .03125em;
    vertical-align: -.1875em
}

.fa-fw {
    text-align: center;
    width: 1.25em
}

.fa-ul {
    list-style-type: none;
    margin-left: var(--fa-li-margin, 2.5em);
    padding-left: 0
}

.fa-ul>li {
    position: relative
}

.fa-li {
    left: calc(var(--fa-li-width, 2em) * -1);
    position: absolute;
    text-align: center;
    width: var(--fa-li-width, 2em);
    line-height: inherit
}

.fa-border {
    border-color: var(--fa-border-color, #eee);
    border-radius: var(--fa-border-radius, .1em);
    border-style: var(--fa-border-style, solid);
    border-width: var(--fa-border-width, .08em);
    padding: var(--fa-border-padding, .2em .25em .15em)
}

.fa-pull-left {
    float: left;
    margin-right: var(--fa-pull-margin, .3em)
}

.fa-pull-right {
    float: right;
    margin-left: var(--fa-pull-margin, .3em)
}

.fa-beat {
    -webkit-animation-name: fa-beat;
    animation-name: fa-beat;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
    animation-timing-function: var(--fa-animation-timing, ease-in-out)
}

.fa-bounce {
    -webkit-animation-name: fa-bounce;
    animation-name: fa-bounce;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(.28, .84, .42, 1));
    animation-timing-function: var(--fa-animation-timing, cubic-bezier(.28, .84, .42, 1))
}

.fa-fade {
    -webkit-animation-name: fa-fade;
    animation-name: fa-fade;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1));
    animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1))
}

.fa-beat-fade {
    -webkit-animation-name: fa-beat-fade;
    animation-name: fa-beat-fade;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1));
    animation-timing-function: var(--fa-animation-timing, cubic-bezier(.4, 0, .6, 1))
}

.fa-flip {
    -webkit-animation-name: fa-flip;
    animation-name: fa-flip;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
    animation-timing-function: var(--fa-animation-timing, ease-in-out)
}

.fa-shake {
    -webkit-animation-name: fa-shake;
    animation-name: fa-shake;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, linear);
    animation-timing-function: var(--fa-animation-timing, linear)
}

.fa-spin {
    -webkit-animation-name: fa-spin;
    animation-name: fa-spin;
    -webkit-animation-delay: var(--fa-animation-delay, 0);
    animation-delay: var(--fa-animation-delay, 0);
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 2s);
    animation-duration: var(--fa-animation-duration, 2s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, linear);
    animation-timing-function: var(--fa-animation-timing, linear)
}

.fa-spin-reverse {
    --fa-animation-direction: reverse
}

.fa-pulse,
.fa-spin-pulse {
    -webkit-animation-name: fa-spin;
    animation-name: fa-spin;
    -webkit-animation-direction: var(--fa-animation-direction, normal);
    animation-direction: var(--fa-animation-direction, normal);
    -webkit-animation-duration: var(--fa-animation-duration, 1s);
    animation-duration: var(--fa-animation-duration, 1s);
    -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));
    animation-timing-function: var(--fa-animation-timing, steps(8))
}

@media (prefers-reduced-motion:reduce) {

    .fa-beat,
    .fa-beat-fade,
    .fa-bounce,
    .fa-fade,
    .fa-flip,
    .fa-pulse,
    .fa-shake,
    .fa-spin,
    .fa-spin-pulse {
        -webkit-animation-delay: -1ms;
        animation-delay: -1ms;
        -webkit-animation-duration: 1ms;
        animation-duration: 1ms;
        -webkit-animation-iteration-count: 1;
        animation-iteration-count: 1;
        transition-delay: 0s;
        transition-duration: 0s
    }
}

@-webkit-keyframes fa-beat {

    0%,
    90% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    45% {
        -webkit-transform: scale(var(--fa-beat-scale, 1.25));
        transform: scale(var(--fa-beat-scale, 1.25))
    }
}

@keyframes fa-beat {

    0%,
    90% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    45% {
        -webkit-transform: scale(var(--fa-beat-scale, 1.25));
        transform: scale(var(--fa-beat-scale, 1.25))
    }
}

@-webkit-keyframes fa-bounce {
    0% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }

    10% {
        -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, .9)) translateY(0);
        transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, .9)) translateY(0)
    }

    30% {
        -webkit-transform: scale(var(--fa-bounce-jump-scale-x, .9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -.5em));
        transform: scale(var(--fa-bounce-jump-scale-x, .9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -.5em))
    }

    50% {
        -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, .95)) translateY(0);
        transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, .95)) translateY(0)
    }

    57% {
        -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -.125em));
        transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -.125em))
    }

    64% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }

    100% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }
}

@keyframes fa-bounce {
    0% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }

    10% {
        -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, .9)) translateY(0);
        transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, .9)) translateY(0)
    }

    30% {
        -webkit-transform: scale(var(--fa-bounce-jump-scale-x, .9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -.5em));
        transform: scale(var(--fa-bounce-jump-scale-x, .9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -.5em))
    }

    50% {
        -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, .95)) translateY(0);
        transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, .95)) translateY(0)
    }

    57% {
        -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -.125em));
        transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -.125em))
    }

    64% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }

    100% {
        -webkit-transform: scale(1, 1) translateY(0);
        transform: scale(1, 1) translateY(0)
    }
}

@-webkit-keyframes fa-fade {
    50% {
        opacity: var(--fa-fade-opacity, .4)
    }
}

@keyframes fa-fade {
    50% {
        opacity: var(--fa-fade-opacity, .4)
    }
}

@-webkit-keyframes fa-beat-fade {

    0%,
    100% {
        opacity: var(--fa-beat-fade-opacity, .4);
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    50% {
        opacity: 1;
        -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
        transform: scale(var(--fa-beat-fade-scale, 1.125))
    }
}

@keyframes fa-beat-fade {

    0%,
    100% {
        opacity: var(--fa-beat-fade-opacity, .4);
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    50% {
        opacity: 1;
        -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
        transform: scale(var(--fa-beat-fade-scale, 1.125))
    }
}

@-webkit-keyframes fa-flip {
    50% {
        -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
        transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg))
    }
}

@keyframes fa-flip {
    50% {
        -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
        transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg))
    }
}

@-webkit-keyframes fa-shake {
    0% {
        -webkit-transform: rotate(-15deg);
        transform: rotate(-15deg)
    }

    4% {
        -webkit-transform: rotate(15deg);
        transform: rotate(15deg)
    }

    24%,
    8% {
        -webkit-transform: rotate(-18deg);
        transform: rotate(-18deg)
    }

    12%,
    28% {
        -webkit-transform: rotate(18deg);
        transform: rotate(18deg)
    }

    16% {
        -webkit-transform: rotate(-22deg);
        transform: rotate(-22deg)
    }

    20% {
        -webkit-transform: rotate(22deg);
        transform: rotate(22deg)
    }

    32% {
        -webkit-transform: rotate(-12deg);
        transform: rotate(-12deg)
    }

    36% {
        -webkit-transform: rotate(12deg);
        transform: rotate(12deg)
    }

    100%,
    40% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
}

@keyframes fa-shake {
    0% {
        -webkit-transform: rotate(-15deg);
        transform: rotate(-15deg)
    }

    4% {
        -webkit-transform: rotate(15deg);
        transform: rotate(15deg)
    }

    24%,
    8% {
        -webkit-transform: rotate(-18deg);
        transform: rotate(-18deg)
    }

    12%,
    28% {
        -webkit-transform: rotate(18deg);
        transform: rotate(18deg)
    }

    16% {
        -webkit-transform: rotate(-22deg);
        transform: rotate(-22deg)
    }

    20% {
        -webkit-transform: rotate(22deg);
        transform: rotate(22deg)
    }

    32% {
        -webkit-transform: rotate(-12deg);
        transform: rotate(-12deg)
    }

    36% {
        -webkit-transform: rotate(12deg);
        transform: rotate(12deg)
    }

    100%,
    40% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
}

@-webkit-keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.fa-rotate-90 {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.fa-rotate-180 {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.fa-rotate-270 {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
}

.fa-flip-horizontal {
    -webkit-transform: scale(-1, 1);
    transform: scale(-1, 1)
}

.fa-flip-vertical {
    -webkit-transform: scale(1, -1);
    transform: scale(1, -1)
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
    -webkit-transform: scale(-1, -1);
    transform: scale(-1, -1)
}

.fa-rotate-by {
    -webkit-transform: rotate(var(--fa-rotate-angle, none));
    transform: rotate(var(--fa-rotate-angle, none))
}

.fa-stack {
    display: inline-block;
    height: 2em;
    line-height: 2em;
    position: relative;
    vertical-align: middle;
    width: 2.5em
}

.fa-stack-1x,
.fa-stack-2x {
    left: 0;
    position: absolute;
    text-align: center;
    width: 100%;
    z-index: var(--fa-stack-z-index, auto)
}

.fa-stack-1x {
    line-height: inherit
}

.fa-stack-2x {
    font-size: 2em
}

.fa-inverse {
    color: var(--fa-inverse, #fff)
}

.fa-0::before {
    content: "0"
}

.fa-1::before {
    content: "1"
}

.fa-2::before {
    content: "2"
}

.fa-3::before {
    content: "3"
}

.fa-4::before {
    content: "4"
}

.fa-5::before {
    content: "5"
}

.fa-6::before {
    content: "6"
}

.fa-7::before {
    content: "7"
}

.fa-8::before {
    content: "8"
}

.fa-9::before {
    content: "9"
}

.fa-a::before {
    content: "A"
}

.fa-address-book::before {
    content: "\f2b9"
}

.fa-contact-book::before {
    content: "\f2b9"
}

.fa-address-card::before {
    content: "\f2bb"
}

.fa-contact-card::before {
    content: "\f2bb"
}

.fa-vcard::before {
    content: "\f2bb"
}

.fa-align-center::before {
    content: "\f037"
}

.fa-align-justify::before {
    content: "\f039"
}

.fa-align-left::before {
    content: "\f036"
}

.fa-align-right::before {
    content: "\f038"
}

.fa-anchor::before {
    content: "\f13d"
}

.fa-anchor-circle-check::before {
    content: "\e4aa"
}

.fa-anchor-circle-exclamation::before {
    content: "\e4ab"
}

.fa-anchor-circle-xmark::before {
    content: "\e4ac"
}

.fa-anchor-lock::before {
    content: "\e4ad"
}

.fa-angle-down::before {
    content: "\f107"
}

.fa-angle-left::before {
    content: "\f104"
}

.fa-angle-right::before {
    content: "\f105"
}

.fa-angle-up::before {
    content: "\f106"
}

.fa-angles-down::before {
    content: "\f103"
}

.fa-angle-double-down::before {
    content: "\f103"
}

.fa-angles-left::before {
    content: "\f100"
}

.fa-angle-double-left::before {
    content: "\f100"
}

.fa-angles-right::before {
    content: "\f101"
}

.fa-angle-double-right::before {
    content: "\f101"
}

.fa-angles-up::before {
    content: "\f102"
}

.fa-angle-double-up::before {
    content: "\f102"
}

.fa-ankh::before {
    content: "\f644"
}

.fa-apple-whole::before {
    content: "\f5d1"
}

.fa-apple-alt::before {
    content: "\f5d1"
}

.fa-archway::before {
    content: "\f557"
}

.fa-arrow-down::before {
    content: "\f063"
}

.fa-arrow-down-1-9::before {
    content: "\f162"
}

.fa-sort-numeric-asc::before {
    content: "\f162"
}

.fa-sort-numeric-down::before {
    content: "\f162"
}

.fa-arrow-down-9-1::before {
    content: "\f886"
}

.fa-sort-numeric-desc::before {
    content: "\f886"
}

.fa-sort-numeric-down-alt::before {
    content: "\f886"
}

.fa-arrow-down-a-z::before {
    content: "\f15d"
}

.fa-sort-alpha-asc::before {
    content: "\f15d"
}

.fa-sort-alpha-down::before {
    content: "\f15d"
}

.fa-arrow-down-long::before {
    content: "\f175"
}

.fa-long-arrow-down::before {
    content: "\f175"
}

.fa-arrow-down-short-wide::before {
    content: "\f884"
}

.fa-sort-amount-desc::before {
    content: "\f884"
}

.fa-sort-amount-down-alt::before {
    content: "\f884"
}

.fa-arrow-down-up-across-line::before {
    content: "\e4af"
}

.fa-arrow-down-up-lock::before {
    content: "\e4b0"
}

.fa-arrow-down-wide-short::before {
    content: "\f160"
}

.fa-sort-amount-asc::before {
    content: "\f160"
}

.fa-sort-amount-down::before {
    content: "\f160"
}

.fa-arrow-down-z-a::before {
    content: "\f881"
}

.fa-sort-alpha-desc::before {
    content: "\f881"
}

.fa-sort-alpha-down-alt::before {
    content: "\f881"
}

.fa-arrow-left::before {
    content: "\f060"
}

.fa-arrow-left-long::before {
    content: "\f177"
}

.fa-long-arrow-left::before {
    content: "\f177"
}

.fa-arrow-pointer::before {
    content: "\f245"
}

.fa-mouse-pointer::before {
    content: "\f245"
}

.fa-arrow-right::before {
    content: "\f061"
}

.fa-arrow-right-arrow-left::before {
    content: "\f0ec"
}

.fa-exchange::before {
    content: "\f0ec"
}

.fa-arrow-right-from-bracket::before {
    content: "\f08b"
}

.fa-sign-out::before {
    content: "\f08b"
}

.fa-arrow-right-long::before {
    content: "\f178"
}

.fa-long-arrow-right::before {
    content: "\f178"
}

.fa-arrow-right-to-bracket::before {
    content: "\f090"
}

.fa-sign-in::before {
    content: "\f090"
}

.fa-arrow-right-to-city::before {
    content: "\e4b3"
}

.fa-arrow-rotate-left::before {
    content: "\f0e2"
}

.fa-arrow-left-rotate::before {
    content: "\f0e2"
}

.fa-arrow-rotate-back::before {
    content: "\f0e2"
}

.fa-arrow-rotate-backward::before {
    content: "\f0e2"
}

.fa-undo::before {
    content: "\f0e2"
}

.fa-arrow-rotate-right::before {
    content: "\f01e"
}

.fa-arrow-right-rotate::before {
    content: "\f01e"
}

.fa-arrow-rotate-forward::before {
    content: "\f01e"
}

.fa-redo::before {
    content: "\f01e"
}

.fa-arrow-trend-down::before {
    content: "\e097"
}

.fa-arrow-trend-up::before {
    content: "\e098"
}

.fa-arrow-turn-down::before {
    content: "\f149"
}

.fa-level-down::before {
    content: "\f149"
}

.fa-arrow-turn-up::before {
    content: "\f148"
}

.fa-level-up::before {
    content: "\f148"
}

.fa-arrow-up::before {
    content: "\f062"
}

.fa-arrow-up-1-9::before {
    content: "\f163"
}

.fa-sort-numeric-up::before {
    content: "\f163"
}

.fa-arrow-up-9-1::before {
    content: "\f887"
}

.fa-sort-numeric-up-alt::before {
    content: "\f887"
}

.fa-arrow-up-a-z::before {
    content: "\f15e"
}

.fa-sort-alpha-up::before {
    content: "\f15e"
}

.fa-arrow-up-from-bracket::before {
    content: "\e09a"
}

.fa-arrow-up-from-ground-water::before {
    content: "\e4b5"
}

.fa-arrow-up-from-water-pump::before {
    content: "\e4b6"
}

.fa-arrow-up-long::before {
    content: "\f176"
}

.fa-long-arrow-up::before {
    content: "\f176"
}

.fa-arrow-up-right-dots::before {
    content: "\e4b7"
}

.fa-arrow-up-right-from-square::before {
    content: "\f08e"
}

.fa-external-link::before {
    content: "\f08e"
}

.fa-arrow-up-short-wide::before {
    content: "\f885"
}

.fa-sort-amount-up-alt::before {
    content: "\f885"
}

.fa-arrow-up-wide-short::before {
    content: "\f161"
}

.fa-sort-amount-up::before {
    content: "\f161"
}

.fa-arrow-up-z-a::before {
    content: "\f882"
}

.fa-sort-alpha-up-alt::before {
    content: "\f882"
}

.fa-arrows-down-to-line::before {
    content: "\e4b8"
}

.fa-arrows-down-to-people::before {
    content: "\e4b9"
}

.fa-arrows-left-right::before {
    content: "\f07e"
}

.fa-arrows-h::before {
    content: "\f07e"
}

.fa-arrows-left-right-to-line::before {
    content: "\e4ba"
}

.fa-arrows-rotate::before {
    content: "\f021"
}

.fa-refresh::before {
    content: "\f021"
}

.fa-sync::before {
    content: "\f021"
}

.fa-arrows-spin::before {
    content: "\e4bb"
}

.fa-arrows-split-up-and-left::before {
    content: "\e4bc"
}

.fa-arrows-to-circle::before {
    content: "\e4bd"
}

.fa-arrows-to-dot::before {
    content: "\e4be"
}

.fa-arrows-to-eye::before {
    content: "\e4bf"
}

.fa-arrows-turn-right::before {
    content: "\e4c0"
}

.fa-arrows-turn-to-dots::before {
    content: "\e4c1"
}

.fa-arrows-up-down::before {
    content: "\f07d"
}

.fa-arrows-v::before {
    content: "\f07d"
}

.fa-arrows-up-down-left-right::before {
    content: "\f047"
}

.fa-arrows::before {
    content: "\f047"
}

.fa-arrows-up-to-line::before {
    content: "\e4c2"
}

.fa-asterisk::before {
    content: "*"
}

.fa-at::before {
    content: "@"
}

.fa-atom::before {
    content: "\f5d2"
}

.fa-audio-description::before {
    content: "\f29e"
}

.fa-austral-sign::before {
    content: "\e0a9"
}

.fa-award::before {
    content: "\f559"
}

.fa-b::before {
    content: "B"
}

.fa-baby::before {
    content: "\f77c"
}

.fa-baby-carriage::before {
    content: "\f77d"
}

.fa-carriage-baby::before {
    content: "\f77d"
}

.fa-backward::before {
    content: "\f04a"
}

.fa-backward-fast::before {
    content: "\f049"
}

.fa-fast-backward::before {
    content: "\f049"
}

.fa-backward-step::before {
    content: "\f048"
}

.fa-step-backward::before {
    content: "\f048"
}

.fa-bacon::before {
    content: "\f7e5"
}

.fa-bacteria::before {
    content: "\e059"
}

.fa-bacterium::before {
    content: "\e05a"
}

.fa-bag-shopping::before {
    content: "\f290"
}

.fa-shopping-bag::before {
    content: "\f290"
}

.fa-bahai::before {
    content: "\f666"
}

.fa-baht-sign::before {
    content: "\e0ac"
}

.fa-ban::before {
    content: "\f05e"
}

.fa-cancel::before {
    content: "\f05e"
}

.fa-ban-smoking::before {
    content: "\f54d"
}

.fa-smoking-ban::before {
    content: "\f54d"
}

.fa-bandage::before {
    content: "\f462"
}

.fa-band-aid::before {
    content: "\f462"
}

.fa-barcode::before {
    content: "\f02a"
}

.fa-bars::before {
    content: "\f0c9"
}

.fa-navicon::before {
    content: "\f0c9"
}

.fa-bars-progress::before {
    content: "\f828"
}

.fa-tasks-alt::before {
    content: "\f828"
}

.fa-bars-staggered::before {
    content: "\f550"
}

.fa-reorder::before {
    content: "\f550"
}

.fa-stream::before {
    content: "\f550"
}

.fa-baseball::before {
    content: "\f433"
}

.fa-baseball-ball::before {
    content: "\f433"
}

.fa-baseball-bat-ball::before {
    content: "\f432"
}

.fa-basket-shopping::before {
    content: "\f291"
}

.fa-shopping-basket::before {
    content: "\f291"
}

.fa-basketball::before {
    content: "\f434"
}

.fa-basketball-ball::before {
    content: "\f434"
}

.fa-bath::before {
    content: "\f2cd"
}

.fa-bathtub::before {
    content: "\f2cd"
}

.fa-battery-empty::before {
    content: "\f244"
}

.fa-battery-0::before {
    content: "\f244"
}

.fa-battery-full::before {
    content: "\f240"
}

.fa-battery::before {
    content: "\f240"
}

.fa-battery-5::before {
    content: "\f240"
}

.fa-battery-half::before {
    content: "\f242"
}

.fa-battery-3::before {
    content: "\f242"
}

.fa-battery-quarter::before {
    content: "\f243"
}

.fa-battery-2::before {
    content: "\f243"
}

.fa-battery-three-quarters::before {
    content: "\f241"
}

.fa-battery-4::before {
    content: "\f241"
}

.fa-bed::before {
    content: "\f236"
}

.fa-bed-pulse::before {
    content: "\f487"
}

.fa-procedures::before {
    content: "\f487"
}

.fa-beer-mug-empty::before {
    content: "\f0fc"
}

.fa-beer::before {
    content: "\f0fc"
}

.fa-bell::before {
    content: "\f0f3"
}

.fa-bell-concierge::before {
    content: "\f562"
}

.fa-concierge-bell::before {
    content: "\f562"
}

.fa-bell-slash::before {
    content: "\f1f6"
}

.fa-bezier-curve::before {
    content: "\f55b"
}

.fa-bicycle::before {
    content: "\f206"
}

.fa-binoculars::before {
    content: "\f1e5"
}

.fa-biohazard::before {
    content: "\f780"
}

.fa-bitcoin-sign::before {
    content: "\e0b4"
}

.fa-blender::before {
    content: "\f517"
}

.fa-blender-phone::before {
    content: "\f6b6"
}

.fa-blog::before {
    content: "\f781"
}

.fa-bold::before {
    content: "\f032"
}

.fa-bolt::before {
    content: "\f0e7"
}

.fa-zap::before {
    content: "\f0e7"
}

.fa-bolt-lightning::before {
    content: "\e0b7"
}

.fa-bomb::before {
    content: "\f1e2"
}

.fa-bone::before {
    content: "\f5d7"
}

.fa-bong::before {
    content: "\f55c"
}

.fa-book::before {
    content: "\f02d"
}

.fa-book-atlas::before {
    content: "\f558"
}

.fa-atlas::before {
    content: "\f558"
}

.fa-book-bible::before {
    content: "\f647"
}

.fa-bible::before {
    content: "\f647"
}

.fa-book-bookmark::before {
    content: "\e0bb"
}

.fa-book-journal-whills::before {
    content: "\f66a"
}

.fa-journal-whills::before {
    content: "\f66a"
}

.fa-book-medical::before {
    content: "\f7e6"
}

.fa-book-open::before {
    content: "\f518"
}

.fa-book-open-reader::before {
    content: "\f5da"
}

.fa-book-reader::before {
    content: "\f5da"
}

.fa-book-quran::before {
    content: "\f687"
}

.fa-quran::before {
    content: "\f687"
}

.fa-book-skull::before {
    content: "\f6b7"
}

.fa-book-dead::before {
    content: "\f6b7"
}

.fa-bookmark::before {
    content: "\f02e"
}

.fa-border-all::before {
    content: "\f84c"
}

.fa-border-none::before {
    content: "\f850"
}

.fa-border-top-left::before {
    content: "\f853"
}

.fa-border-style::before {
    content: "\f853"
}

.fa-bore-hole::before {
    content: "\e4c3"
}

.fa-bottle-droplet::before {
    content: "\e4c4"
}

.fa-bottle-water::before {
    content: "\e4c5"
}

.fa-bowl-food::before {
    content: "\e4c6"
}

.fa-bowl-rice::before {
    content: "\e2eb"
}

.fa-bowling-ball::before {
    content: "\f436"
}

.fa-box::before {
    content: "\f466"
}

.fa-box-archive::before {
    content: "\f187"
}

.fa-archive::before {
    content: "\f187"
}

.fa-box-open::before {
    content: "\f49e"
}

.fa-box-tissue::before {
    content: "\e05b"
}

.fa-boxes-packing::before {
    content: "\e4c7"
}

.fa-boxes-stacked::before {
    content: "\f468"
}

.fa-boxes::before {
    content: "\f468"
}

.fa-boxes-alt::before {
    content: "\f468"
}

.fa-braille::before {
    content: "\f2a1"
}

.fa-brain::before {
    content: "\f5dc"
}

.fa-brazilian-real-sign::before {
    content: "\e46c"
}

.fa-bread-slice::before {
    content: "\f7ec"
}

.fa-bridge::before {
    content: "\e4c8"
}

.fa-bridge-circle-check::before {
    content: "\e4c9"
}

.fa-bridge-circle-exclamation::before {
    content: "\e4ca"
}

.fa-bridge-circle-xmark::before {
    content: "\e4cb"
}

.fa-bridge-lock::before {
    content: "\e4cc"
}

.fa-bridge-water::before {
    content: "\e4ce"
}

.fa-briefcase::before {
    content: "\f0b1"
}

.fa-briefcase-medical::before {
    content: "\f469"
}

.fa-broom::before {
    content: "\f51a"
}

.fa-broom-ball::before {
    content: "\f458"
}

.fa-quidditch::before {
    content: "\f458"
}

.fa-quidditch-broom-ball::before {
    content: "\f458"
}

.fa-brush::before {
    content: "\f55d"
}

.fa-bucket::before {
    content: "\e4cf"
}

.fa-bug::before {
    content: "\f188"
}

.fa-bug-slash::before {
    content: "\e490"
}

.fa-bugs::before {
    content: "\e4d0"
}

.fa-building::before {
    content: "\f1ad"
}

.fa-building-circle-arrow-right::before {
    content: "\e4d1"
}

.fa-building-circle-check::before {
    content: "\e4d2"
}

.fa-building-circle-exclamation::before {
    content: "\e4d3"
}

.fa-building-circle-xmark::before {
    content: "\e4d4"
}

.fa-building-columns::before {
    content: "\f19c"
}

.fa-bank::before {
    content: "\f19c"
}

.fa-institution::before {
    content: "\f19c"
}

.fa-museum::before {
    content: "\f19c"
}

.fa-university::before {
    content: "\f19c"
}

.fa-building-flag::before {
    content: "\e4d5"
}

.fa-building-lock::before {
    content: "\e4d6"
}

.fa-building-ngo::before {
    content: "\e4d7"
}

.fa-building-shield::before {
    content: "\e4d8"
}

.fa-building-un::before {
    content: "\e4d9"
}

.fa-building-user::before {
    content: "\e4da"
}

.fa-building-wheat::before {
    content: "\e4db"
}

.fa-bullhorn::before {
    content: "\f0a1"
}

.fa-bullseye::before {
    content: "\f140"
}

.fa-burger::before {
    content: "\f805"
}

.fa-hamburger::before {
    content: "\f805"
}

.fa-burst::before {
    content: "\e4dc"
}

.fa-bus::before {
    content: "\f207"
}

.fa-bus-simple::before {
    content: "\f55e"
}

.fa-bus-alt::before {
    content: "\f55e"
}

.fa-business-time::before {
    content: "\f64a"
}

.fa-briefcase-clock::before {
    content: "\f64a"
}

.fa-c::before {
    content: "C"
}

.fa-cake-candles::before {
    content: "\f1fd"
}

.fa-birthday-cake::before {
    content: "\f1fd"
}

.fa-cake::before {
    content: "\f1fd"
}

.fa-calculator::before {
    content: "\f1ec"
}

.fa-calendar::before {
    content: "\f133"
}

.fa-calendar-check::before {
    content: "\f274"
}

.fa-calendar-day::before {
    content: "\f783"
}

.fa-calendar-days::before {
    content: "\f073"
}

.fa-calendar-alt::before {
    content: "\f073"
}

.fa-calendar-minus::before {
    content: "\f272"
}

.fa-calendar-plus::before {
    content: "\f271"
}

.fa-calendar-week::before {
    content: "\f784"
}

.fa-calendar-xmark::before {
    content: "\f273"
}

.fa-calendar-times::before {
    content: "\f273"
}

.fa-camera::before {
    content: "\f030"
}

.fa-camera-alt::before {
    content: "\f030"
}

.fa-camera-retro::before {
    content: "\f083"
}

.fa-camera-rotate::before {
    content: "\e0d8"
}

.fa-campground::before {
    content: "\f6bb"
}

.fa-candy-cane::before {
    content: "\f786"
}

.fa-cannabis::before {
    content: "\f55f"
}

.fa-capsules::before {
    content: "\f46b"
}

.fa-car::before {
    content: "\f1b9"
}

.fa-automobile::before {
    content: "\f1b9"
}

.fa-car-battery::before {
    content: "\f5df"
}

.fa-battery-car::before {
    content: "\f5df"
}

.fa-car-burst::before {
    content: "\f5e1"
}

.fa-car-crash::before {
    content: "\f5e1"
}

.fa-car-on::before {
    content: "\e4dd"
}

.fa-car-rear::before {
    content: "\f5de"
}

.fa-car-alt::before {
    content: "\f5de"
}

.fa-car-side::before {
    content: "\f5e4"
}

.fa-car-tunnel::before {
    content: "\e4de"
}

.fa-caravan::before {
    content: "\f8ff"
}

.fa-caret-down::before {
    content: "\f0d7"
}

.fa-caret-left::before {
    content: "\f0d9"
}

.fa-caret-right::before {
    content: "\f0da"
}

.fa-caret-up::before {
    content: "\f0d8"
}

.fa-carrot::before {
    content: "\f787"
}

.fa-cart-arrow-down::before {
    content: "\f218"
}

.fa-cart-flatbed::before {
    content: "\f474"
}

.fa-dolly-flatbed::before {
    content: "\f474"
}

.fa-cart-flatbed-suitcase::before {
    content: "\f59d"
}

.fa-luggage-cart::before {
    content: "\f59d"
}

.fa-cart-plus::before {
    content: "\f217"
}

.fa-cart-shopping::before {
    content: "\f07a"
}

.fa-shopping-cart::before {
    content: "\f07a"
}

.fa-cash-register::before {
    content: "\f788"
}

.fa-cat::before {
    content: "\f6be"
}

.fa-cedi-sign::before {
    content: "\e0df"
}

.fa-cent-sign::before {
    content: "\e3f5"
}

.fa-certificate::before {
    content: "\f0a3"
}

.fa-chair::before {
    content: "\f6c0"
}

.fa-chalkboard::before {
    content: "\f51b"
}

.fa-blackboard::before {
    content: "\f51b"
}

.fa-chalkboard-user::before {
    content: "\f51c"
}

.fa-chalkboard-teacher::before {
    content: "\f51c"
}

.fa-champagne-glasses::before {
    content: "\f79f"
}

.fa-glass-cheers::before {
    content: "\f79f"
}

.fa-charging-station::before {
    content: "\f5e7"
}

.fa-chart-area::before {
    content: "\f1fe"
}

.fa-area-chart::before {
    content: "\f1fe"
}

.fa-chart-bar::before {
    content: "\f080"
}

.fa-bar-chart::before {
    content: "\f080"
}

.fa-chart-column::before {
    content: "\e0e3"
}

.fa-chart-gantt::before {
    content: "\e0e4"
}

.fa-chart-line::before {
    content: "\f201"
}

.fa-line-chart::before {
    content: "\f201"
}

.fa-chart-pie::before {
    content: "\f200"
}

.fa-pie-chart::before {
    content: "\f200"
}

.fa-chart-simple::before {
    content: "\e473"
}

.fa-check::before {
    content: "\f00c"
}

.fa-check-double::before {
    content: "\f560"
}

.fa-check-to-slot::before {
    content: "\f772"
}

.fa-vote-yea::before {
    content: "\f772"
}

.fa-cheese::before {
    content: "\f7ef"
}

.fa-chess::before {
    content: "\f439"
}

.fa-chess-bishop::before {
    content: "\f43a"
}

.fa-chess-board::before {
    content: "\f43c"
}

.fa-chess-king::before {
    content: "\f43f"
}

.fa-chess-knight::before {
    content: "\f441"
}

.fa-chess-pawn::before {
    content: "\f443"
}

.fa-chess-queen::before {
    content: "\f445"
}

.fa-chess-rook::before {
    content: "\f447"
}

.fa-chevron-down::before {
    content: "\f078"
}

.fa-chevron-left::before {
    content: "\f053"
}

.fa-chevron-right::before {
    content: "\f054"
}

.fa-chevron-up::before {
    content: "\f077"
}

.fa-child::before {
    content: "\f1ae"
}

.fa-child-dress::before {
    content: "\e59c"
}

.fa-child-reaching::before {
    content: "\e59d"
}

.fa-child-rifle::before {
    content: "\e4e0"
}

.fa-children::before {
    content: "\e4e1"
}

.fa-church::before {
    content: "\f51d"
}

.fa-circle::before {
    content: "\f111"
}

.fa-circle-arrow-down::before {
    content: "\f0ab"
}

.fa-arrow-circle-down::before {
    content: "\f0ab"
}

.fa-circle-arrow-left::before {
    content: "\f0a8"
}

.fa-arrow-circle-left::before {
    content: "\f0a8"
}

.fa-circle-arrow-right::before {
    content: "\f0a9"
}

.fa-arrow-circle-right::before {
    content: "\f0a9"
}

.fa-circle-arrow-up::before {
    content: "\f0aa"
}

.fa-arrow-circle-up::before {
    content: "\f0aa"
}

.fa-circle-check::before {
    content: "\f058"
}

.fa-check-circle::before {
    content: "\f058"
}

.fa-circle-chevron-down::before {
    content: "\f13a"
}

.fa-chevron-circle-down::before {
    content: "\f13a"
}

.fa-circle-chevron-left::before {
    content: "\f137"
}

.fa-chevron-circle-left::before {
    content: "\f137"
}

.fa-circle-chevron-right::before {
    content: "\f138"
}

.fa-chevron-circle-right::before {
    content: "\f138"
}

.fa-circle-chevron-up::before {
    content: "\f139"
}

.fa-chevron-circle-up::before {
    content: "\f139"
}

.fa-circle-dollar-to-slot::before {
    content: "\f4b9"
}

.fa-donate::before {
    content: "\f4b9"
}

.fa-circle-dot::before {
    content: "\f192"
}

.fa-dot-circle::before {
    content: "\f192"
}

.fa-circle-down::before {
    content: "\f358"
}

.fa-arrow-alt-circle-down::before {
    content: "\f358"
}

.fa-circle-exclamation::before {
    content: "\f06a"
}

.fa-exclamation-circle::before {
    content: "\f06a"
}

.fa-circle-h::before {
    content: "\f47e"
}

.fa-hospital-symbol::before {
    content: "\f47e"
}

.fa-circle-half-stroke::before {
    content: "\f042"
}

.fa-adjust::before {
    content: "\f042"
}

.fa-circle-info::before {
    content: "\f05a"
}

.fa-info-circle::before {
    content: "\f05a"
}

.fa-circle-left::before {
    content: "\f359"
}

.fa-arrow-alt-circle-left::before {
    content: "\f359"
}

.fa-circle-minus::before {
    content: "\f056"
}

.fa-minus-circle::before {
    content: "\f056"
}

.fa-circle-nodes::before {
    content: "\e4e2"
}

.fa-circle-notch::before {
    content: "\f1ce"
}

.fa-circle-pause::before {
    content: "\f28b"
}

.fa-pause-circle::before {
    content: "\f28b"
}

.fa-circle-play::before {
    content: "\f144"
}

.fa-play-circle::before {
    content: "\f144"
}

.fa-circle-plus::before {
    content: "\f055"
}

.fa-plus-circle::before {
    content: "\f055"
}

.fa-circle-question::before {
    content: "\f059"
}

.fa-question-circle::before {
    content: "\f059"
}

.fa-circle-radiation::before {
    content: "\f7ba"
}

.fa-radiation-alt::before {
    content: "\f7ba"
}

.fa-circle-right::before {
    content: "\f35a"
}

.fa-arrow-alt-circle-right::before {
    content: "\f35a"
}

.fa-circle-stop::before {
    content: "\f28d"
}

.fa-stop-circle::before {
    content: "\f28d"
}

.fa-circle-up::before {
    content: "\f35b"
}

.fa-arrow-alt-circle-up::before {
    content: "\f35b"
}

.fa-circle-user::before {
    content: "\f2bd"
}

.fa-user-circle::before {
    content: "\f2bd"
}

.fa-circle-xmark::before {
    content: "\f057"
}

.fa-times-circle::before {
    content: "\f057"
}

.fa-xmark-circle::before {
    content: "\f057"
}

.fa-city::before {
    content: "\f64f"
}

.fa-clapperboard::before {
    content: "\e131"
}

.fa-clipboard::before {
    content: "\f328"
}

.fa-clipboard-check::before {
    content: "\f46c"
}

.fa-clipboard-list::before {
    content: "\f46d"
}

.fa-clipboard-question::before {
    content: "\e4e3"
}

.fa-clipboard-user::before {
    content: "\f7f3"
}

.fa-clock::before {
    content: "\f017"
}

.fa-clock-four::before {
    content: "\f017"
}

.fa-clock-rotate-left::before {
    content: "\f1da"
}

.fa-history::before {
    content: "\f1da"
}

.fa-clone::before {
    content: "\f24d"
}

.fa-closed-captioning::before {
    content: "\f20a"
}

.fa-cloud::before {
    content: "\f0c2"
}

.fa-cloud-arrow-down::before {
    content: "\f0ed"
}

.fa-cloud-download::before {
    content: "\f0ed"
}

.fa-cloud-download-alt::before {
    content: "\f0ed"
}

.fa-cloud-arrow-up::before {
    content: "\f0ee"
}

.fa-cloud-upload::before {
    content: "\f0ee"
}

.fa-cloud-upload-alt::before {
    content: "\f0ee"
}

.fa-cloud-bolt::before {
    content: "\f76c"
}

.fa-thunderstorm::before {
    content: "\f76c"
}

.fa-cloud-meatball::before {
    content: "\f73b"
}

.fa-cloud-moon::before {
    content: "\f6c3"
}

.fa-cloud-moon-rain::before {
    content: "\f73c"
}

.fa-cloud-rain::before {
    content: "\f73d"
}

.fa-cloud-showers-heavy::before {
    content: "\f740"
}

.fa-cloud-showers-water::before {
    content: "\e4e4"
}

.fa-cloud-sun::before {
    content: "\f6c4"
}

.fa-cloud-sun-rain::before {
    content: "\f743"
}

.fa-clover::before {
    content: "\e139"
}

.fa-code::before {
    content: "\f121"
}

.fa-code-branch::before {
    content: "\f126"
}

.fa-code-commit::before {
    content: "\f386"
}

.fa-code-compare::before {
    content: "\e13a"
}

.fa-code-fork::before {
    content: "\e13b"
}

.fa-code-merge::before {
    content: "\f387"
}

.fa-code-pull-request::before {
    content: "\e13c"
}

.fa-coins::before {
    content: "\f51e"
}

.fa-colon-sign::before {
    content: "\e140"
}

.fa-comment::before {
    content: "\f075"
}

.fa-comment-dollar::before {
    content: "\f651"
}

.fa-comment-dots::before {
    content: "\f4ad"
}

.fa-commenting::before {
    content: "\f4ad"
}

.fa-comment-medical::before {
    content: "\f7f5"
}

.fa-comment-slash::before {
    content: "\f4b3"
}

.fa-comment-sms::before {
    content: "\f7cd"
}

.fa-sms::before {
    content: "\f7cd"
}

.fa-comments::before {
    content: "\f086"
}

.fa-comments-dollar::before {
    content: "\f653"
}

.fa-compact-disc::before {
    content: "\f51f"
}

.fa-compass::before {
    content: "\f14e"
}

.fa-compass-drafting::before {
    content: "\f568"
}

.fa-drafting-compass::before {
    content: "\f568"
}

.fa-compress::before {
    content: "\f066"
}

.fa-computer::before {
    content: "\e4e5"
}

.fa-computer-mouse::before {
    content: "\f8cc"
}

.fa-mouse::before {
    content: "\f8cc"
}

.fa-cookie::before {
    content: "\f563"
}

.fa-cookie-bite::before {
    content: "\f564"
}

.fa-copy::before {
    content: "\f0c5"
}

.fa-copyright::before {
    content: "\f1f9"
}

.fa-couch::before {
    content: "\f4b8"
}

.fa-cow::before {
    content: "\f6c8"
}

.fa-credit-card::before {
    content: "\f09d"
}

.fa-credit-card-alt::before {
    content: "\f09d"
}

.fa-crop::before {
    content: "\f125"
}

.fa-crop-simple::before {
    content: "\f565"
}

.fa-crop-alt::before {
    content: "\f565"
}

.fa-cross::before {
    content: "\f654"
}

.fa-crosshairs::before {
    content: "\f05b"
}

.fa-crow::before {
    content: "\f520"
}

.fa-crown::before {
    content: "\f521"
}

.fa-crutch::before {
    content: "\f7f7"
}

.fa-cruzeiro-sign::before {
    content: "\e152"
}

.fa-cube::before {
    content: "\f1b2"
}

.fa-cubes::before {
    content: "\f1b3"
}

.fa-cubes-stacked::before {
    content: "\e4e6"
}

.fa-d::before {
    content: "D"
}

.fa-database::before {
    content: "\f1c0"
}

.fa-delete-left::before {
    content: "\f55a"
}

.fa-backspace::before {
    content: "\f55a"
}

.fa-democrat::before {
    content: "\f747"
}

.fa-desktop::before {
    content: "\f390"
}

.fa-desktop-alt::before {
    content: "\f390"
}

.fa-dharmachakra::before {
    content: "\f655"
}

.fa-diagram-next::before {
    content: "\e476"
}

.fa-diagram-predecessor::before {
    content: "\e477"
}

.fa-diagram-project::before {
    content: "\f542"
}

.fa-project-diagram::before {
    content: "\f542"
}

.fa-diagram-successor::before {
    content: "\e47a"
}

.fa-diamond::before {
    content: "\f219"
}

.fa-diamond-turn-right::before {
    content: "\f5eb"
}

.fa-directions::before {
    content: "\f5eb"
}

.fa-dice::before {
    content: "\f522"
}

.fa-dice-d20::before {
    content: "\f6cf"
}

.fa-dice-d6::before {
    content: "\f6d1"
}

.fa-dice-five::before {
    content: "\f523"
}

.fa-dice-four::before {
    content: "\f524"
}

.fa-dice-one::before {
    content: "\f525"
}

.fa-dice-six::before {
    content: "\f526"
}

.fa-dice-three::before {
    content: "\f527"
}

.fa-dice-two::before {
    content: "\f528"
}

.fa-disease::before {
    content: "\f7fa"
}

.fa-display::before {
    content: "\e163"
}

.fa-divide::before {
    content: "\f529"
}

.fa-dna::before {
    content: "\f471"
}

.fa-dog::before {
    content: "\f6d3"
}

.fa-dollar-sign::before {
    content: "$"
}

.fa-dollar::before {
    content: "$"
}

.fa-usd::before {
    content: "$"
}

.fa-dolly::before {
    content: "\f472"
}

.fa-dolly-box::before {
    content: "\f472"
}

.fa-dong-sign::before {
    content: "\e169"
}

.fa-door-closed::before {
    content: "\f52a"
}

.fa-door-open::before {
    content: "\f52b"
}

.fa-dove::before {
    content: "\f4ba"
}

.fa-down-left-and-up-right-to-center::before {
    content: "\f422"
}

.fa-compress-alt::before {
    content: "\f422"
}

.fa-down-long::before {
    content: "\f309"
}

.fa-long-arrow-alt-down::before {
    content: "\f309"
}

.fa-download::before {
    content: "\f019"
}

.fa-dragon::before {
    content: "\f6d5"
}

.fa-draw-polygon::before {
    content: "\f5ee"
}

.fa-droplet::before {
    content: "\f043"
}

.fa-tint::before {
    content: "\f043"
}

.fa-droplet-slash::before {
    content: "\f5c7"
}

.fa-tint-slash::before {
    content: "\f5c7"
}

.fa-drum::before {
    content: "\f569"
}

.fa-drum-steelpan::before {
    content: "\f56a"
}

.fa-drumstick-bite::before {
    content: "\f6d7"
}

.fa-dumbbell::before {
    content: "\f44b"
}

.fa-dumpster::before {
    content: "\f793"
}

.fa-dumpster-fire::before {
    content: "\f794"
}

.fa-dungeon::before {
    content: "\f6d9"
}

.fa-e::before {
    content: "E"
}

.fa-ear-deaf::before {
    content: "\f2a4"
}

.fa-deaf::before {
    content: "\f2a4"
}

.fa-deafness::before {
    content: "\f2a4"
}

.fa-hard-of-hearing::before {
    content: "\f2a4"
}

.fa-ear-listen::before {
    content: "\f2a2"
}

.fa-assistive-listening-systems::before {
    content: "\f2a2"
}

.fa-earth-africa::before {
    content: "\f57c"
}

.fa-globe-africa::before {
    content: "\f57c"
}

.fa-earth-americas::before {
    content: "\f57d"
}

.fa-earth::before {
    content: "\f57d"
}

.fa-earth-america::before {
    content: "\f57d"
}

.fa-globe-americas::before {
    content: "\f57d"
}

.fa-earth-asia::before {
    content: "\f57e"
}

.fa-globe-asia::before {
    content: "\f57e"
}

.fa-earth-europe::before {
    content: "\f7a2"
}

.fa-globe-europe::before {
    content: "\f7a2"
}

.fa-earth-oceania::before {
    content: "\e47b"
}

.fa-globe-oceania::before {
    content: "\e47b"
}

.fa-egg::before {
    content: "\f7fb"
}

.fa-eject::before {
    content: "\f052"
}

.fa-elevator::before {
    content: "\e16d"
}

.fa-ellipsis::before {
    content: "\f141"
}

.fa-ellipsis-h::before {
    content: "\f141"
}

.fa-ellipsis-vertical::before {
    content: "\f142"
}

.fa-ellipsis-v::before {
    content: "\f142"
}

.fa-envelope::before {
    content: "\f0e0"
}

.fa-envelope-circle-check::before {
    content: "\e4e8"
}

.fa-envelope-open::before {
    content: "\f2b6"
}

.fa-envelope-open-text::before {
    content: "\f658"
}

.fa-envelopes-bulk::before {
    content: "\f674"
}

.fa-mail-bulk::before {
    content: "\f674"
}

.fa-equals::before {
    content: "="
}

.fa-eraser::before {
    content: "\f12d"
}

.fa-ethernet::before {
    content: "\f796"
}

.fa-euro-sign::before {
    content: "\f153"
}

.fa-eur::before {
    content: "\f153"
}

.fa-euro::before {
    content: "\f153"
}

.fa-exclamation::before {
    content: "!"
}

.fa-expand::before {
    content: "\f065"
}

.fa-explosion::before {
    content: "\e4e9"
}

.fa-eye::before {
    content: "\f06e"
}

.fa-eye-dropper::before {
    content: "\f1fb"
}

.fa-eye-dropper-empty::before {
    content: "\f1fb"
}

.fa-eyedropper::before {
    content: "\f1fb"
}

.fa-eye-low-vision::before {
    content: "\f2a8"
}

.fa-low-vision::before {
    content: "\f2a8"
}

.fa-eye-slash::before {
    content: "\f070"
}

.fa-f::before {
    content: "F"
}

.fa-face-angry::before {
    content: "\f556"
}

.fa-angry::before {
    content: "\f556"
}

.fa-face-dizzy::before {
    content: "\f567"
}

.fa-dizzy::before {
    content: "\f567"
}

.fa-face-flushed::before {
    content: "\f579"
}

.fa-flushed::before {
    content: "\f579"
}

.fa-face-frown::before {
    content: "\f119"
}

.fa-frown::before {
    content: "\f119"
}

.fa-face-frown-open::before {
    content: "\f57a"
}

.fa-frown-open::before {
    content: "\f57a"
}

.fa-face-grimace::before {
    content: "\f57f"
}

.fa-grimace::before {
    content: "\f57f"
}

.fa-face-grin::before {
    content: "\f580"
}

.fa-grin::before {
    content: "\f580"
}

.fa-face-grin-beam::before {
    content: "\f582"
}

.fa-grin-beam::before {
    content: "\f582"
}

.fa-face-grin-beam-sweat::before {
    content: "\f583"
}

.fa-grin-beam-sweat::before {
    content: "\f583"
}

.fa-face-grin-hearts::before {
    content: "\f584"
}

.fa-grin-hearts::before {
    content: "\f584"
}

.fa-face-grin-squint::before {
    content: "\f585"
}

.fa-grin-squint::before {
    content: "\f585"
}

.fa-face-grin-squint-tears::before {
    content: "\f586"
}

.fa-grin-squint-tears::before {
    content: "\f586"
}

.fa-face-grin-stars::before {
    content: "\f587"
}

.fa-grin-stars::before {
    content: "\f587"
}

.fa-face-grin-tears::before {
    content: "\f588"
}

.fa-grin-tears::before {
    content: "\f588"
}

.fa-face-grin-tongue::before {
    content: "\f589"
}

.fa-grin-tongue::before {
    content: "\f589"
}

.fa-face-grin-tongue-squint::before {
    content: "\f58a"
}

.fa-grin-tongue-squint::before {
    content: "\f58a"
}

.fa-face-grin-tongue-wink::before {
    content: "\f58b"
}

.fa-grin-tongue-wink::before {
    content: "\f58b"
}

.fa-face-grin-wide::before {
    content: "\f581"
}

.fa-grin-alt::before {
    content: "\f581"
}

.fa-face-grin-wink::before {
    content: "\f58c"
}

.fa-grin-wink::before {
    content: "\f58c"
}

.fa-face-kiss::before {
    content: "\f596"
}

.fa-kiss::before {
    content: "\f596"
}

.fa-face-kiss-beam::before {
    content: "\f597"
}

.fa-kiss-beam::before {
    content: "\f597"
}

.fa-face-kiss-wink-heart::before {
    content: "\f598"
}

.fa-kiss-wink-heart::before {
    content: "\f598"
}

.fa-face-laugh::before {
    content: "\f599"
}

.fa-laugh::before {
    content: "\f599"
}

.fa-face-laugh-beam::before {
    content: "\f59a"
}

.fa-laugh-beam::before {
    content: "\f59a"
}

.fa-face-laugh-squint::before {
    content: "\f59b"
}

.fa-laugh-squint::before {
    content: "\f59b"
}

.fa-face-laugh-wink::before {
    content: "\f59c"
}

.fa-laugh-wink::before {
    content: "\f59c"
}

.fa-face-meh::before {
    content: "\f11a"
}

.fa-meh::before {
    content: "\f11a"
}

.fa-face-meh-blank::before {
    content: "\f5a4"
}

.fa-meh-blank::before {
    content: "\f5a4"
}

.fa-face-rolling-eyes::before {
    content: "\f5a5"
}

.fa-meh-rolling-eyes::before {
    content: "\f5a5"
}

.fa-face-sad-cry::before {
    content: "\f5b3"
}

.fa-sad-cry::before {
    content: "\f5b3"
}

.fa-face-sad-tear::before {
    content: "\f5b4"
}

.fa-sad-tear::before {
    content: "\f5b4"
}

.fa-face-smile::before {
    content: "\f118"
}

.fa-smile::before {
    content: "\f118"
}

.fa-face-smile-beam::before {
    content: "\f5b8"
}

.fa-smile-beam::before {
    content: "\f5b8"
}

.fa-face-smile-wink::before {
    content: "\f4da"
}

.fa-smile-wink::before {
    content: "\f4da"
}

.fa-face-surprise::before {
    content: "\f5c2"
}

.fa-surprise::before {
    content: "\f5c2"
}

.fa-face-tired::before {
    content: "\f5c8"
}

.fa-tired::before {
    content: "\f5c8"
}

.fa-fan::before {
    content: "\f863"
}

.fa-faucet::before {
    content: "\e005"
}

.fa-faucet-drip::before {
    content: "\e006"
}

.fa-fax::before {
    content: "\f1ac"
}

.fa-feather::before {
    content: "\f52d"
}

.fa-feather-pointed::before {
    content: "\f56b"
}

.fa-feather-alt::before {
    content: "\f56b"
}

.fa-ferry::before {
    content: "\e4ea"
}

.fa-file::before {
    content: "\f15b"
}

.fa-file-arrow-down::before {
    content: "\f56d"
}

.fa-file-download::before {
    content: "\f56d"
}

.fa-file-arrow-up::before {
    content: "\f574"
}

.fa-file-upload::before {
    content: "\f574"
}

.fa-file-audio::before {
    content: "\f1c7"
}

.fa-file-circle-check::before {
    content: "\e493"
}

.fa-file-circle-exclamation::before {
    content: "\e4eb"
}

.fa-file-circle-minus::before {
    content: "\e4ed"
}

.fa-file-circle-plus::before {
    content: "\e4ee"
}

.fa-file-circle-question::before {
    content: "\e4ef"
}

.fa-file-circle-xmark::before {
    content: "\e494"
}

.fa-file-code::before {
    content: "\f1c9"
}

.fa-file-contract::before {
    content: "\f56c"
}

.fa-file-csv::before {
    content: "\f6dd"
}

.fa-file-excel::before {
    content: "\f1c3"
}

.fa-file-export::before {
    content: "\f56e"
}

.fa-arrow-right-from-file::before {
    content: "\f56e"
}

.fa-file-image::before {
    content: "\f1c5"
}

.fa-file-import::before {
    content: "\f56f"
}

.fa-arrow-right-to-file::before {
    content: "\f56f"
}

.fa-file-invoice::before {
    content: "\f570"
}

.fa-file-invoice-dollar::before {
    content: "\f571"
}

.fa-file-lines::before {
    content: "\f15c"
}

.fa-file-alt::before {
    content: "\f15c"
}

.fa-file-text::before {
    content: "\f15c"
}

.fa-file-medical::before {
    content: "\f477"
}

.fa-file-pdf::before {
    content: "\f1c1"
}

.fa-file-pen::before {
    content: "\f31c"
}

.fa-file-edit::before {
    content: "\f31c"
}

.fa-file-powerpoint::before {
    content: "\f1c4"
}

.fa-file-prescription::before {
    content: "\f572"
}

.fa-file-shield::before {
    content: "\e4f0"
}

.fa-file-signature::before {
    content: "\f573"
}

.fa-file-video::before {
    content: "\f1c8"
}

.fa-file-waveform::before {
    content: "\f478"
}

.fa-file-medical-alt::before {
    content: "\f478"
}

.fa-file-word::before {
    content: "\f1c2"
}

.fa-file-zipper::before {
    content: "\f1c6"
}

.fa-file-archive::before {
    content: "\f1c6"
}

.fa-fill::before {
    content: "\f575"
}

.fa-fill-drip::before {
    content: "\f576"
}

.fa-film::before {
    content: "\f008"
}

.fa-filter::before {
    content: "\f0b0"
}

.fa-filter-circle-dollar::before {
    content: "\f662"
}

.fa-funnel-dollar::before {
    content: "\f662"
}

.fa-filter-circle-xmark::before {
    content: "\e17b"
}

.fa-fingerprint::before {
    content: "\f577"
}

.fa-fire::before {
    content: "\f06d"
}

.fa-fire-burner::before {
    content: "\e4f1"
}

.fa-fire-extinguisher::before {
    content: "\f134"
}

.fa-fire-flame-curved::before {
    content: "\f7e4"
}

.fa-fire-alt::before {
    content: "\f7e4"
}

.fa-fire-flame-simple::before {
    content: "\f46a"
}

.fa-burn::before {
    content: "\f46a"
}

.fa-fish::before {
    content: "\f578"
}

.fa-fish-fins::before {
    content: "\e4f2"
}

.fa-flag::before {
    content: "\f024"
}

.fa-flag-checkered::before {
    content: "\f11e"
}

.fa-flag-usa::before {
    content: "\f74d"
}

.fa-flask::before {
    content: "\f0c3"
}

.fa-flask-vial::before {
    content: "\e4f3"
}

.fa-floppy-disk::before {
    content: "\f0c7"
}

.fa-save::before {
    content: "\f0c7"
}

.fa-florin-sign::before {
    content: "\e184"
}

.fa-folder::before {
    content: "\f07b"
}

.fa-folder-blank::before {
    content: "\f07b"
}

.fa-folder-closed::before {
    content: "\e185"
}

.fa-folder-minus::before {
    content: "\f65d"
}

.fa-folder-open::before {
    content: "\f07c"
}

.fa-folder-plus::before {
    content: "\f65e"
}

.fa-folder-tree::before {
    content: "\f802"
}

.fa-font::before {
    content: "\f031"
}

.fa-football::before {
    content: "\f44e"
}

.fa-football-ball::before {
    content: "\f44e"
}

.fa-forward::before {
    content: "\f04e"
}

.fa-forward-fast::before {
    content: "\f050"
}

.fa-fast-forward::before {
    content: "\f050"
}

.fa-forward-step::before {
    content: "\f051"
}

.fa-step-forward::before {
    content: "\f051"
}

.fa-franc-sign::before {
    content: "\e18f"
}

.fa-frog::before {
    content: "\f52e"
}

.fa-futbol::before {
    content: "\f1e3"
}

.fa-futbol-ball::before {
    content: "\f1e3"
}

.fa-soccer-ball::before {
    content: "\f1e3"
}

.fa-g::before {
    content: "G"
}

.fa-gamepad::before {
    content: "\f11b"
}

.fa-gas-pump::before {
    content: "\f52f"
}

.fa-gauge::before {
    content: "\f624"
}

.fa-dashboard::before {
    content: "\f624"
}

.fa-gauge-med::before {
    content: "\f624"
}

.fa-tachometer-alt-average::before {
    content: "\f624"
}

.fa-gauge-high::before {
    content: "\f625"
}

.fa-tachometer-alt::before {
    content: "\f625"
}

.fa-tachometer-alt-fast::before {
    content: "\f625"
}

.fa-gauge-simple::before {
    content: "\f629"
}

.fa-gauge-simple-med::before {
    content: "\f629"
}

.fa-tachometer-average::before {
    content: "\f629"
}

.fa-gauge-simple-high::before {
    content: "\f62a"
}

.fa-tachometer::before {
    content: "\f62a"
}

.fa-tachometer-fast::before {
    content: "\f62a"
}

.fa-gavel::before {
    content: "\f0e3"
}

.fa-legal::before {
    content: "\f0e3"
}

.fa-gear::before {
    content: "\f013"
}

.fa-cog::before {
    content: "\f013"
}

.fa-gears::before {
    content: "\f085"
}

.fa-cogs::before {
    content: "\f085"
}

.fa-gem::before {
    content: "\f3a5"
}

.fa-genderless::before {
    content: "\f22d"
}

.fa-ghost::before {
    content: "\f6e2"
}

.fa-gift::before {
    content: "\f06b"
}

.fa-gifts::before {
    content: "\f79c"
}

.fa-glass-water::before {
    content: "\e4f4"
}

.fa-glass-water-droplet::before {
    content: "\e4f5"
}

.fa-glasses::before {
    content: "\f530"
}

.fa-globe::before {
    content: "\f0ac"
}

.fa-golf-ball-tee::before {
    content: "\f450"
}

.fa-golf-ball::before {
    content: "\f450"
}

.fa-gopuram::before {
    content: "\f664"
}

.fa-graduation-cap::before {
    content: "\f19d"
}

.fa-mortar-board::before {
    content: "\f19d"
}

.fa-greater-than::before {
    content: ">"
}

.fa-greater-than-equal::before {
    content: "\f532"
}

.fa-grip::before {
    content: "\f58d"
}

.fa-grip-horizontal::before {
    content: "\f58d"
}

.fa-grip-lines::before {
    content: "\f7a4"
}

.fa-grip-lines-vertical::before {
    content: "\f7a5"
}

.fa-grip-vertical::before {
    content: "\f58e"
}

.fa-group-arrows-rotate::before {
    content: "\e4f6"
}

.fa-guarani-sign::before {
    content: "\e19a"
}

.fa-guitar::before {
    content: "\f7a6"
}

.fa-gun::before {
    content: "\e19b"
}

.fa-h::before {
    content: "H"
}

.fa-hammer::before {
    content: "\f6e3"
}

.fa-hamsa::before {
    content: "\f665"
}

.fa-hand::before {
    content: "\f256"
}

.fa-hand-paper::before {
    content: "\f256"
}

.fa-hand-back-fist::before {
    content: "\f255"
}

.fa-hand-rock::before {
    content: "\f255"
}

.fa-hand-dots::before {
    content: "\f461"
}

.fa-allergies::before {
    content: "\f461"
}

.fa-hand-fist::before {
    content: "\f6de"
}

.fa-fist-raised::before {
    content: "\f6de"
}

.fa-hand-holding::before {
    content: "\f4bd"
}

.fa-hand-holding-dollar::before {
    content: "\f4c0"
}

.fa-hand-holding-usd::before {
    content: "\f4c0"
}

.fa-hand-holding-droplet::before {
    content: "\f4c1"
}

.fa-hand-holding-water::before {
    content: "\f4c1"
}

.fa-hand-holding-hand::before {
    content: "\e4f7"
}

.fa-hand-holding-heart::before {
    content: "\f4be"
}

.fa-hand-holding-medical::before {
    content: "\e05c"
}

.fa-hand-lizard::before {
    content: "\f258"
}

.fa-hand-middle-finger::before {
    content: "\f806"
}

.fa-hand-peace::before {
    content: "\f25b"
}

.fa-hand-point-down::before {
    content: "\f0a7"
}

.fa-hand-point-left::before {
    content: "\f0a5"
}

.fa-hand-point-right::before {
    content: "\f0a4"
}

.fa-hand-point-up::before {
    content: "\f0a6"
}

.fa-hand-pointer::before {
    content: "\f25a"
}

.fa-hand-scissors::before {
    content: "\f257"
}

.fa-hand-sparkles::before {
    content: "\e05d"
}

.fa-hand-spock::before {
    content: "\f259"
}

.fa-handcuffs::before {
    content: "\e4f8"
}

.fa-hands::before {
    content: "\f2a7"
}

.fa-sign-language::before {
    content: "\f2a7"
}

.fa-signing::before {
    content: "\f2a7"
}

.fa-hands-asl-interpreting::before {
    content: "\f2a3"
}

.fa-american-sign-language-interpreting::before {
    content: "\f2a3"
}

.fa-asl-interpreting::before {
    content: "\f2a3"
}

.fa-hands-american-sign-language-interpreting::before {
    content: "\f2a3"
}

.fa-hands-bound::before {
    content: "\e4f9"
}

.fa-hands-bubbles::before {
    content: "\e05e"
}

.fa-hands-wash::before {
    content: "\e05e"
}

.fa-hands-clapping::before {
    content: "\e1a8"
}

.fa-hands-holding::before {
    content: "\f4c2"
}

.fa-hands-holding-child::before {
    content: "\e4fa"
}

.fa-hands-holding-circle::before {
    content: "\e4fb"
}

.fa-hands-praying::before {
    content: "\f684"
}

.fa-praying-hands::before {
    content: "\f684"
}

.fa-handshake::before {
    content: "\f2b5"
}

.fa-handshake-angle::before {
    content: "\f4c4"
}

.fa-hands-helping::before {
    content: "\f4c4"
}

.fa-handshake-simple::before {
    content: "\f4c6"
}

.fa-handshake-alt::before {
    content: "\f4c6"
}

.fa-handshake-simple-slash::before {
    content: "\e05f"
}

.fa-handshake-alt-slash::before {
    content: "\e05f"
}

.fa-handshake-slash::before {
    content: "\e060"
}

.fa-hanukiah::before {
    content: "\f6e6"
}

.fa-hard-drive::before {
    content: "\f0a0"
}

.fa-hdd::before {
    content: "\f0a0"
}

.fa-hashtag::before {
    content: "#"
}

.fa-hat-cowboy::before {
    content: "\f8c0"
}

.fa-hat-cowboy-side::before {
    content: "\f8c1"
}

.fa-hat-wizard::before {
    content: "\f6e8"
}

.fa-head-side-cough::before {
    content: "\e061"
}

.fa-head-side-cough-slash::before {
    content: "\e062"
}

.fa-head-side-mask::before {
    content: "\e063"
}

.fa-head-side-virus::before {
    content: "\e064"
}

.fa-heading::before {
    content: "\f1dc"
}

.fa-header::before {
    content: "\f1dc"
}

.fa-headphones::before {
    content: "\f025"
}

.fa-headphones-simple::before {
    content: "\f58f"
}

.fa-headphones-alt::before {
    content: "\f58f"
}

.fa-headset::before {
    content: "\f590"
}

.fa-heart::before {
    content: "\f004"
}

.fa-heart-circle-bolt::before {
    content: "\e4fc"
}

.fa-heart-circle-check::before {
    content: "\e4fd"
}

.fa-heart-circle-exclamation::before {
    content: "\e4fe"
}

.fa-heart-circle-minus::before {
    content: "\e4ff"
}

.fa-heart-circle-plus::before {
    content: "\e500"
}

.fa-heart-circle-xmark::before {
    content: "\e501"
}

.fa-heart-crack::before {
    content: "\f7a9"
}

.fa-heart-broken::before {
    content: "\f7a9"
}

.fa-heart-pulse::before {
    content: "\f21e"
}

.fa-heartbeat::before {
    content: "\f21e"
}

.fa-helicopter::before {
    content: "\f533"
}

.fa-helicopter-symbol::before {
    content: "\e502"
}

.fa-helmet-safety::before {
    content: "\f807"
}

.fa-hard-hat::before {
    content: "\f807"
}

.fa-hat-hard::before {
    content: "\f807"
}

.fa-helmet-un::before {
    content: "\e503"
}

.fa-highlighter::before {
    content: "\f591"
}

.fa-hill-avalanche::before {
    content: "\e507"
}

.fa-hill-rockslide::before {
    content: "\e508"
}

.fa-hippo::before {
    content: "\f6ed"
}

.fa-hockey-puck::before {
    content: "\f453"
}

.fa-holly-berry::before {
    content: "\f7aa"
}

.fa-horse::before {
    content: "\f6f0"
}

.fa-horse-head::before {
    content: "\f7ab"
}

.fa-hospital::before {
    content: "\f0f8"
}

.fa-hospital-alt::before {
    content: "\f0f8"
}

.fa-hospital-wide::before {
    content: "\f0f8"
}

.fa-hospital-user::before {
    content: "\f80d"
}

.fa-hot-tub-person::before {
    content: "\f593"
}

.fa-hot-tub::before {
    content: "\f593"
}

.fa-hotdog::before {
    content: "\f80f"
}

.fa-hotel::before {
    content: "\f594"
}

.fa-hourglass::before {
    content: "\f254"
}

.fa-hourglass-2::before {
    content: "\f254"
}

.fa-hourglass-half::before {
    content: "\f254"
}

.fa-hourglass-empty::before {
    content: "\f252"
}

.fa-hourglass-end::before {
    content: "\f253"
}

.fa-hourglass-3::before {
    content: "\f253"
}

.fa-hourglass-start::before {
    content: "\f251"
}

.fa-hourglass-1::before {
    content: "\f251"
}

.fa-house::before {
    content: "\f015"
}

.fa-home::before {
    content: "\f015"
}

.fa-home-alt::before {
    content: "\f015"
}

.fa-home-lg-alt::before {
    content: "\f015"
}

.fa-house-chimney::before {
    content: "\e3af"
}

.fa-home-lg::before {
    content: "\e3af"
}

.fa-house-chimney-crack::before {
    content: "\f6f1"
}

.fa-house-damage::before {
    content: "\f6f1"
}

.fa-house-chimney-medical::before {
    content: "\f7f2"
}

.fa-clinic-medical::before {
    content: "\f7f2"
}

.fa-house-chimney-user::before {
    content: "\e065"
}

.fa-house-chimney-window::before {
    content: "\e00d"
}

.fa-house-circle-check::before {
    content: "\e509"
}

.fa-house-circle-exclamation::before {
    content: "\e50a"
}

.fa-house-circle-xmark::before {
    content: "\e50b"
}

.fa-house-crack::before {
    content: "\e3b1"
}

.fa-house-fire::before {
    content: "\e50c"
}

.fa-house-flag::before {
    content: "\e50d"
}

.fa-house-flood-water::before {
    content: "\e50e"
}

.fa-house-flood-water-circle-arrow-right::before {
    content: "\e50f"
}

.fa-house-laptop::before {
    content: "\e066"
}

.fa-laptop-house::before {
    content: "\e066"
}

.fa-house-lock::before {
    content: "\e510"
}

.fa-house-medical::before {
    content: "\e3b2"
}

.fa-house-medical-circle-check::before {
    content: "\e511"
}

.fa-house-medical-circle-exclamation::before {
    content: "\e512"
}

.fa-house-medical-circle-xmark::before {
    content: "\e513"
}

.fa-house-medical-flag::before {
    content: "\e514"
}

.fa-house-signal::before {
    content: "\e012"
}

.fa-house-tsunami::before {
    content: "\e515"
}

.fa-house-user::before {
    content: "\e1b0"
}

.fa-home-user::before {
    content: "\e1b0"
}

.fa-hryvnia-sign::before {
    content: "\f6f2"
}

.fa-hryvnia::before {
    content: "\f6f2"
}

.fa-hurricane::before {
    content: "\f751"
}

.fa-i::before {
    content: "I"
}

.fa-i-cursor::before {
    content: "\f246"
}

.fa-ice-cream::before {
    content: "\f810"
}

.fa-icicles::before {
    content: "\f7ad"
}

.fa-icons::before {
    content: "\f86d"
}

.fa-heart-music-camera-bolt::before {
    content: "\f86d"
}

.fa-id-badge::before {
    content: "\f2c1"
}

.fa-id-card::before {
    content: "\f2c2"
}

.fa-drivers-license::before {
    content: "\f2c2"
}

.fa-id-card-clip::before {
    content: "\f47f"
}

.fa-id-card-alt::before {
    content: "\f47f"
}

.fa-igloo::before {
    content: "\f7ae"
}

.fa-image::before {
    content: "\f03e"
}

.fa-image-portrait::before {
    content: "\f3e0"
}

.fa-portrait::before {
    content: "\f3e0"
}

.fa-images::before {
    content: "\f302"
}

.fa-inbox::before {
    content: "\f01c"
}

.fa-indent::before {
    content: "\f03c"
}

.fa-indian-rupee-sign::before {
    content: "\e1bc"
}

.fa-indian-rupee::before {
    content: "\e1bc"
}

.fa-inr::before {
    content: "\e1bc"
}

.fa-industry::before {
    content: "\f275"
}

.fa-infinity::before {
    content: "\f534"
}

.fa-info::before {
    content: "\f129"
}

.fa-italic::before {
    content: "\f033"
}

.fa-j::before {
    content: "J"
}

.fa-jar::before {
    content: "\e516"
}

.fa-jar-wheat::before {
    content: "\e517"
}

.fa-jedi::before {
    content: "\f669"
}

.fa-jet-fighter::before {
    content: "\f0fb"
}

.fa-fighter-jet::before {
    content: "\f0fb"
}

.fa-jet-fighter-up::before {
    content: "\e518"
}

.fa-joint::before {
    content: "\f595"
}

.fa-jug-detergent::before {
    content: "\e519"
}

.fa-k::before {
    content: "K"
}

.fa-kaaba::before {
    content: "\f66b"
}

.fa-key::before {
    content: "\f084"
}

.fa-keyboard::before {
    content: "\f11c"
}

.fa-khanda::before {
    content: "\f66d"
}

.fa-kip-sign::before {
    content: "\e1c4"
}

.fa-kit-medical::before {
    content: "\f479"
}

.fa-first-aid::before {
    content: "\f479"
}

.fa-kitchen-set::before {
    content: "\e51a"
}

.fa-kiwi-bird::before {
    content: "\f535"
}

.fa-l::before {
    content: "L"
}

.fa-land-mine-on::before {
    content: "\e51b"
}

.fa-landmark::before {
    content: "\f66f"
}

.fa-landmark-dome::before {
    content: "\f752"
}

.fa-landmark-alt::before {
    content: "\f752"
}

.fa-landmark-flag::before {
    content: "\e51c"
}

.fa-language::before {
    content: "\f1ab"
}

.fa-laptop::before {
    content: "\f109"
}

.fa-laptop-code::before {
    content: "\f5fc"
}

.fa-laptop-file::before {
    content: "\e51d"
}

.fa-laptop-medical::before {
    content: "\f812"
}

.fa-lari-sign::before {
    content: "\e1c8"
}

.fa-layer-group::before {
    content: "\f5fd"
}

.fa-leaf::before {
    content: "\f06c"
}

.fa-left-long::before {
    content: "\f30a"
}

.fa-long-arrow-alt-left::before {
    content: "\f30a"
}

.fa-left-right::before {
    content: "\f337"
}

.fa-arrows-alt-h::before {
    content: "\f337"
}

.fa-lemon::before {
    content: "\f094"
}

.fa-less-than::before {
    content: "<"
}

.fa-less-than-equal::before {
    content: "\f537"
}

.fa-life-ring::before {
    content: "\f1cd"
}

.fa-lightbulb::before {
    content: "\f0eb"
}

.fa-lines-leaning::before {
    content: "\e51e"
}

.fa-link::before {
    content: "\f0c1"
}

.fa-chain::before {
    content: "\f0c1"
}

.fa-link-slash::before {
    content: "\f127"
}

.fa-chain-broken::before {
    content: "\f127"
}

.fa-chain-slash::before {
    content: "\f127"
}

.fa-unlink::before {
    content: "\f127"
}

.fa-lira-sign::before {
    content: "\f195"
}

.fa-list::before {
    content: "\f03a"
}

.fa-list-squares::before {
    content: "\f03a"
}

.fa-list-check::before {
    content: "\f0ae"
}

.fa-tasks::before {
    content: "\f0ae"
}

.fa-list-ol::before {
    content: "\f0cb"
}

.fa-list-1-2::before {
    content: "\f0cb"
}

.fa-list-numeric::before {
    content: "\f0cb"
}

.fa-list-ul::before {
    content: "\f0ca"
}

.fa-list-dots::before {
    content: "\f0ca"
}

.fa-litecoin-sign::before {
    content: "\e1d3"
}

.fa-location-arrow::before {
    content: "\f124"
}

.fa-location-crosshairs::before {
    content: "\f601"
}

.fa-location::before {
    content: "\f601"
}

.fa-location-dot::before {
    content: "\f3c5"
}

.fa-map-marker-alt::before {
    content: "\f3c5"
}

.fa-location-pin::before {
    content: "\f041"
}

.fa-map-marker::before {
    content: "\f041"
}

.fa-location-pin-lock::before {
    content: "\e51f"
}

.fa-lock::before {
    content: "\f023"
}

.fa-lock-open::before {
    content: "\f3c1"
}

.fa-locust::before {
    content: "\e520"
}

.fa-lungs::before {
    content: "\f604"
}

.fa-lungs-virus::before {
    content: "\e067"
}

.fa-m::before {
    content: "M"
}

.fa-magnet::before {
    content: "\f076"
}

.fa-magnifying-glass::before {
    content: "\f002"
}

.fa-search::before {
    content: "\f002"
}

.fa-magnifying-glass-arrow-right::before {
    content: "\e521"
}

.fa-magnifying-glass-chart::before {
    content: "\e522"
}

.fa-magnifying-glass-dollar::before {
    content: "\f688"
}

.fa-search-dollar::before {
    content: "\f688"
}

.fa-magnifying-glass-location::before {
    content: "\f689"
}

.fa-search-location::before {
    content: "\f689"
}

.fa-magnifying-glass-minus::before {
    content: "\f010"
}

.fa-search-minus::before {
    content: "\f010"
}

.fa-magnifying-glass-plus::before {
    content: "\f00e"
}

.fa-search-plus::before {
    content: "\f00e"
}

.fa-manat-sign::before {
    content: "\e1d5"
}

.fa-map::before {
    content: "\f279"
}

.fa-map-location::before {
    content: "\f59f"
}

.fa-map-marked::before {
    content: "\f59f"
}

.fa-map-location-dot::before {
    content: "\f5a0"
}

.fa-map-marked-alt::before {
    content: "\f5a0"
}

.fa-map-pin::before {
    content: "\f276"
}

.fa-marker::before {
    content: "\f5a1"
}

.fa-mars::before {
    content: "\f222"
}

.fa-mars-and-venus::before {
    content: "\f224"
}

.fa-mars-and-venus-burst::before {
    content: "\e523"
}

.fa-mars-double::before {
    content: "\f227"
}

.fa-mars-stroke::before {
    content: "\f229"
}

.fa-mars-stroke-right::before {
    content: "\f22b"
}

.fa-mars-stroke-h::before {
    content: "\f22b"
}

.fa-mars-stroke-up::before {
    content: "\f22a"
}

.fa-mars-stroke-v::before {
    content: "\f22a"
}

.fa-martini-glass::before {
    content: "\f57b"
}

.fa-glass-martini-alt::before {
    content: "\f57b"
}

.fa-martini-glass-citrus::before {
    content: "\f561"
}

.fa-cocktail::before {
    content: "\f561"
}

.fa-martini-glass-empty::before {
    content: "\f000"
}

.fa-glass-martini::before {
    content: "\f000"
}

.fa-mask::before {
    content: "\f6fa"
}

.fa-mask-face::before {
    content: "\e1d7"
}

.fa-mask-ventilator::before {
    content: "\e524"
}

.fa-masks-theater::before {
    content: "\f630"
}

.fa-theater-masks::before {
    content: "\f630"
}

.fa-mattress-pillow::before {
    content: "\e525"
}

.fa-maximize::before {
    content: "\f31e"
}

.fa-expand-arrows-alt::before {
    content: "\f31e"
}

.fa-medal::before {
    content: "\f5a2"
}

.fa-memory::before {
    content: "\f538"
}

.fa-menorah::before {
    content: "\f676"
}

.fa-mercury::before {
    content: "\f223"
}

.fa-message::before {
    content: "\f27a"
}

.fa-comment-alt::before {
    content: "\f27a"
}

.fa-meteor::before {
    content: "\f753"
}

.fa-microchip::before {
    content: "\f2db"
}

.fa-microphone::before {
    content: "\f130"
}

.fa-microphone-lines::before {
    content: "\f3c9"
}

.fa-microphone-alt::before {
    content: "\f3c9"
}

.fa-microphone-lines-slash::before {
    content: "\f539"
}

.fa-microphone-alt-slash::before {
    content: "\f539"
}

.fa-microphone-slash::before {
    content: "\f131"
}

.fa-microscope::before {
    content: "\f610"
}

.fa-mill-sign::before {
    content: "\e1ed"
}

.fa-minimize::before {
    content: "\f78c"
}

.fa-compress-arrows-alt::before {
    content: "\f78c"
}

.fa-minus::before {
    content: "\f068"
}

.fa-subtract::before {
    content: "\f068"
}

.fa-mitten::before {
    content: "\f7b5"
}

.fa-mobile::before {
    content: "\f3ce"
}

.fa-mobile-android::before {
    content: "\f3ce"
}

.fa-mobile-phone::before {
    content: "\f3ce"
}

.fa-mobile-button::before {
    content: "\f10b"
}

.fa-mobile-retro::before {
    content: "\e527"
}

.fa-mobile-screen::before {
    content: "\f3cf"
}

.fa-mobile-android-alt::before {
    content: "\f3cf"
}

.fa-mobile-screen-button::before {
    content: "\f3cd"
}

.fa-mobile-alt::before {
    content: "\f3cd"
}

.fa-money-bill::before {
    content: "\f0d6"
}

.fa-money-bill-1::before {
    content: "\f3d1"
}

.fa-money-bill-alt::before {
    content: "\f3d1"
}

.fa-money-bill-1-wave::before {
    content: "\f53b"
}

.fa-money-bill-wave-alt::before {
    content: "\f53b"
}

.fa-money-bill-transfer::before {
    content: "\e528"
}

.fa-money-bill-trend-up::before {
    content: "\e529"
}

.fa-money-bill-wave::before {
    content: "\f53a"
}

.fa-money-bill-wheat::before {
    content: "\e52a"
}

.fa-money-bills::before {
    content: "\e1f3"
}

.fa-money-check::before {
    content: "\f53c"
}

.fa-money-check-dollar::before {
    content: "\f53d"
}

.fa-money-check-alt::before {
    content: "\f53d"
}

.fa-monument::before {
    content: "\f5a6"
}

.fa-moon::before {
    content: "\f186"
}

.fa-mortar-pestle::before {
    content: "\f5a7"
}

.fa-mosque::before {
    content: "\f678"
}

.fa-mosquito::before {
    content: "\e52b"
}

.fa-mosquito-net::before {
    content: "\e52c"
}

.fa-motorcycle::before {
    content: "\f21c"
}

.fa-mound::before {
    content: "\e52d"
}

.fa-mountain::before {
    content: "\f6fc"
}

.fa-mountain-city::before {
    content: "\e52e"
}

.fa-mountain-sun::before {
    content: "\e52f"
}

.fa-mug-hot::before {
    content: "\f7b6"
}

.fa-mug-saucer::before {
    content: "\f0f4"
}

.fa-coffee::before {
    content: "\f0f4"
}

.fa-music::before {
    content: "\f001"
}

.fa-n::before {
    content: "N"
}

.fa-naira-sign::before {
    content: "\e1f6"
}

.fa-network-wired::before {
    content: "\f6ff"
}

.fa-neuter::before {
    content: "\f22c"
}

.fa-newspaper::before {
    content: "\f1ea"
}

.fa-not-equal::before {
    content: "\f53e"
}

.fa-note-sticky::before {
    content: "\f249"
}

.fa-sticky-note::before {
    content: "\f249"
}

.fa-notes-medical::before {
    content: "\f481"
}

.fa-o::before {
    content: "O"
}

.fa-object-group::before {
    content: "\f247"
}

.fa-object-ungroup::before {
    content: "\f248"
}

.fa-oil-can::before {
    content: "\f613"
}

.fa-oil-well::before {
    content: "\e532"
}

.fa-om::before {
    content: "\f679"
}

.fa-otter::before {
    content: "\f700"
}

.fa-outdent::before {
    content: "\f03b"
}

.fa-dedent::before {
    content: "\f03b"
}

.fa-p::before {
    content: "P"
}

.fa-pager::before {
    content: "\f815"
}

.fa-paint-roller::before {
    content: "\f5aa"
}

.fa-paintbrush::before {
    content: "\f1fc"
}

.fa-paint-brush::before {
    content: "\f1fc"
}

.fa-palette::before {
    content: "\f53f"
}

.fa-pallet::before {
    content: "\f482"
}

.fa-panorama::before {
    content: "\e209"
}

.fa-paper-plane::before {
    content: "\f1d8"
}

.fa-paperclip::before {
    content: "\f0c6"
}

.fa-parachute-box::before {
    content: "\f4cd"
}

.fa-paragraph::before {
    content: "\f1dd"
}

.fa-passport::before {
    content: "\f5ab"
}

.fa-paste::before {
    content: "\f0ea"
}

.fa-file-clipboard::before {
    content: "\f0ea"
}

.fa-pause::before {
    content: "\f04c"
}

.fa-paw::before {
    content: "\f1b0"
}

.fa-peace::before {
    content: "\f67c"
}

.fa-pen::before {
    content: "\f304"
}

.fa-pen-clip::before {
    content: "\f305"
}

.fa-pen-alt::before {
    content: "\f305"
}

.fa-pen-fancy::before {
    content: "\f5ac"
}

.fa-pen-nib::before {
    content: "\f5ad"
}

.fa-pen-ruler::before {
    content: "\f5ae"
}

.fa-pencil-ruler::before {
    content: "\f5ae"
}

.fa-pen-to-square::before {
    content: "\f044"
}

.fa-edit::before {
    content: "\f044"
}

.fa-pencil::before {
    content: "\f303"
}

.fa-pencil-alt::before {
    content: "\f303"
}

.fa-people-arrows-left-right::before {
    content: "\e068"
}

.fa-people-arrows::before {
    content: "\e068"
}

.fa-people-carry-box::before {
    content: "\f4ce"
}

.fa-people-carry::before {
    content: "\f4ce"
}

.fa-people-group::before {
    content: "\e533"
}

.fa-people-line::before {
    content: "\e534"
}

.fa-people-pulling::before {
    content: "\e535"
}

.fa-people-robbery::before {
    content: "\e536"
}

.fa-people-roof::before {
    content: "\e537"
}

.fa-pepper-hot::before {
    content: "\f816"
}

.fa-percent::before {
    content: "%"
}

.fa-percentage::before {
    content: "%"
}

.fa-person::before {
    content: "\f183"
}

.fa-male::before {
    content: "\f183"
}

.fa-person-arrow-down-to-line::before {
    content: "\e538"
}

.fa-person-arrow-up-from-line::before {
    content: "\e539"
}

.fa-person-biking::before {
    content: "\f84a"
}

.fa-biking::before {
    content: "\f84a"
}

.fa-person-booth::before {
    content: "\f756"
}

.fa-person-breastfeeding::before {
    content: "\e53a"
}

.fa-person-burst::before {
    content: "\e53b"
}

.fa-person-cane::before {
    content: "\e53c"
}

.fa-person-chalkboard::before {
    content: "\e53d"
}

.fa-person-circle-check::before {
    content: "\e53e"
}

.fa-person-circle-exclamation::before {
    content: "\e53f"
}

.fa-person-circle-minus::before {
    content: "\e540"
}

.fa-person-circle-plus::before {
    content: "\e541"
}

.fa-person-circle-question::before {
    content: "\e542"
}

.fa-person-circle-xmark::before {
    content: "\e543"
}

.fa-person-digging::before {
    content: "\f85e"
}

.fa-digging::before {
    content: "\f85e"
}

.fa-person-dots-from-line::before {
    content: "\f470"
}

.fa-diagnoses::before {
    content: "\f470"
}

.fa-person-dress::before {
    content: "\f182"
}

.fa-female::before {
    content: "\f182"
}

.fa-person-dress-burst::before {
    content: "\e544"
}

.fa-person-drowning::before {
    content: "\e545"
}

.fa-person-falling::before {
    content: "\e546"
}

.fa-person-falling-burst::before {
    content: "\e547"
}

.fa-person-half-dress::before {
    content: "\e548"
}

.fa-person-harassing::before {
    content: "\e549"
}

.fa-person-hiking::before {
    content: "\f6ec"
}

.fa-hiking::before {
    content: "\f6ec"
}

.fa-person-military-pointing::before {
    content: "\e54a"
}

.fa-person-military-rifle::before {
    content: "\e54b"
}

.fa-person-military-to-person::before {
    content: "\e54c"
}

.fa-person-praying::before {
    content: "\f683"
}

.fa-pray::before {
    content: "\f683"
}

.fa-person-pregnant::before {
    content: "\e31e"
}

.fa-person-rays::before {
    content: "\e54d"
}

.fa-person-rifle::before {
    content: "\e54e"
}

.fa-person-running::before {
    content: "\f70c"
}

.fa-running::before {
    content: "\f70c"
}

.fa-person-shelter::before {
    content: "\e54f"
}

.fa-person-skating::before {
    content: "\f7c5"
}

.fa-skating::before {
    content: "\f7c5"
}

.fa-person-skiing::before {
    content: "\f7c9"
}

.fa-skiing::before {
    content: "\f7c9"
}

.fa-person-skiing-nordic::before {
    content: "\f7ca"
}

.fa-skiing-nordic::before {
    content: "\f7ca"
}

.fa-person-snowboarding::before {
    content: "\f7ce"
}

.fa-snowboarding::before {
    content: "\f7ce"
}

.fa-person-swimming::before {
    content: "\f5c4"
}

.fa-swimmer::before {
    content: "\f5c4"
}

.fa-person-through-window::before {
    content: "\e433"
}

.fa-person-walking::before {
    content: "\f554"
}

.fa-walking::before {
    content: "\f554"
}

.fa-person-walking-arrow-loop-left::before {
    content: "\e551"
}

.fa-person-walking-arrow-right::before {
    content: "\e552"
}

.fa-person-walking-dashed-line-arrow-right::before {
    content: "\e553"
}

.fa-person-walking-luggage::before {
    content: "\e554"
}

.fa-person-walking-with-cane::before {
    content: "\f29d"
}

.fa-blind::before {
    content: "\f29d"
}

.fa-peseta-sign::before {
    content: "\e221"
}

.fa-peso-sign::before {
    content: "\e222"
}

.fa-phone::before {
    content: "\f095"
}

.fa-phone-flip::before {
    content: "\f879"
}

.fa-phone-alt::before {
    content: "\f879"
}

.fa-phone-slash::before {
    content: "\f3dd"
}

.fa-phone-volume::before {
    content: "\f2a0"
}

.fa-volume-control-phone::before {
    content: "\f2a0"
}

.fa-photo-film::before {
    content: "\f87c"
}

.fa-photo-video::before {
    content: "\f87c"
}

.fa-piggy-bank::before {
    content: "\f4d3"
}

.fa-pills::before {
    content: "\f484"
}

.fa-pizza-slice::before {
    content: "\f818"
}

.fa-place-of-worship::before {
    content: "\f67f"
}

.fa-plane::before {
    content: "\f072"
}

.fa-plane-arrival::before {
    content: "\f5af"
}

.fa-plane-circle-check::before {
    content: "\e555"
}

.fa-plane-circle-exclamation::before {
    content: "\e556"
}

.fa-plane-circle-xmark::before {
    content: "\e557"
}

.fa-plane-departure::before {
    content: "\f5b0"
}

.fa-plane-lock::before {
    content: "\e558"
}

.fa-plane-slash::before {
    content: "\e069"
}

.fa-plane-up::before {
    content: "\e22d"
}

.fa-plant-wilt::before {
    content: "\e43b"
}

.fa-plate-wheat::before {
    content: "\e55a"
}

.fa-play::before {
    content: "\f04b"
}

.fa-plug::before {
    content: "\f1e6"
}

.fa-plug-circle-bolt::before {
    content: "\e55b"
}

.fa-plug-circle-check::before {
    content: "\e55c"
}

.fa-plug-circle-exclamation::before {
    content: "\e55d"
}

.fa-plug-circle-minus::before {
    content: "\e55e"
}

.fa-plug-circle-plus::before {
    content: "\e55f"
}

.fa-plug-circle-xmark::before {
    content: "\e560"
}

.fa-plus::before {
    content: "+"
}

.fa-add::before {
    content: "+"
}

.fa-plus-minus::before {
    content: "\e43c"
}

.fa-podcast::before {
    content: "\f2ce"
}

.fa-poo::before {
    content: "\f2fe"
}

.fa-poo-storm::before {
    content: "\f75a"
}

.fa-poo-bolt::before {
    content: "\f75a"
}

.fa-poop::before {
    content: "\f619"
}

.fa-power-off::before {
    content: "\f011"
}

.fa-prescription::before {
    content: "\f5b1"
}

.fa-prescription-bottle::before {
    content: "\f485"
}

.fa-prescription-bottle-medical::before {
    content: "\f486"
}

.fa-prescription-bottle-alt::before {
    content: "\f486"
}

.fa-print::before {
    content: "\f02f"
}

.fa-pump-medical::before {
    content: "\e06a"
}

.fa-pump-soap::before {
    content: "\e06b"
}

.fa-puzzle-piece::before {
    content: "\f12e"
}

.fa-q::before {
    content: "Q"
}

.fa-qrcode::before {
    content: "\f029"
}

.fa-question::before {
    content: "?"
}

.fa-quote-left::before {
    content: "\f10d"
}

.fa-quote-left-alt::before {
    content: "\f10d"
}

.fa-quote-right::before {
    content: "\f10e"
}

.fa-quote-right-alt::before {
    content: "\f10e"
}

.fa-r::before {
    content: "R"
}

.fa-radiation::before {
    content: "\f7b9"
}

.fa-radio::before {
    content: "\f8d7"
}

.fa-rainbow::before {
    content: "\f75b"
}

.fa-ranking-star::before {
    content: "\e561"
}

.fa-receipt::before {
    content: "\f543"
}

.fa-record-vinyl::before {
    content: "\f8d9"
}

.fa-rectangle-ad::before {
    content: "\f641"
}

.fa-ad::before {
    content: "\f641"
}

.fa-rectangle-list::before {
    content: "\f022"
}

.fa-list-alt::before {
    content: "\f022"
}

.fa-rectangle-xmark::before {
    content: "\f410"
}

.fa-rectangle-times::before {
    content: "\f410"
}

.fa-times-rectangle::before {
    content: "\f410"
}

.fa-window-close::before {
    content: "\f410"
}

.fa-recycle::before {
    content: "\f1b8"
}

.fa-registered::before {
    content: "\f25d"
}

.fa-repeat::before {
    content: "\f363"
}

.fa-reply::before {
    content: "\f3e5"
}

.fa-mail-reply::before {
    content: "\f3e5"
}

.fa-reply-all::before {
    content: "\f122"
}

.fa-mail-reply-all::before {
    content: "\f122"
}

.fa-republican::before {
    content: "\f75e"
}

.fa-restroom::before {
    content: "\f7bd"
}

.fa-retweet::before {
    content: "\f079"
}

.fa-ribbon::before {
    content: "\f4d6"
}

.fa-right-from-bracket::before {
    content: "\f2f5"
}

.fa-sign-out-alt::before {
    content: "\f2f5"
}

.fa-right-left::before {
    content: "\f362"
}

.fa-exchange-alt::before {
    content: "\f362"
}

.fa-right-long::before {
    content: "\f30b"
}

.fa-long-arrow-alt-right::before {
    content: "\f30b"
}

.fa-right-to-bracket::before {
    content: "\f2f6"
}

.fa-sign-in-alt::before {
    content: "\f2f6"
}

.fa-ring::before {
    content: "\f70b"
}

.fa-road::before {
    content: "\f018"
}

.fa-road-barrier::before {
    content: "\e562"
}

.fa-road-bridge::before {
    content: "\e563"
}

.fa-road-circle-check::before {
    content: "\e564"
}

.fa-road-circle-exclamation::before {
    content: "\e565"
}

.fa-road-circle-xmark::before {
    content: "\e566"
}

.fa-road-lock::before {
    content: "\e567"
}

.fa-road-spikes::before {
    content: "\e568"
}

.fa-robot::before {
    content: "\f544"
}

.fa-rocket::before {
    content: "\f135"
}

.fa-rotate::before {
    content: "\f2f1"
}

.fa-sync-alt::before {
    content: "\f2f1"
}

.fa-rotate-left::before {
    content: "\f2ea"
}

.fa-rotate-back::before {
    content: "\f2ea"
}

.fa-rotate-backward::before {
    content: "\f2ea"
}

.fa-undo-alt::before {
    content: "\f2ea"
}

.fa-rotate-right::before {
    content: "\f2f9"
}

.fa-redo-alt::before {
    content: "\f2f9"
}

.fa-rotate-forward::before {
    content: "\f2f9"
}

.fa-route::before {
    content: "\f4d7"
}

.fa-rss::before {
    content: "\f09e"
}

.fa-feed::before {
    content: "\f09e"
}

.fa-ruble-sign::before {
    content: "\f158"
}

.fa-rouble::before {
    content: "\f158"
}

.fa-rub::before {
    content: "\f158"
}

.fa-ruble::before {
    content: "\f158"
}

.fa-rug::before {
    content: "\e569"
}

.fa-ruler::before {
    content: "\f545"
}

.fa-ruler-combined::before {
    content: "\f546"
}

.fa-ruler-horizontal::before {
    content: "\f547"
}

.fa-ruler-vertical::before {
    content: "\f548"
}

.fa-rupee-sign::before {
    content: "\f156"
}

.fa-rupee::before {
    content: "\f156"
}

.fa-rupiah-sign::before {
    content: "\e23d"
}

.fa-s::before {
    content: "S"
}

.fa-sack-dollar::before {
    content: "\f81d"
}

.fa-sack-xmark::before {
    content: "\e56a"
}

.fa-sailboat::before {
    content: "\e445"
}

.fa-satellite::before {
    content: "\f7bf"
}

.fa-satellite-dish::before {
    content: "\f7c0"
}

.fa-scale-balanced::before {
    content: "\f24e"
}

.fa-balance-scale::before {
    content: "\f24e"
}

.fa-scale-unbalanced::before {
    content: "\f515"
}

.fa-balance-scale-left::before {
    content: "\f515"
}

.fa-scale-unbalanced-flip::before {
    content: "\f516"
}

.fa-balance-scale-right::before {
    content: "\f516"
}

.fa-school::before {
    content: "\f549"
}

.fa-school-circle-check::before {
    content: "\e56b"
}

.fa-school-circle-exclamation::before {
    content: "\e56c"
}

.fa-school-circle-xmark::before {
    content: "\e56d"
}

.fa-school-flag::before {
    content: "\e56e"
}

.fa-school-lock::before {
    content: "\e56f"
}

.fa-scissors::before {
    content: "\f0c4"
}

.fa-cut::before {
    content: "\f0c4"
}

.fa-screwdriver::before {
    content: "\f54a"
}

.fa-screwdriver-wrench::before {
    content: "\f7d9"
}

.fa-tools::before {
    content: "\f7d9"
}

.fa-scroll::before {
    content: "\f70e"
}

.fa-scroll-torah::before {
    content: "\f6a0"
}

.fa-torah::before {
    content: "\f6a0"
}

.fa-sd-card::before {
    content: "\f7c2"
}

.fa-section::before {
    content: "\e447"
}

.fa-seedling::before {
    content: "\f4d8"
}

.fa-sprout::before {
    content: "\f4d8"
}

.fa-server::before {
    content: "\f233"
}

.fa-shapes::before {
    content: "\f61f"
}

.fa-triangle-circle-square::before {
    content: "\f61f"
}

.fa-share::before {
    content: "\f064"
}

.fa-arrow-turn-right::before {
    content: "\f064"
}

.fa-mail-forward::before {
    content: "\f064"
}

.fa-share-from-square::before {
    content: "\f14d"
}

.fa-share-square::before {
    content: "\f14d"
}

.fa-share-nodes::before {
    content: "\f1e0"
}

.fa-share-alt::before {
    content: "\f1e0"
}

.fa-sheet-plastic::before {
    content: "\e571"
}

.fa-shekel-sign::before {
    content: "\f20b"
}

.fa-ils::before {
    content: "\f20b"
}

.fa-shekel::before {
    content: "\f20b"
}

.fa-sheqel::before {
    content: "\f20b"
}

.fa-sheqel-sign::before {
    content: "\f20b"
}

.fa-shield::before {
    content: "\f132"
}

.fa-shield-blank::before {
    content: "\f132"
}

.fa-shield-cat::before {
    content: "\e572"
}

.fa-shield-dog::before {
    content: "\e573"
}

.fa-shield-halved::before {
    content: "\f3ed"
}

.fa-shield-alt::before {
    content: "\f3ed"
}

.fa-shield-heart::before {
    content: "\e574"
}

.fa-shield-virus::before {
    content: "\e06c"
}

.fa-ship::before {
    content: "\f21a"
}

.fa-shirt::before {
    content: "\f553"
}

.fa-t-shirt::before {
    content: "\f553"
}

.fa-tshirt::before {
    content: "\f553"
}

.fa-shoe-prints::before {
    content: "\f54b"
}

.fa-shop::before {
    content: "\f54f"
}

.fa-store-alt::before {
    content: "\f54f"
}

.fa-shop-lock::before {
    content: "\e4a5"
}

.fa-shop-slash::before {
    content: "\e070"
}

.fa-store-alt-slash::before {
    content: "\e070"
}

.fa-shower::before {
    content: "\f2cc"
}

.fa-shrimp::before {
    content: "\e448"
}

.fa-shuffle::before {
    content: "\f074"
}

.fa-random::before {
    content: "\f074"
}

.fa-shuttle-space::before {
    content: "\f197"
}

.fa-space-shuttle::before {
    content: "\f197"
}

.fa-sign-hanging::before {
    content: "\f4d9"
}

.fa-sign::before {
    content: "\f4d9"
}

.fa-signal::before {
    content: "\f012"
}

.fa-signal-5::before {
    content: "\f012"
}

.fa-signal-perfect::before {
    content: "\f012"
}

.fa-signature::before {
    content: "\f5b7"
}

.fa-signs-post::before {
    content: "\f277"
}

.fa-map-signs::before {
    content: "\f277"
}

.fa-sim-card::before {
    content: "\f7c4"
}

.fa-sink::before {
    content: "\e06d"
}

.fa-sitemap::before {
    content: "\f0e8"
}

.fa-skull::before {
    content: "\f54c"
}

.fa-skull-crossbones::before {
    content: "\f714"
}

.fa-slash::before {
    content: "\f715"
}

.fa-sleigh::before {
    content: "\f7cc"
}

.fa-sliders::before {
    content: "\f1de"
}

.fa-sliders-h::before {
    content: "\f1de"
}

.fa-smog::before {
    content: "\f75f"
}

.fa-smoking::before {
    content: "\f48d"
}

.fa-snowflake::before {
    content: "\f2dc"
}

.fa-snowman::before {
    content: "\f7d0"
}

.fa-snowplow::before {
    content: "\f7d2"
}

.fa-soap::before {
    content: "\e06e"
}

.fa-socks::before {
    content: "\f696"
}

.fa-solar-panel::before {
    content: "\f5ba"
}

.fa-sort::before {
    content: "\f0dc"
}

.fa-unsorted::before {
    content: "\f0dc"
}

.fa-sort-down::before {
    content: "\f0dd"
}

.fa-sort-desc::before {
    content: "\f0dd"
}

.fa-sort-up::before {
    content: "\f0de"
}

.fa-sort-asc::before {
    content: "\f0de"
}

.fa-spa::before {
    content: "\f5bb"
}

.fa-spaghetti-monster-flying::before {
    content: "\f67b"
}

.fa-pastafarianism::before {
    content: "\f67b"
}

.fa-spell-check::before {
    content: "\f891"
}

.fa-spider::before {
    content: "\f717"
}

.fa-spinner::before {
    content: "\f110"
}

.fa-splotch::before {
    content: "\f5bc"
}

.fa-spoon::before {
    content: "\f2e5"
}

.fa-utensil-spoon::before {
    content: "\f2e5"
}

.fa-spray-can::before {
    content: "\f5bd"
}

.fa-spray-can-sparkles::before {
    content: "\f5d0"
}

.fa-air-freshener::before {
    content: "\f5d0"
}

.fa-square::before {
    content: "\f0c8"
}

.fa-square-arrow-up-right::before {
    content: "\f14c"
}

.fa-external-link-square::before {
    content: "\f14c"
}

.fa-square-caret-down::before {
    content: "\f150"
}

.fa-caret-square-down::before {
    content: "\f150"
}

.fa-square-caret-left::before {
    content: "\f191"
}

.fa-caret-square-left::before {
    content: "\f191"
}

.fa-square-caret-right::before {
    content: "\f152"
}

.fa-caret-square-right::before {
    content: "\f152"
}

.fa-square-caret-up::before {
    content: "\f151"
}

.fa-caret-square-up::before {
    content: "\f151"
}

.fa-square-check::before {
    content: "\f14a"
}

.fa-check-square::before {
    content: "\f14a"
}

.fa-square-envelope::before {
    content: "\f199"
}

.fa-envelope-square::before {
    content: "\f199"
}

.fa-square-full::before {
    content: "\f45c"
}

.fa-square-h::before {
    content: "\f0fd"
}

.fa-h-square::before {
    content: "\f0fd"
}

.fa-square-minus::before {
    content: "\f146"
}

.fa-minus-square::before {
    content: "\f146"
}

.fa-square-nfi::before {
    content: "\e576"
}

.fa-square-parking::before {
    content: "\f540"
}

.fa-parking::before {
    content: "\f540"
}

.fa-square-pen::before {
    content: "\f14b"
}

.fa-pen-square::before {
    content: "\f14b"
}

.fa-pencil-square::before {
    content: "\f14b"
}

.fa-square-person-confined::before {
    content: "\e577"
}

.fa-square-phone::before {
    content: "\f098"
}

.fa-phone-square::before {
    content: "\f098"
}

.fa-square-phone-flip::before {
    content: "\f87b"
}

.fa-phone-square-alt::before {
    content: "\f87b"
}

.fa-square-plus::before {
    content: "\f0fe"
}

.fa-plus-square::before {
    content: "\f0fe"
}

.fa-square-poll-horizontal::before {
    content: "\f682"
}

.fa-poll-h::before {
    content: "\f682"
}

.fa-square-poll-vertical::before {
    content: "\f681"
}

.fa-poll::before {
    content: "\f681"
}

.fa-square-root-variable::before {
    content: "\f698"
}

.fa-square-root-alt::before {
    content: "\f698"
}

.fa-square-rss::before {
    content: "\f143"
}

.fa-rss-square::before {
    content: "\f143"
}

.fa-square-share-nodes::before {
    content: "\f1e1"
}

.fa-share-alt-square::before {
    content: "\f1e1"
}

.fa-square-up-right::before {
    content: "\f360"
}

.fa-external-link-square-alt::before {
    content: "\f360"
}

.fa-square-virus::before {
    content: "\e578"
}

.fa-square-xmark::before {
    content: "\f2d3"
}

.fa-times-square::before {
    content: "\f2d3"
}

.fa-xmark-square::before {
    content: "\f2d3"
}

.fa-staff-aesculapius::before {
    content: "\e579"
}

.fa-rod-asclepius::before {
    content: "\e579"
}

.fa-rod-snake::before {
    content: "\e579"
}

.fa-staff-snake::before {
    content: "\e579"
}

.fa-stairs::before {
    content: "\e289"
}

.fa-stamp::before {
    content: "\f5bf"
}

.fa-star::before {
    content: "\f005"
}

.fa-star-and-crescent::before {
    content: "\f699"
}

.fa-star-half::before {
    content: "\f089"
}

.fa-star-half-stroke::before {
    content: "\f5c0"
}

.fa-star-half-alt::before {
    content: "\f5c0"
}

.fa-star-of-david::before {
    content: "\f69a"
}

.fa-star-of-life::before {
    content: "\f621"
}

.fa-sterling-sign::before {
    content: "\f154"
}

.fa-gbp::before {
    content: "\f154"
}

.fa-pound-sign::before {
    content: "\f154"
}

.fa-stethoscope::before {
    content: "\f0f1"
}

.fa-stop::before {
    content: "\f04d"
}

.fa-stopwatch::before {
    content: "\f2f2"
}

.fa-stopwatch-20::before {
    content: "\e06f"
}

.fa-store::before {
    content: "\f54e"
}

.fa-store-slash::before {
    content: "\e071"
}

.fa-street-view::before {
    content: "\f21d"
}

.fa-strikethrough::before {
    content: "\f0cc"
}

.fa-stroopwafel::before {
    content: "\f551"
}

.fa-subscript::before {
    content: "\f12c"
}

.fa-suitcase::before {
    content: "\f0f2"
}

.fa-suitcase-medical::before {
    content: "\f0fa"
}

.fa-medkit::before {
    content: "\f0fa"
}

.fa-suitcase-rolling::before {
    content: "\f5c1"
}

.fa-sun::before {
    content: "\f185"
}

.fa-sun-plant-wilt::before {
    content: "\e57a"
}

.fa-superscript::before {
    content: "\f12b"
}

.fa-swatchbook::before {
    content: "\f5c3"
}

.fa-synagogue::before {
    content: "\f69b"
}

.fa-syringe::before {
    content: "\f48e"
}

.fa-t::before {
    content: "T"
}

.fa-table::before {
    content: "\f0ce"
}

.fa-table-cells::before {
    content: "\f00a"
}

.fa-th::before {
    content: "\f00a"
}

.fa-table-cells-large::before {
    content: "\f009"
}

.fa-th-large::before {
    content: "\f009"
}

.fa-table-columns::before {
    content: "\f0db"
}

.fa-columns::before {
    content: "\f0db"
}

.fa-table-list::before {
    content: "\f00b"
}

.fa-th-list::before {
    content: "\f00b"
}

.fa-table-tennis-paddle-ball::before {
    content: "\f45d"
}

.fa-ping-pong-paddle-ball::before {
    content: "\f45d"
}

.fa-table-tennis::before {
    content: "\f45d"
}

.fa-tablet::before {
    content: "\f3fb"
}

.fa-tablet-android::before {
    content: "\f3fb"
}

.fa-tablet-button::before {
    content: "\f10a"
}

.fa-tablet-screen-button::before {
    content: "\f3fa"
}

.fa-tablet-alt::before {
    content: "\f3fa"
}

.fa-tablets::before {
    content: "\f490"
}

.fa-tachograph-digital::before {
    content: "\f566"
}

.fa-digital-tachograph::before {
    content: "\f566"
}

.fa-tag::before {
    content: "\f02b"
}

.fa-tags::before {
    content: "\f02c"
}

.fa-tape::before {
    content: "\f4db"
}

.fa-tarp::before {
    content: "\e57b"
}

.fa-tarp-droplet::before {
    content: "\e57c"
}

.fa-taxi::before {
    content: "\f1ba"
}

.fa-cab::before {
    content: "\f1ba"
}

.fa-teeth::before {
    content: "\f62e"
}

.fa-teeth-open::before {
    content: "\f62f"
}

.fa-temperature-arrow-down::before {
    content: "\e03f"
}

.fa-temperature-down::before {
    content: "\e03f"
}

.fa-temperature-arrow-up::before {
    content: "\e040"
}

.fa-temperature-up::before {
    content: "\e040"
}

.fa-temperature-empty::before {
    content: "\f2cb"
}

.fa-temperature-0::before {
    content: "\f2cb"
}

.fa-thermometer-0::before {
    content: "\f2cb"
}

.fa-thermometer-empty::before {
    content: "\f2cb"
}

.fa-temperature-full::before {
    content: "\f2c7"
}

.fa-temperature-4::before {
    content: "\f2c7"
}

.fa-thermometer-4::before {
    content: "\f2c7"
}

.fa-thermometer-full::before {
    content: "\f2c7"
}

.fa-temperature-half::before {
    content: "\f2c9"
}

.fa-temperature-2::before {
    content: "\f2c9"
}

.fa-thermometer-2::before {
    content: "\f2c9"
}

.fa-thermometer-half::before {
    content: "\f2c9"
}

.fa-temperature-high::before {
    content: "\f769"
}

.fa-temperature-low::before {
    content: "\f76b"
}

.fa-temperature-quarter::before {
    content: "\f2ca"
}

.fa-temperature-1::before {
    content: "\f2ca"
}

.fa-thermometer-1::before {
    content: "\f2ca"
}

.fa-thermometer-quarter::before {
    content: "\f2ca"
}

.fa-temperature-three-quarters::before {
    content: "\f2c8"
}

.fa-temperature-3::before {
    content: "\f2c8"
}

.fa-thermometer-3::before {
    content: "\f2c8"
}

.fa-thermometer-three-quarters::before {
    content: "\f2c8"
}

.fa-tenge-sign::before {
    content: "\f7d7"
}

.fa-tenge::before {
    content: "\f7d7"
}

.fa-tent::before {
    content: "\e57d"
}

.fa-tent-arrow-down-to-line::before {
    content: "\e57e"
}

.fa-tent-arrow-left-right::before {
    content: "\e57f"
}

.fa-tent-arrow-turn-left::before {
    content: "\e580"
}

.fa-tent-arrows-down::before {
    content: "\e581"
}

.fa-tents::before {
    content: "\e582"
}

.fa-terminal::before {
    content: "\f120"
}

.fa-text-height::before {
    content: "\f034"
}

.fa-text-slash::before {
    content: "\f87d"
}

.fa-remove-format::before {
    content: "\f87d"
}

.fa-text-width::before {
    content: "\f035"
}

.fa-thermometer::before {
    content: "\f491"
}

.fa-thumbs-down::before {
    content: "\f165"
}

.fa-thumbs-up::before {
    content: "\f164"
}

.fa-thumbtack::before {
    content: "\f08d"
}

.fa-thumb-tack::before {
    content: "\f08d"
}

.fa-ticket::before {
    content: "\f145"
}

.fa-ticket-simple::before {
    content: "\f3ff"
}

.fa-ticket-alt::before {
    content: "\f3ff"
}

.fa-timeline::before {
    content: "\e29c"
}

.fa-toggle-off::before {
    content: "\f204"
}

.fa-toggle-on::before {
    content: "\f205"
}

.fa-toilet::before {
    content: "\f7d8"
}

.fa-toilet-paper::before {
    content: "\f71e"
}

.fa-toilet-paper-slash::before {
    content: "\e072"
}

.fa-toilet-portable::before {
    content: "\e583"
}

.fa-toilets-portable::before {
    content: "\e584"
}

.fa-toolbox::before {
    content: "\f552"
}

.fa-tooth::before {
    content: "\f5c9"
}

.fa-torii-gate::before {
    content: "\f6a1"
}

.fa-tornado::before {
    content: "\f76f"
}

.fa-tower-broadcast::before {
    content: "\f519"
}

.fa-broadcast-tower::before {
    content: "\f519"
}

.fa-tower-cell::before {
    content: "\e585"
}

.fa-tower-observation::before {
    content: "\e586"
}

.fa-tractor::before {
    content: "\f722"
}

.fa-trademark::before {
    content: "\f25c"
}

.fa-traffic-light::before {
    content: "\f637"
}

.fa-trailer::before {
    content: "\e041"
}

.fa-train::before {
    content: "\f238"
}

.fa-train-subway::before {
    content: "\f239"
}

.fa-subway::before {
    content: "\f239"
}

.fa-train-tram::before {
    content: "\f7da"
}

.fa-tram::before {
    content: "\f7da"
}

.fa-transgender::before {
    content: "\f225"
}

.fa-transgender-alt::before {
    content: "\f225"
}

.fa-trash::before {
    content: "\f1f8"
}

.fa-trash-arrow-up::before {
    content: "\f829"
}

.fa-trash-restore::before {
    content: "\f829"
}

.fa-trash-can::before {
    content: "\f2ed"
}

.fa-trash-alt::before {
    content: "\f2ed"
}

.fa-trash-can-arrow-up::before {
    content: "\f82a"
}

.fa-trash-restore-alt::before {
    content: "\f82a"
}

.fa-tree::before {
    content: "\f1bb"
}

.fa-tree-city::before {
    content: "\e587"
}

.fa-triangle-exclamation::before {
    content: "\f071"
}

.fa-exclamation-triangle::before {
    content: "\f071"
}

.fa-warning::before {
    content: "\f071"
}

.fa-trophy::before {
    content: "\f091"
}

.fa-trowel::before {
    content: "\e589"
}

.fa-trowel-bricks::before {
    content: "\e58a"
}

.fa-truck::before {
    content: "\f0d1"
}

.fa-truck-arrow-right::before {
    content: "\e58b"
}

.fa-truck-droplet::before {
    content: "\e58c"
}

.fa-truck-fast::before {
    content: "\f48b"
}

.fa-shipping-fast::before {
    content: "\f48b"
}

.fa-truck-field::before {
    content: "\e58d"
}

.fa-truck-field-un::before {
    content: "\e58e"
}

.fa-truck-front::before {
    content: "\e2b7"
}

.fa-truck-medical::before {
    content: "\f0f9"
}

.fa-ambulance::before {
    content: "\f0f9"
}

.fa-truck-monster::before {
    content: "\f63b"
}

.fa-truck-moving::before {
    content: "\f4df"
}

.fa-truck-pickup::before {
    content: "\f63c"
}

.fa-truck-plane::before {
    content: "\e58f"
}

.fa-truck-ramp-box::before {
    content: "\f4de"
}

.fa-truck-loading::before {
    content: "\f4de"
}

.fa-tty::before {
    content: "\f1e4"
}

.fa-teletype::before {
    content: "\f1e4"
}

.fa-turkish-lira-sign::before {
    content: "\e2bb"
}

.fa-try::before {
    content: "\e2bb"
}

.fa-turkish-lira::before {
    content: "\e2bb"
}

.fa-turn-down::before {
    content: "\f3be"
}

.fa-level-down-alt::before {
    content: "\f3be"
}

.fa-turn-up::before {
    content: "\f3bf"
}

.fa-level-up-alt::before {
    content: "\f3bf"
}

.fa-tv::before {
    content: "\f26c"
}

.fa-television::before {
    content: "\f26c"
}

.fa-tv-alt::before {
    content: "\f26c"
}

.fa-u::before {
    content: "U"
}

.fa-umbrella::before {
    content: "\f0e9"
}

.fa-umbrella-beach::before {
    content: "\f5ca"
}

.fa-underline::before {
    content: "\f0cd"
}

.fa-universal-access::before {
    content: "\f29a"
}

.fa-unlock::before {
    content: "\f09c"
}

.fa-unlock-keyhole::before {
    content: "\f13e"
}

.fa-unlock-alt::before {
    content: "\f13e"
}

.fa-up-down::before {
    content: "\f338"
}

.fa-arrows-alt-v::before {
    content: "\f338"
}

.fa-up-down-left-right::before {
    content: "\f0b2"
}

.fa-arrows-alt::before {
    content: "\f0b2"
}

.fa-up-long::before {
    content: "\f30c"
}

.fa-long-arrow-alt-up::before {
    content: "\f30c"
}

.fa-up-right-and-down-left-from-center::before {
    content: "\f424"
}

.fa-expand-alt::before {
    content: "\f424"
}

.fa-up-right-from-square::before {
    content: "\f35d"
}

.fa-external-link-alt::before {
    content: "\f35d"
}

.fa-upload::before {
    content: "\f093"
}

.fa-user::before {
    content: "\f007"
}

.fa-user-astronaut::before {
    content: "\f4fb"
}

.fa-user-check::before {
    content: "\f4fc"
}

.fa-user-clock::before {
    content: "\f4fd"
}

.fa-user-doctor::before {
    content: "\f0f0"
}

.fa-user-md::before {
    content: "\f0f0"
}

.fa-user-gear::before {
    content: "\f4fe"
}

.fa-user-cog::before {
    content: "\f4fe"
}

.fa-user-graduate::before {
    content: "\f501"
}

.fa-user-group::before {
    content: "\f500"
}

.fa-user-friends::before {
    content: "\f500"
}

.fa-user-injured::before {
    content: "\f728"
}

.fa-user-large::before {
    content: "\f406"
}

.fa-user-alt::before {
    content: "\f406"
}

.fa-user-large-slash::before {
    content: "\f4fa"
}

.fa-user-alt-slash::before {
    content: "\f4fa"
}

.fa-user-lock::before {
    content: "\f502"
}

.fa-user-minus::before {
    content: "\f503"
}

.fa-user-ninja::before {
    content: "\f504"
}

.fa-user-nurse::before {
    content: "\f82f"
}

.fa-user-pen::before {
    content: "\f4ff"
}

.fa-user-edit::before {
    content: "\f4ff"
}

.fa-user-plus::before {
    content: "\f234"
}

.fa-user-secret::before {
    content: "\f21b"
}

.fa-user-shield::before {
    content: "\f505"
}

.fa-user-slash::before {
    content: "\f506"
}

.fa-user-tag::before {
    content: "\f507"
}

.fa-user-tie::before {
    content: "\f508"
}

.fa-user-xmark::before {
    content: "\f235"
}

.fa-user-times::before {
    content: "\f235"
}

.fa-users::before {
    content: "\f0c0"
}

.fa-users-between-lines::before {
    content: "\e591"
}

.fa-users-gear::before {
    content: "\f509"
}

.fa-users-cog::before {
    content: "\f509"
}

.fa-users-line::before {
    content: "\e592"
}

.fa-users-rays::before {
    content: "\e593"
}

.fa-users-rectangle::before {
    content: "\e594"
}

.fa-users-slash::before {
    content: "\e073"
}

.fa-users-viewfinder::before {
    content: "\e595"
}

.fa-utensils::before {
    content: "\f2e7"
}

.fa-cutlery::before {
    content: "\f2e7"
}

.fa-v::before {
    content: "V"
}

.fa-van-shuttle::before {
    content: "\f5b6"
}

.fa-shuttle-van::before {
    content: "\f5b6"
}

.fa-vault::before {
    content: "\e2c5"
}

.fa-vector-square::before {
    content: "\f5cb"
}

.fa-venus::before {
    content: "\f221"
}

.fa-venus-double::before {
    content: "\f226"
}

.fa-venus-mars::before {
    content: "\f228"
}

.fa-vest::before {
    content: "\e085"
}

.fa-vest-patches::before {
    content: "\e086"
}

.fa-vial::before {
    content: "\f492"
}

.fa-vial-circle-check::before {
    content: "\e596"
}

.fa-vial-virus::before {
    content: "\e597"
}

.fa-vials::before {
    content: "\f493"
}

.fa-video::before {
    content: "\f03d"
}

.fa-video-camera::before {
    content: "\f03d"
}

.fa-video-slash::before {
    content: "\f4e2"
}

.fa-vihara::before {
    content: "\f6a7"
}

.fa-virus::before {
    content: "\e074"
}

.fa-virus-covid::before {
    content: "\e4a8"
}

.fa-virus-covid-slash::before {
    content: "\e4a9"
}

.fa-virus-slash::before {
    content: "\e075"
}

.fa-viruses::before {
    content: "\e076"
}

.fa-voicemail::before {
    content: "\f897"
}

.fa-volcano::before {
    content: "\f770"
}

.fa-volleyball::before {
    content: "\f45f"
}

.fa-volleyball-ball::before {
    content: "\f45f"
}

.fa-volume-high::before {
    content: "\f028"
}

.fa-volume-up::before {
    content: "\f028"
}

.fa-volume-low::before {
    content: "\f027"
}

.fa-volume-down::before {
    content: "\f027"
}

.fa-volume-off::before {
    content: "\f026"
}

.fa-volume-xmark::before {
    content: "\f6a9"
}

.fa-volume-mute::before {
    content: "\f6a9"
}

.fa-volume-times::before {
    content: "\f6a9"
}

.fa-vr-cardboard::before {
    content: "\f729"
}

.fa-w::before {
    content: "W"
}

.fa-walkie-talkie::before {
    content: "\f8ef"
}

.fa-wallet::before {
    content: "\f555"
}

.fa-wand-magic::before {
    content: "\f0d0"
}

.fa-magic::before {
    content: "\f0d0"
}

.fa-wand-magic-sparkles::before {
    content: "\e2ca"
}

.fa-magic-wand-sparkles::before {
    content: "\e2ca"
}

.fa-wand-sparkles::before {
    content: "\f72b"
}

.fa-warehouse::before {
    content: "\f494"
}

.fa-water::before {
    content: "\f773"
}

.fa-water-ladder::before {
    content: "\f5c5"
}

.fa-ladder-water::before {
    content: "\f5c5"
}

.fa-swimming-pool::before {
    content: "\f5c5"
}

.fa-wave-square::before {
    content: "\f83e"
}

.fa-weight-hanging::before {
    content: "\f5cd"
}

.fa-weight-scale::before {
    content: "\f496"
}

.fa-weight::before {
    content: "\f496"
}

.fa-wheat-awn::before {
    content: "\e2cd"
}

.fa-wheat-alt::before {
    content: "\e2cd"
}

.fa-wheat-awn-circle-exclamation::before {
    content: "\e598"
}

.fa-wheelchair::before {
    content: "\f193"
}

.fa-wheelchair-move::before {
    content: "\e2ce"
}

.fa-wheelchair-alt::before {
    content: "\e2ce"
}

.fa-whiskey-glass::before {
    content: "\f7a0"
}

.fa-glass-whiskey::before {
    content: "\f7a0"
}

.fa-wifi::before {
    content: "\f1eb"
}

.fa-wifi-3::before {
    content: "\f1eb"
}

.fa-wifi-strong::before {
    content: "\f1eb"
}

.fa-wind::before {
    content: "\f72e"
}

.fa-window-maximize::before {
    content: "\f2d0"
}

.fa-window-minimize::before {
    content: "\f2d1"
}

.fa-window-restore::before {
    content: "\f2d2"
}

.fa-wine-bottle::before {
    content: "\f72f"
}

.fa-wine-glass::before {
    content: "\f4e3"
}

.fa-wine-glass-empty::before {
    content: "\f5ce"
}

.fa-wine-glass-alt::before {
    content: "\f5ce"
}

.fa-won-sign::before {
    content: "\f159"
}

.fa-krw::before {
    content: "\f159"
}

.fa-won::before {
    content: "\f159"
}

.fa-worm::before {
    content: "\e599"
}

.fa-wrench::before {
    content: "\f0ad"
}

.fa-x::before {
    content: "X"
}

.fa-x-ray::before {
    content: "\f497"
}

.fa-xmark::before {
    content: "\f00d"
}

.fa-close::before {
    content: "\f00d"
}

.fa-multiply::before {
    content: "\f00d"
}

.fa-remove::before {
    content: "\f00d"
}

.fa-times::before {
    content: "\f00d"
}

.fa-xmarks-lines::before {
    content: "\e59a"
}

.fa-y::before {
    content: "Y"
}

.fa-yen-sign::before {
    content: "\f157"
}

.fa-cny::before {
    content: "\f157"
}

.fa-jpy::before {
    content: "\f157"
}

.fa-rmb::before {
    content: "\f157"
}

.fa-yen::before {
    content: "\f157"
}

.fa-yin-yang::before {
    content: "\f6ad"
}

.fa-z::before {
    content: "Z"
}

.fa-sr-only,
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0
}

.fa-sr-only-focusable:not(:focus),
.sr-only-focusable:not(:focus) {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0
}

/*!
 * Font Awesome Free 6.1.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2022 Fonticons, Inc.
 */
:host,
:root {
    --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free"
}

@font-face {
    font-family: "Font Awesome 6 Free";
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: url(../../fonts/fontawesome/fa-regular-400.woff2) format("woff2"), url(../../fonts/fontawesome/fa-regular-400.ttf) format("truetype")
}

.fa-regular,
.far {
    font-family: "Font Awesome 6 Free";
    font-weight: 400
}

/*!
 * Font Awesome Free 6.1.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2022 Fonticons, Inc.
 */
:host,
:root {
    --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free"
}

@font-face {
    font-family: "Font Awesome 6 Free";
    font-style: normal;
    font-weight: 900;
    font-display: block;
    src: url(../../fonts/fontawesome/fa-solid-900.woff2) format("woff2"), url(../../fonts/fontawesome/fa-solid-900.ttf) format("truetype")
}

.fa-solid,
.fas {
    font-family: "Font Awesome 6 Free";
    font-weight: 900
}

/*!
 * Font Awesome Free 6.1.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2022 Fonticons, Inc.
 */
:host,
:root {
    --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands"
}

@font-face {
    font-family: "Font Awesome 6 Brands";
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: url(../../fonts/fontawesome/fa-brands-400.woff2) format("woff2"), url(../../fonts/fontawesome/fa-brands-400.ttf) format("truetype")
}

.fa-brands,
.fab {
    font-family: "Font Awesome 6 Brands";
    font-weight: 400
}

.fa-42-group:before {
    content: "\e080"
}

.fa-innosoft:before {
    content: "\e080"
}

.fa-500px:before {
    content: "\f26e"
}

.fa-accessible-icon:before {
    content: "\f368"
}

.fa-accusoft:before {
    content: "\f369"
}

.fa-adn:before {
    content: "\f170"
}

.fa-adversal:before {
    content: "\f36a"
}

.fa-affiliatetheme:before {
    content: "\f36b"
}

.fa-airbnb:before {
    content: "\f834"
}

.fa-algolia:before {
    content: "\f36c"
}

.fa-alipay:before {
    content: "\f642"
}

.fa-amazon:before {
    content: "\f270"
}

.fa-amazon-pay:before {
    content: "\f42c"
}

.fa-amilia:before {
    content: "\f36d"
}

.fa-android:before {
    content: "\f17b"
}

.fa-angellist:before {
    content: "\f209"
}

.fa-angrycreative:before {
    content: "\f36e"
}

.fa-angular:before {
    content: "\f420"
}

.fa-app-store:before {
    content: "\f36f"
}

.fa-app-store-ios:before {
    content: "\f370"
}

.fa-apper:before {
    content: "\f371"
}

.fa-apple:before {
    content: "\f179"
}

.fa-apple-pay:before {
    content: "\f415"
}

.fa-artstation:before {
    content: "\f77a"
}

.fa-asymmetrik:before {
    content: "\f372"
}

.fa-atlassian:before {
    content: "\f77b"
}

.fa-audible:before {
    content: "\f373"
}

.fa-autoprefixer:before {
    content: "\f41c"
}

.fa-avianex:before {
    content: "\f374"
}

.fa-aviato:before {
    content: "\f421"
}

.fa-aws:before {
    content: "\f375"
}

.fa-bandcamp:before {
    content: "\f2d5"
}

.fa-battle-net:before {
    content: "\f835"
}

.fa-behance:before {
    content: "\f1b4"
}

.fa-behance-square:before {
    content: "\f1b5"
}

.fa-bilibili:before {
    content: "\e3d9"
}

.fa-bimobject:before {
    content: "\f378"
}

.fa-bitbucket:before {
    content: "\f171"
}

.fa-bitcoin:before {
    content: "\f379"
}

.fa-bity:before {
    content: "\f37a"
}

.fa-black-tie:before {
    content: "\f27e"
}

.fa-blackberry:before {
    content: "\f37b"
}

.fa-blogger:before {
    content: "\f37c"
}

.fa-blogger-b:before {
    content: "\f37d"
}

.fa-bluetooth:before {
    content: "\f293"
}

.fa-bluetooth-b:before {
    content: "\f294"
}

.fa-bootstrap:before {
    content: "\f836"
}

.fa-bots:before {
    content: "\e340"
}

.fa-btc:before {
    content: "\f15a"
}

.fa-buffer:before {
    content: "\f837"
}

.fa-buromobelexperte:before {
    content: "\f37f"
}

.fa-buy-n-large:before {
    content: "\f8a6"
}

.fa-buysellads:before {
    content: "\f20d"
}

.fa-canadian-maple-leaf:before {
    content: "\f785"
}

.fa-cc-amazon-pay:before {
    content: "\f42d"
}

.fa-cc-amex:before {
    content: "\f1f3"
}

.fa-cc-apple-pay:before {
    content: "\f416"
}

.fa-cc-diners-club:before {
    content: "\f24c"
}

.fa-cc-discover:before {
    content: "\f1f2"
}

.fa-cc-jcb:before {
    content: "\f24b"
}

.fa-cc-mastercard:before {
    content: "\f1f1"
}

.fa-cc-paypal:before {
    content: "\f1f4"
}

.fa-cc-stripe:before {
    content: "\f1f5"
}

.fa-cc-visa:before {
    content: "\f1f0"
}

.fa-centercode:before {
    content: "\f380"
}

.fa-centos:before {
    content: "\f789"
}

.fa-chrome:before {
    content: "\f268"
}

.fa-chromecast:before {
    content: "\f838"
}

.fa-cloudflare:before {
    content: "\e07d"
}

.fa-cloudscale:before {
    content: "\f383"
}

.fa-cloudsmith:before {
    content: "\f384"
}

.fa-cloudversify:before {
    content: "\f385"
}

.fa-cmplid:before {
    content: "\e360"
}

.fa-codepen:before {
    content: "\f1cb"
}

.fa-codiepie:before {
    content: "\f284"
}

.fa-confluence:before {
    content: "\f78d"
}

.fa-connectdevelop:before {
    content: "\f20e"
}

.fa-contao:before {
    content: "\f26d"
}

.fa-cotton-bureau:before {
    content: "\f89e"
}

.fa-cpanel:before {
    content: "\f388"
}

.fa-creative-commons:before {
    content: "\f25e"
}

.fa-creative-commons-by:before {
    content: "\f4e7"
}

.fa-creative-commons-nc:before {
    content: "\f4e8"
}

.fa-creative-commons-nc-eu:before {
    content: "\f4e9"
}

.fa-creative-commons-nc-jp:before {
    content: "\f4ea"
}

.fa-creative-commons-nd:before {
    content: "\f4eb"
}

.fa-creative-commons-pd:before {
    content: "\f4ec"
}

.fa-creative-commons-pd-alt:before {
    content: "\f4ed"
}

.fa-creative-commons-remix:before {
    content: "\f4ee"
}

.fa-creative-commons-sa:before {
    content: "\f4ef"
}

.fa-creative-commons-sampling:before {
    content: "\f4f0"
}

.fa-creative-commons-sampling-plus:before {
    content: "\f4f1"
}

.fa-creative-commons-share:before {
    content: "\f4f2"
}

.fa-creative-commons-zero:before {
    content: "\f4f3"
}

.fa-critical-role:before {
    content: "\f6c9"
}

.fa-css3:before {
    content: "\f13c"
}

.fa-css3-alt:before {
    content: "\f38b"
}

.fa-cuttlefish:before {
    content: "\f38c"
}

.fa-d-and-d:before {
    content: "\f38d"
}

.fa-d-and-d-beyond:before {
    content: "\f6ca"
}

.fa-dailymotion:before {
    content: "\e052"
}

.fa-dashcube:before {
    content: "\f210"
}

.fa-deezer:before {
    content: "\e077"
}

.fa-delicious:before {
    content: "\f1a5"
}

.fa-deploydog:before {
    content: "\f38e"
}

.fa-deskpro:before {
    content: "\f38f"
}

.fa-dev:before {
    content: "\f6cc"
}

.fa-deviantart:before {
    content: "\f1bd"
}

.fa-dhl:before {
    content: "\f790"
}

.fa-diaspora:before {
    content: "\f791"
}

.fa-digg:before {
    content: "\f1a6"
}

.fa-digital-ocean:before {
    content: "\f391"
}

.fa-discord:before {
    content: "\f392"
}

.fa-discourse:before {
    content: "\f393"
}

.fa-dochub:before {
    content: "\f394"
}

.fa-docker:before {
    content: "\f395"
}

.fa-draft2digital:before {
    content: "\f396"
}

.fa-dribbble:before {
    content: "\f17d"
}

.fa-dribbble-square:before {
    content: "\f397"
}

.fa-dropbox:before {
    content: "\f16b"
}

.fa-drupal:before {
    content: "\f1a9"
}

.fa-dyalog:before {
    content: "\f399"
}

.fa-earlybirds:before {
    content: "\f39a"
}

.fa-ebay:before {
    content: "\f4f4"
}

.fa-edge:before {
    content: "\f282"
}

.fa-edge-legacy:before {
    content: "\e078"
}

.fa-elementor:before {
    content: "\f430"
}

.fa-ello:before {
    content: "\f5f1"
}

.fa-ember:before {
    content: "\f423"
}

.fa-empire:before {
    content: "\f1d1"
}

.fa-envira:before {
    content: "\f299"
}

.fa-erlang:before {
    content: "\f39d"
}

.fa-ethereum:before {
    content: "\f42e"
}

.fa-etsy:before {
    content: "\f2d7"
}

.fa-evernote:before {
    content: "\f839"
}

.fa-expeditedssl:before {
    content: "\f23e"
}

.fa-facebook:before {
    content: "\f09a"
}

.fa-facebook-f:before {
    content: "\f39e"
}

.fa-facebook-messenger:before {
    content: "\f39f"
}

.fa-facebook-square:before {
    content: "\f082"
}

.fa-fantasy-flight-games:before {
    content: "\f6dc"
}

.fa-fedex:before {
    content: "\f797"
}

.fa-fedora:before {
    content: "\f798"
}

.fa-figma:before {
    content: "\f799"
}

.fa-firefox:before {
    content: "\f269"
}

.fa-firefox-browser:before {
    content: "\e007"
}

.fa-first-order:before {
    content: "\f2b0"
}

.fa-first-order-alt:before {
    content: "\f50a"
}

.fa-firstdraft:before {
    content: "\f3a1"
}

.fa-flickr:before {
    content: "\f16e"
}

.fa-flipboard:before {
    content: "\f44d"
}

.fa-fly:before {
    content: "\f417"
}

.fa-font-awesome:before {
    content: "\f2b4"
}

.fa-font-awesome-flag:before {
    content: "\f2b4"
}

.fa-font-awesome-logo-full:before {
    content: "\f2b4"
}

.fa-fonticons:before {
    content: "\f280"
}

.fa-fonticons-fi:before {
    content: "\f3a2"
}

.fa-fort-awesome:before {
    content: "\f286"
}

.fa-fort-awesome-alt:before {
    content: "\f3a3"
}

.fa-forumbee:before {
    content: "\f211"
}

.fa-foursquare:before {
    content: "\f180"
}

.fa-free-code-camp:before {
    content: "\f2c5"
}

.fa-freebsd:before {
    content: "\f3a4"
}

.fa-fulcrum:before {
    content: "\f50b"
}

.fa-galactic-republic:before {
    content: "\f50c"
}

.fa-galactic-senate:before {
    content: "\f50d"
}

.fa-get-pocket:before {
    content: "\f265"
}

.fa-gg:before {
    content: "\f260"
}

.fa-gg-circle:before {
    content: "\f261"
}

.fa-git:before {
    content: "\f1d3"
}

.fa-git-alt:before {
    content: "\f841"
}

.fa-git-square:before {
    content: "\f1d2"
}

.fa-github:before {
    content: "\f09b"
}

.fa-github-alt:before {
    content: "\f113"
}

.fa-github-square:before {
    content: "\f092"
}

.fa-gitkraken:before {
    content: "\f3a6"
}

.fa-gitlab:before {
    content: "\f296"
}

.fa-gitter:before {
    content: "\f426"
}

.fa-glide:before {
    content: "\f2a5"
}

.fa-glide-g:before {
    content: "\f2a6"
}

.fa-gofore:before {
    content: "\f3a7"
}

.fa-golang:before {
    content: "\e40f"
}

.fa-goodreads:before {
    content: "\f3a8"
}

.fa-goodreads-g:before {
    content: "\f3a9"
}

.fa-google:before {
    content: "\f1a0"
}

.fa-google-drive:before {
    content: "\f3aa"
}

.fa-google-pay:before {
    content: "\e079"
}

.fa-google-play:before {
    content: "\f3ab"
}

.fa-google-plus:before {
    content: "\f2b3"
}

.fa-google-plus-g:before {
    content: "\f0d5"
}

.fa-google-plus-square:before {
    content: "\f0d4"
}

.fa-google-wallet:before {
    content: "\f1ee"
}

.fa-gratipay:before {
    content: "\f184"
}

.fa-grav:before {
    content: "\f2d6"
}

.fa-gripfire:before {
    content: "\f3ac"
}

.fa-grunt:before {
    content: "\f3ad"
}

.fa-guilded:before {
    content: "\e07e"
}

.fa-gulp:before {
    content: "\f3ae"
}

.fa-hacker-news:before {
    content: "\f1d4"
}

.fa-hacker-news-square:before {
    content: "\f3af"
}

.fa-hackerrank:before {
    content: "\f5f7"
}

.fa-hashnode:before {
    content: "\e499"
}

.fa-hips:before {
    content: "\f452"
}

.fa-hire-a-helper:before {
    content: "\f3b0"
}

.fa-hive:before {
    content: "\e07f"
}

.fa-hooli:before {
    content: "\f427"
}

.fa-hornbill:before {
    content: "\f592"
}

.fa-hotjar:before {
    content: "\f3b1"
}

.fa-houzz:before {
    content: "\f27c"
}

.fa-html5:before {
    content: "\f13b"
}

.fa-hubspot:before {
    content: "\f3b2"
}

.fa-ideal:before {
    content: "\e013"
}

.fa-imdb:before {
    content: "\f2d8"
}

.fa-instagram:before {
    content: "\f16d"
}

.fa-instagram-square:before {
    content: "\e055"
}

.fa-instalod:before {
    content: "\e081"
}

.fa-intercom:before {
    content: "\f7af"
}

.fa-internet-explorer:before {
    content: "\f26b"
}

.fa-invision:before {
    content: "\f7b0"
}

.fa-ioxhost:before {
    content: "\f208"
}

.fa-itch-io:before {
    content: "\f83a"
}

.fa-itunes:before {
    content: "\f3b4"
}

.fa-itunes-note:before {
    content: "\f3b5"
}

.fa-java:before {
    content: "\f4e4"
}

.fa-jedi-order:before {
    content: "\f50e"
}

.fa-jenkins:before {
    content: "\f3b6"
}

.fa-jira:before {
    content: "\f7b1"
}

.fa-joget:before {
    content: "\f3b7"
}

.fa-joomla:before {
    content: "\f1aa"
}

.fa-js:before {
    content: "\f3b8"
}

.fa-js-square:before {
    content: "\f3b9"
}

.fa-jsfiddle:before {
    content: "\f1cc"
}

.fa-kaggle:before {
    content: "\f5fa"
}

.fa-keybase:before {
    content: "\f4f5"
}

.fa-keycdn:before {
    content: "\f3ba"
}

.fa-kickstarter:before {
    content: "\f3bb"
}

.fa-kickstarter-k:before {
    content: "\f3bc"
}

.fa-korvue:before {
    content: "\f42f"
}

.fa-laravel:before {
    content: "\f3bd"
}

.fa-lastfm:before {
    content: "\f202"
}

.fa-lastfm-square:before {
    content: "\f203"
}

.fa-leanpub:before {
    content: "\f212"
}

.fa-less:before {
    content: "\f41d"
}

.fa-line:before {
    content: "\f3c0"
}

.fa-linkedin:before {
    content: "\f08c"
}

.fa-linkedin-in:before {
    content: "\f0e1"
}

.fa-linode:before {
    content: "\f2b8"
}

.fa-linux:before {
    content: "\f17c"
}

.fa-lyft:before {
    content: "\f3c3"
}

.fa-magento:before {
    content: "\f3c4"
}

.fa-mailchimp:before {
    content: "\f59e"
}

.fa-mandalorian:before {
    content: "\f50f"
}

.fa-markdown:before {
    content: "\f60f"
}

.fa-mastodon:before {
    content: "\f4f6"
}

.fa-maxcdn:before {
    content: "\f136"
}

.fa-mdb:before {
    content: "\f8ca"
}

.fa-medapps:before {
    content: "\f3c6"
}

.fa-medium:before {
    content: "\f23a"
}

.fa-medium-m:before {
    content: "\f23a"
}

.fa-medrt:before {
    content: "\f3c8"
}

.fa-meetup:before {
    content: "\f2e0"
}

.fa-megaport:before {
    content: "\f5a3"
}

.fa-mendeley:before {
    content: "\f7b3"
}

.fa-microblog:before {
    content: "\e01a"
}

.fa-microsoft:before {
    content: "\f3ca"
}

.fa-mix:before {
    content: "\f3cb"
}

.fa-mixcloud:before {
    content: "\f289"
}

.fa-mixer:before {
    content: "\e056"
}

.fa-mizuni:before {
    content: "\f3cc"
}

.fa-modx:before {
    content: "\f285"
}

.fa-monero:before {
    content: "\f3d0"
}

.fa-napster:before {
    content: "\f3d2"
}

.fa-neos:before {
    content: "\f612"
}

.fa-nfc-directional:before {
    content: "\e530"
}

.fa-nfc-symbol:before {
    content: "\e531"
}

.fa-nimblr:before {
    content: "\f5a8"
}

.fa-node:before {
    content: "\f419"
}

.fa-node-js:before {
    content: "\f3d3"
}

.fa-npm:before {
    content: "\f3d4"
}

.fa-ns8:before {
    content: "\f3d5"
}

.fa-nutritionix:before {
    content: "\f3d6"
}

.fa-octopus-deploy:before {
    content: "\e082"
}

.fa-odnoklassniki:before {
    content: "\f263"
}

.fa-odnoklassniki-square:before {
    content: "\f264"
}

.fa-old-republic:before {
    content: "\f510"
}

.fa-opencart:before {
    content: "\f23d"
}

.fa-openid:before {
    content: "\f19b"
}

.fa-opera:before {
    content: "\f26a"
}

.fa-optin-monster:before {
    content: "\f23c"
}

.fa-orcid:before {
    content: "\f8d2"
}

.fa-osi:before {
    content: "\f41a"
}

.fa-padlet:before {
    content: "\e4a0"
}

.fa-page4:before {
    content: "\f3d7"
}

.fa-pagelines:before {
    content: "\f18c"
}

.fa-palfed:before {
    content: "\f3d8"
}

.fa-patreon:before {
    content: "\f3d9"
}

.fa-paypal:before {
    content: "\f1ed"
}

.fa-perbyte:before {
    content: "\e083"
}

.fa-periscope:before {
    content: "\f3da"
}

.fa-phabricator:before {
    content: "\f3db"
}

.fa-phoenix-framework:before {
    content: "\f3dc"
}

.fa-phoenix-squadron:before {
    content: "\f511"
}

.fa-php:before {
    content: "\f457"
}

.fa-pied-piper:before {
    content: "\f2ae"
}

.fa-pied-piper-alt:before {
    content: "\f1a8"
}

.fa-pied-piper-hat:before {
    content: "\f4e5"
}

.fa-pied-piper-pp:before {
    content: "\f1a7"
}

.fa-pied-piper-square:before {
    content: "\e01e"
}

.fa-pinterest:before {
    content: "\f0d2"
}

.fa-pinterest-p:before {
    content: "\f231"
}

.fa-pinterest-square:before {
    content: "\f0d3"
}

.fa-pix:before {
    content: "\e43a"
}

.fa-playstation:before {
    content: "\f3df"
}

.fa-product-hunt:before {
    content: "\f288"
}

.fa-pushed:before {
    content: "\f3e1"
}

.fa-python:before {
    content: "\f3e2"
}

.fa-qq:before {
    content: "\f1d6"
}

.fa-quinscape:before {
    content: "\f459"
}

.fa-quora:before {
    content: "\f2c4"
}

.fa-r-project:before {
    content: "\f4f7"
}

.fa-raspberry-pi:before {
    content: "\f7bb"
}

.fa-ravelry:before {
    content: "\f2d9"
}

.fa-react:before {
    content: "\f41b"
}

.fa-reacteurope:before {
    content: "\f75d"
}

.fa-readme:before {
    content: "\f4d5"
}

.fa-rebel:before {
    content: "\f1d0"
}

.fa-red-river:before {
    content: "\f3e3"
}

.fa-reddit:before {
    content: "\f1a1"
}

.fa-reddit-alien:before {
    content: "\f281"
}

.fa-reddit-square:before {
    content: "\f1a2"
}

.fa-redhat:before {
    content: "\f7bc"
}

.fa-renren:before {
    content: "\f18b"
}

.fa-replyd:before {
    content: "\f3e6"
}

.fa-researchgate:before {
    content: "\f4f8"
}

.fa-resolving:before {
    content: "\f3e7"
}

.fa-rev:before {
    content: "\f5b2"
}

.fa-rocketchat:before {
    content: "\f3e8"
}

.fa-rockrms:before {
    content: "\f3e9"
}

.fa-rust:before {
    content: "\e07a"
}

.fa-safari:before {
    content: "\f267"
}

.fa-salesforce:before {
    content: "\f83b"
}

.fa-sass:before {
    content: "\f41e"
}

.fa-schlix:before {
    content: "\f3ea"
}

.fa-screenpal:before {
    content: "\e570"
}

.fa-scribd:before {
    content: "\f28a"
}

.fa-searchengin:before {
    content: "\f3eb"
}

.fa-sellcast:before {
    content: "\f2da"
}

.fa-sellsy:before {
    content: "\f213"
}

.fa-servicestack:before {
    content: "\f3ec"
}

.fa-shirtsinbulk:before {
    content: "\f214"
}

.fa-shopify:before {
    content: "\e057"
}

.fa-shopware:before {
    content: "\f5b5"
}

.fa-simplybuilt:before {
    content: "\f215"
}

.fa-sistrix:before {
    content: "\f3ee"
}

.fa-sith:before {
    content: "\f512"
}

.fa-sitrox:before {
    content: "\e44a"
}

.fa-sketch:before {
    content: "\f7c6"
}

.fa-skyatlas:before {
    content: "\f216"
}

.fa-skype:before {
    content: "\f17e"
}

.fa-slack:before {
    content: "\f198"
}

.fa-slack-hash:before {
    content: "\f198"
}

.fa-slideshare:before {
    content: "\f1e7"
}

.fa-snapchat:before {
    content: "\f2ab"
}

.fa-snapchat-ghost:before {
    content: "\f2ab"
}

.fa-snapchat-square:before {
    content: "\f2ad"
}

.fa-soundcloud:before {
    content: "\f1be"
}

.fa-sourcetree:before {
    content: "\f7d3"
}

.fa-speakap:before {
    content: "\f3f3"
}

.fa-speaker-deck:before {
    content: "\f83c"
}

.fa-spotify:before {
    content: "\f1bc"
}

.fa-square-font-awesome:before {
    content: "\f425"
}

.fa-square-font-awesome-stroke:before {
    content: "\f35c"
}

.fa-font-awesome-alt:before {
    content: "\f35c"
}

.fa-squarespace:before {
    content: "\f5be"
}

.fa-stack-exchange:before {
    content: "\f18d"
}

.fa-stack-overflow:before {
    content: "\f16c"
}

.fa-stackpath:before {
    content: "\f842"
}

.fa-staylinked:before {
    content: "\f3f5"
}

.fa-steam:before {
    content: "\f1b6"
}

.fa-steam-square:before {
    content: "\f1b7"
}

.fa-steam-symbol:before {
    content: "\f3f6"
}

.fa-sticker-mule:before {
    content: "\f3f7"
}

.fa-strava:before {
    content: "\f428"
}

.fa-stripe:before {
    content: "\f429"
}

.fa-stripe-s:before {
    content: "\f42a"
}

.fa-studiovinari:before {
    content: "\f3f8"
}

.fa-stumbleupon:before {
    content: "\f1a4"
}

.fa-stumbleupon-circle:before {
    content: "\f1a3"
}

.fa-superpowers:before {
    content: "\f2dd"
}

.fa-supple:before {
    content: "\f3f9"
}

.fa-suse:before {
    content: "\f7d6"
}

.fa-swift:before {
    content: "\f8e1"
}

.fa-symfony:before {
    content: "\f83d"
}

.fa-teamspeak:before {
    content: "\f4f9"
}

.fa-telegram:before {
    content: "\f2c6"
}

.fa-telegram-plane:before {
    content: "\f2c6"
}

.fa-tencent-weibo:before {
    content: "\f1d5"
}

.fa-the-red-yeti:before {
    content: "\f69d"
}

.fa-themeco:before {
    content: "\f5c6"
}

.fa-themeisle:before {
    content: "\f2b2"
}

.fa-think-peaks:before {
    content: "\f731"
}

.fa-tiktok:before {
    content: "\e07b"
}

.fa-trade-federation:before {
    content: "\f513"
}

.fa-trello:before {
    content: "\f181"
}

.fa-tumblr:before {
    content: "\f173"
}

.fa-tumblr-square:before {
    content: "\f174"
}

.fa-twitch:before {
    content: "\f1e8"
}

.fa-twitter:before {
    content: "\f099"
}

.fa-twitter-square:before {
    content: "\f081"
}

.fa-typo3:before {
    content: "\f42b"
}

.fa-uber:before {
    content: "\f402"
}

.fa-ubuntu:before {
    content: "\f7df"
}

.fa-uikit:before {
    content: "\f403"
}

.fa-umbraco:before {
    content: "\f8e8"
}

.fa-uncharted:before {
    content: "\e084"
}

.fa-uniregistry:before {
    content: "\f404"
}

.fa-unity:before {
    content: "\e049"
}

.fa-unsplash:before {
    content: "\e07c"
}

.fa-untappd:before {
    content: "\f405"
}

.fa-ups:before {
    content: "\f7e0"
}

.fa-usb:before {
    content: "\f287"
}

.fa-usps:before {
    content: "\f7e1"
}

.fa-ussunnah:before {
    content: "\f407"
}

.fa-vaadin:before {
    content: "\f408"
}

.fa-viacoin:before {
    content: "\f237"
}

.fa-viadeo:before {
    content: "\f2a9"
}

.fa-viadeo-square:before {
    content: "\f2aa"
}

.fa-viber:before {
    content: "\f409"
}

.fa-vimeo:before {
    content: "\f40a"
}

.fa-vimeo-square:before {
    content: "\f194"
}

.fa-vimeo-v:before {
    content: "\f27d"
}

.fa-vine:before {
    content: "\f1ca"
}

.fa-vk:before {
    content: "\f189"
}

.fa-vnv:before {
    content: "\f40b"
}

.fa-vuejs:before {
    content: "\f41f"
}

.fa-watchman-monitoring:before {
    content: "\e087"
}

.fa-waze:before {
    content: "\f83f"
}

.fa-weebly:before {
    content: "\f5cc"
}

.fa-weibo:before {
    content: "\f18a"
}

.fa-weixin:before {
    content: "\f1d7"
}

.fa-whatsapp:before {
    content: "\f232"
}

.fa-whatsapp-square:before {
    content: "\f40c"
}

.fa-whmcs:before {
    content: "\f40d"
}

.fa-wikipedia-w:before {
    content: "\f266"
}

.fa-windows:before {
    content: "\f17a"
}

.fa-wirsindhandwerk:before {
    content: "\e2d0"
}

.fa-wsh:before {
    content: "\e2d0"
}

.fa-wix:before {
    content: "\f5cf"
}

.fa-wizards-of-the-coast:before {
    content: "\f730"
}

.fa-wodu:before {
    content: "\e088"
}

.fa-wolf-pack-battalion:before {
    content: "\f514"
}

.fa-wordpress:before {
    content: "\f19a"
}

.fa-wordpress-simple:before {
    content: "\f411"
}

.fa-wpbeginner:before {
    content: "\f297"
}

.fa-wpexplorer:before {
    content: "\f2de"
}

.fa-wpforms:before {
    content: "\f298"
}

.fa-wpressr:before {
    content: "\f3e4"
}

.fa-xbox:before {
    content: "\f412"
}

.fa-xing:before {
    content: "\f168"
}

.fa-xing-square:before {
    content: "\f169"
}

.fa-y-combinator:before {
    content: "\f23b"
}

.fa-yahoo:before {
    content: "\f19e"
}

.fa-yammer:before {
    content: "\f840"
}

.fa-yandex:before {
    content: "\f413"
}

.fa-yandex-international:before {
    content: "\f414"
}

.fa-yarn:before {
    content: "\f7e3"
}

.fa-yelp:before {
    content: "\f1e9"
}

.fa-yoast:before {
    content: "\f2b1"
}

.fa-youtube:before {
    content: "\f167"
}

.fa-youtube-square:before {
    content: "\f431"
}

.fa-zhihu:before {
    content: "\f63f"
}

.irs {
    display: block;
    position: relative;
    user-select: none;
    -webkit-touch-callout: none
}

.irs.irs-with-grid .irs-grid {
    display: block
}

.irs.irs-disabled {
    opacity: .35
}

.irs-line {
    position: relative;
    display: block;
    outline: 0;
    overflow: hidden
}

.irs-bar {
    position: absolute;
    display: block;
    width: 0;
    left: 0
}

.irs-handle {
    position: absolute;
    display: block;
    z-index: 1;
    cursor: pointer
}

.irs-handle.type_last {
    z-index: 2
}

.irs-max,
.irs-min {
    position: absolute;
    display: block;
    cursor: default
}

.irs-min {
    left: 0
}

.irs-max {
    right: 0
}

.irs-from,
.irs-single,
.irs-to {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    white-space: nowrap;
    cursor: default
}

.irs-grid {
    position: absolute;
    display: none;
    bottom: 0;
    left: 0;
    width: 100%
}

.irs-grid .irs-grid-pol {
    position: absolute;
    width: 1px;
    height: .75rem;
    top: 0;
    left: 0
}

.irs-grid .irs-grid-pol.small {
    height: .5rem
}

.irs-grid .irs-grid-text {
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    white-space: nowrap
}

.irs-disable-mask {
    position: absolute;
    display: block;
    width: 102%;
    height: 100%;
    background: 0 0;
    top: 0;
    left: -1%;
    z-index: 2;
    cursor: default
}

.irs-hidden-input {
    position: absolute !important;
    display: block !important;
    top: 0 !important;
    left: 0 !important;
    width: 0 !important;
    height: 0 !important;
    font-size: 0 !important;
    line-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    outline: 0 !important;
    z-index: -9999 !important;
    background: 0 0 !important;
    border-style: solid !important;
    border-color: transparent !important
}

.irs-shadow {
    display: none
}

.irs--flat {
    height: 4rem
}

.irs--flat.irs-with-grid {
    height: 6rem
}

.irs--flat .irs-bar,
.irs--flat .irs-line {
    top: 3rem
}

.irs--flat .irs-line {
    height: .5rem;
    background: var(--bs-bg-level-3);
    border-radius: 1rem
}

.irs--flat .irs-bar {
    height: .5rem;
    background: #2196f3
}

.irs--flat .irs-bar--single {
    border-radius: 1rem
}

.irs--flat .irs-handle {
    width: 1.5rem;
    height: 1.5rem;
    background: #2196f3;
    top: 2.5rem;
    border-radius: 1.5rem
}

.irs--flat .irs-handle.state_hover,
.irs--flat .irs-handle:hover {
    background: #bce0fb
}

.irs--flat .irs-from,
.irs--flat .irs-max,
.irs--flat .irs-min,
.irs--flat .irs-single,
.irs--flat .irs-to {
    font-size: .875rem;
    font-weight: 500;
    line-height: 1;
    padding: .5rem .75rem;
    border-radius: .25rem
}

.irs--flat .irs-max,
.irs--flat .irs-min {
    top: 0;
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-3)
}

.irs--flat .irs-from,
.irs--flat .irs-single,
.irs--flat .irs-to {
    color: #fff;
    background: #2196f3
}

.irs--flat .irs-from:before,
.irs--flat .irs-single:before,
.irs--flat .irs-to:before {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border: .4rem solid transparent;
    border-top-color: #2196f3;
    bottom: -.8rem;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden
}

.irs--flat .irs-grid {
    height: 2rem
}

.irs--flat .irs-grid-pol {
    background: var(--bs-bg-level-3)
}

.irs--flat .irs-grid-text {
    top: .75rem;
    color: var(--bs-text-level-1);
    font-size: 1rem;
    font-weight: 400
}

@keyframes contextMenuslideUp {
    0% {
        opacity: 0;
        transform: translateY(1rem)
    }

    100% {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes cm-spin {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

.context-menu-list {
    position: absolute;
    display: inline-block;
    min-width: 12.5rem;
    max-width: 25rem;
    padding: .25rem 0;
    list-style: none;
    animation-name: contextMenuslideUp;
    animation-duration: .25s;
    animation-fill-mode: both;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.context-menu-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: var(--bs-text-level-2);
    padding: .5rem 1rem;
    user-select: none
}

.context-menu-item span {
    font-family: var(--bs-font-sans-serif);
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    flex: 1
}

.context-menu-item>label>input,
.context-menu-item>label>textarea {
    user-select: text
}

.context-menu-item.context-menu-hover {
    color: #2196f3;
    background: var(--bs-bg-level-2);
    cursor: pointer
}

.context-menu-item.context-menu-disabled {
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-2);
    opacity: .65;
    cursor: default
}

.context-menu-item>.context-menu-list {
    display: none
}

.context-menu-item.context-menu-visible>.context-menu-list {
    display: block
}

.context-menu-submenu {
    position: relative
}

.context-menu-submenu:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-45deg);
    bottom: 1px
}

@media (prefers-reduced-motion:reduce) {
    .context-menu-submenu:after {
        transition: none
    }
}

.context-menu-separator {
    height: 1px;
    max-height: 1px;
    min-height: 1px;
    background: var(--bs-border-color);
    margin: .5rem 0;
    padding: 0
}

.context-menu-input.context-menu-hover {
    color: var(--bs-text-level-2);
    cursor: default
}

.context-menu-input>label {
    margin: 0
}

.context-menu-input>label,
.context-menu-input>label>input[type=email],
.context-menu-input>label>input[type=text],
.context-menu-input>label>select,
.context-menu-input>label>textarea {
    display: block;
    width: 100%
}

.context-menu-input>label>input[type=checkbox],
.context-menu-input>label>input[type=radio] {
    position: relative;
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    top: .25rem;
    margin-right: .5rem
}

.context-menu-input>label>textarea {
    height: 7rem
}

.context-menu-accesskey {
    text-decoration: underline
}

.context-menu-icon {
    display: flex;
    align-items: center;
    justify-content: center
}

.context-menu-icon:before {
    display: inline-block;
    position: relative;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900;
    width: 1.5rem;
    font-size: 1rem;
    text-align: center;
    margin-right: .5rem
}

.context-menu-icon-add:before {
    content: "\f067"
}

.context-menu-icon-copy:before {
    content: "\f0c5"
}

.context-menu-icon-cut:before {
    content: "\f0c4"
}

.context-menu-icon-delete:before {
    content: "\f55a"
}

.context-menu-icon-edit:before {
    content: "\f044"
}

.context-menu-icon-loading:before {
    content: "\f1ce"
}

.context-menu-icon-paste:before {
    content: "\f0ea"
}

.context-menu-icon-quit:before {
    content: "\f2f5"
}

.context-menu-icon-loading:before {
    animation: cm-spin .75s infinite linear
}

.jstree-children,
.jstree-container-ul,
.jstree-node {
    display: block;
    margin: 0;
    padding: 0;
    list-style-type: none;
    list-style-image: none
}

.jstree-container-ul>.jstree-node {
    margin-left: 0;
    margin-right: 0
}

.jstree-anchor,
.jstree-animated,
.jstree-wholerow {
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {

    .jstree-anchor,
    .jstree-animated,
    .jstree-wholerow {
        transition: none
    }
}

.jstree-node {
    position: relative;
    min-height: 2rem;
    line-height: 2rem;
    margin-left: 2rem;
    white-space: nowrap
}

.jstree-node:before {
    content: "";
    display: block;
    position: absolute;
    height: 100%;
    width: 2px;
    background: var(--bs-bg-level-4);
    top: 0;
    left: 1rem;
    transform: translateX(-50%);
    z-index: 1
}

.jstree-anchor {
    display: inline-block;
    height: 2rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    line-height: 2rem;
    padding-right: .5rem;
    margin: 0;
    white-space: nowrap;
    vertical-align: top;
    border-radius: .25rem
}

.jstree-anchor a.jstree-anchor {
    color: var(--bs-text-level-3)
}

.jstree-anchor:focus {
    outline: 0
}

.jstree-anchor,
.jstree-anchor:active,
.jstree-anchor:hover,
.jstree-anchor:link,
.jstree-anchor:visited {
    text-decoration: none
}

.jstree-ocl {
    z-index: 1;
    cursor: pointer
}

.jstree-node.jstree-leaf>.jstree-ocl:before {
    content: "";
    position: absolute;
    display: block;
    width: 50%;
    height: 2px;
    background: var(--bs-bg-level-4);
    left: 50%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1
}

.jstree-ocl:after {
    width: 100%;
    height: 100%;
    z-index: 2;
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-1)
}

.jstree {
    background: 0 0
}

.jstree .jstree-closed>.jstree-children,
.jstree .jstree-leaf>.jstree-children {
    display: none
}

.jstree .jstree-leaf>.jstree-ocl {
    cursor: default
}

.jstree .jstree-open>.jstree-children {
    display: block
}

.jstree-hovered:not(.jstree-clicked):not(.jstree-disabled) {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.jstree-clicked {
    color: #fff !important;
    background: #2196f3 !important
}

.jstree-clicked a.jstree-clicked {
    color: #fff !important
}

.jstree-clicked .jstree-checkbox:after {
    color: #fff !important
}

.jstree-disabled {
    cursor: default;
    color: var(--bs-text-level-1) !important;
    background: var(--bs-bg-level-2) !important;
    opacity: .65 !important
}

.jstree-striped {
    background-image: linear-gradient(to bottom, #bdbdbd 50%, transparent 50%);
    background-size: auto 4rem
}

.jstree-search {
    font-style: italic;
    font-weight: 500
}

.jstree-no-dots .jstree-node {
    position: static
}

.jstree-no-dots .jstree-node:before,
.jstree-no-dots .jstree-ocl:before {
    display: none !important
}

.jstree-no-dots .jstree-ocl:after {
    background: 0 0
}

.jstree-ellipsis {
    max-width: 100%;
    overflow: hidden
}

.jstree-ellipsis .jstree-anchor {
    max-width: calc(100% - 2rem);
    text-overflow: ellipsis;
    overflow: hidden
}

.jstree-hidden,
.jstree-hidden.jstree-node {
    display: none
}

.jstree-icon {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem
}

.jstree-icon:after {
    display: inline-block;
    position: relative;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 400
}

.jstree-icon,
.jstree-icon:empty {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    margin: 0;
    padding: 0;
    vertical-align: top;
    text-decoration: none;
    text-align: center
}

.jstree-anchor>.jstree-themeicon-hidden,
.jstree-no-icons .jstree-themeicon {
    display: none
}

.jstree-themeicon:after {
    content: "\f07b"
}

.jstree-themeicon-custom:after {
    content: ""
}

.jstree-open>.jstree-ocl:after {
    content: "\f150"
}

.jstree-closed>.jstree-ocl:after {
    content: "\f152"
}

.jstree-loading>.jstree-ocl {
    z-index: 2;
    background: var(--bs-bg-level-1)
}

.jstree-loading>.jstree-ocl:before {
    display: none
}

.jstree-loading>.jstree-ocl:after {
    content: "";
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    vertical-align: text-bottom;
    border-radius: 50%;
    animation: spinner-border .75s linear infinite;
    border: 2px solid var(--bs-text-level-1);
    border-right-color: transparent
}

.jstree-checkbox:after {
    color: var(--bs-text-level-1);
    content: "\f0c8"
}

.jstree-undetermined:after {
    color: var(--bs-text-level-1);
    content: "\f146"
}

.jstree-checked:not(.jstree-disabled)>.jstree-checkbox:after,
.jstree-clicked:not(.jstree-disabled)>.jstree-checkbox:after {
    content: "\f14a"
}

.jstree-no-checkboxes .jstree-checkbox {
    display: none !important
}

.jstree-checkbox-no-clicked .jstree-clicked {
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1)
}

.jstree-checkbox-no-clicked .jstree-clicked.jstree-hovered {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.jstree-checkbox-no-clicked>.jstree-wholerow-ul .jstree-wholerow-clicked {
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1)
}

.jstree-checkbox-no-clicked>.jstree-wholerow-ul .jstree-wholerow-clicked.jstree-wholerow-hovered {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.jstree-checkbox-no-clicked .jstree-checkbox:after,
.jstree-checkbox-no-clicked .jstree-ocl:after {
    color: var(--bs-text-level-1) !important
}

.jstree-checkbox-no-clicked .jstree-checkbox:after {
    content: "\f0c8" !important
}

.jstree-wholerow-ul {
    position: relative;
    display: inline-block;
    min-width: 100%
}

.jstree-wholerow-ul .jstree-leaf>.jstree-ocl {
    cursor: pointer
}

.jstree-wholerow-ul .jstree-anchor,
.jstree-wholerow-ul .jstree-icon {
    position: relative
}

.jstree-wholerow-ul .jstree-clicked,
.jstree-wholerow-ul .jstree-hovered {
    background: 0 0
}

.jstree-wholerow-ul .jstree-wholerow {
    position: absolute;
    height: 2rem;
    width: 100%;
    left: 0;
    user-select: none;
    cursor: pointer
}

.jstree-wholerow-ul .jstree-wholerow-hovered {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.jstree-wholerow-ul .jstree-wholerow-clicked {
    color: #fff;
    background: #2196f3
}

.jstree-wholerow-ul .jstree-wholerow-clicked+.jstree-ocl:after {
    color: #fff
}

.jstree-contextmenu .jstree-anchor {
    -webkit-user-select: none;
    -webkit-touch-callout: none
}

.vakata-context {
    z-index: 10;
    display: none
}

.vakata-context,
.vakata-context ul {
    position: absolute;
    width: 12rem;
    padding: .25rem 0;
    margin: 0;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.vakata-context ul {
    list-style: none;
    left: 100%;
    margin-top: -2.5rem
}

.vakata-context .vakata-context-right ul {
    left: auto;
    right: 100%
}

.vakata-context li {
    list-style: none
}

.vakata-context li>a {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: var(--bs-text-level-2);
    padding: .5rem 1rem;
    text-decoration: none;
    white-space: nowrap;
    position: relative
}

.vakata-context li>a>i {
    font-size: 1rem
}

.vakata-context li>a>svg {
    font-size: 1rem;
    height: 1em
}

.vakata-context li>a>i,
.vakata-context li>a>svg {
    display: inline-block;
    width: 1.5rem;
    margin-right: .75rem;
    text-align: center;
    vertical-align: top
}

.vakata-context li>a.vakata-context-parent {
    position: relative
}

.vakata-context li>a.vakata-context-parent:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(-45deg);
    bottom: 1px;
    top: calc(50% - .275rem);
    right: .75rem;
    position: absolute
}

@media (prefers-reduced-motion:reduce) {
    .vakata-context li>a.vakata-context-parent:after {
        transition: none
    }
}

.vakata-context li>a:focus {
    outline: 0
}

.vakata-context li>a .vakata-contextmenu-sep {
    display: none
}

.vakata-context .vakata-context-separator {
    height: 1px;
    max-height: 1px;
    background: var(--bs-border-color);
    padding: 0;
    border: 0
}

.vakata-context .vakata-context-hover>a {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.vakata-context .vakata-contextmenu-disabled a,
.vakata-context .vakata-contextmenu-disabled a:hover {
    color: var(--bs-bg-level-2);
    background: var(--bs-text-level-1);
    opacity: .65;
    cursor: default
}

#jstree-marker {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    padding: 0;
    border-right: 0;
    transform: translateY(-50%);
    border-top: .5rem solid transparent;
    border-bottom: .5rem solid transparent;
    border-left: .5rem solid var(--bs-text-level-1)
}

#jstree-dnd {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 2rem;
    padding-right: .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    margin: 0;
    border-radius: .25rem
}

#jstree-dnd .jstree-copy,
#jstree-dnd .jstree-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    margin-right: .25rem;
    padding: 0
}

#jstree-dnd .jstree-er:after,
#jstree-dnd .jstree-ok:after {
    display: inline-block;
    position: relative;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900
}

#jstree-dnd .jstree-ok:after {
    content: "\f00c";
    color: #4caf50
}

#jstree-dnd .jstree-er:after {
    content: "\f00d";
    color: #f44336
}

@font-face {
    font-family: KaTeX_AMS;
    src: url(../../fonts/katexKaTeX_AMS-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_AMS-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_AMS-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Caligraphic;
    src: url(../../fonts/katexKaTeX_Caligraphic-Bold.html) format("woff2"), url(../../fonts/katexKaTeX_Caligraphic-Bold-2.html) format("woff"), url(../../fonts/katexKaTeX_Caligraphic-Bold-3.html) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Caligraphic;
    src: url(../../fonts/katexKaTeX_Caligraphic-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Caligraphic-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Caligraphic-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Fraktur;
    src: url(../../fonts/katexKaTeX_Fraktur-Bold.html) format("woff2"), url(../../fonts/katexKaTeX_Fraktur-Bold-2.html) format("woff"), url(../../fonts/katexKaTeX_Fraktur-Bold-3.html) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Fraktur;
    src: url(../../fonts/katexKaTeX_Fraktur-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Fraktur-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Fraktur-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Main;
    src: url(../../fonts/katexKaTeX_Main-Bold.html) format("woff2"), url(../../fonts/katexKaTeX_Main-Bold-2.html) format("woff"), url(../../fonts/katexKaTeX_Main-Bold-3.html) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Main;
    src: url(../../fonts/katexKaTeX_Main-BoldItalic.html) format("woff2"), url(../../fonts/katexKaTeX_Main-BoldItalic-2.html) format("woff"), url(../../fonts/katexKaTeX_Main-BoldItalic-3.html) format("truetype");
    font-weight: 700;
    font-style: italic
}

@font-face {
    font-family: KaTeX_Main;
    src: url(../../fonts/katexKaTeX_Main-Italic.html) format("woff2"), url(../../fonts/katexKaTeX_Main-Italic-2.html) format("woff"), url(../../fonts/katexKaTeX_Main-Italic-3.html) format("truetype");
    font-weight: 400;
    font-style: italic
}

@font-face {
    font-family: KaTeX_Main;
    src: url(../../fonts/katexKaTeX_Main-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Main-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Main-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Math;
    src: url(../../fonts/katexKaTeX_Math-BoldItalic.html) format("woff2"), url(../../fonts/katexKaTeX_Math-BoldItalic-2.html) format("woff"), url(../../fonts/katexKaTeX_Math-BoldItalic-3.html) format("truetype");
    font-weight: 700;
    font-style: italic
}

@font-face {
    font-family: KaTeX_Math;
    src: url(../../fonts/katexKaTeX_Math-Italic.html) format("woff2"), url(../../fonts/katexKaTeX_Math-Italic-2.html) format("woff"), url(../../fonts/katexKaTeX_Math-Italic-3.html) format("truetype");
    font-weight: 400;
    font-style: italic
}

@font-face {
    font-family: KaTeX_SansSerif;
    src: url(../../fonts/katexKaTeX_SansSerif-Bold.html) format("woff2"), url(../../fonts/katexKaTeX_SansSerif-Bold-2.html) format("woff"), url(../../fonts/katexKaTeX_SansSerif-Bold-3.html) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: KaTeX_SansSerif;
    src: url(../../fonts/katexKaTeX_SansSerif-Italic.html) format("woff2"), url(../../fonts/katexKaTeX_SansSerif-Italic-2.html) format("woff"), url(../../fonts/katexKaTeX_SansSerif-Italic-3.html) format("truetype");
    font-weight: 400;
    font-style: italic
}

@font-face {
    font-family: KaTeX_SansSerif;
    src: url(../../fonts/katexKaTeX_SansSerif-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_SansSerif-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_SansSerif-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Script;
    src: url(../../fonts/katexKaTeX_Script-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Script-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Script-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Size1;
    src: url(../../fonts/katexKaTeX_Size1-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Size1-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Size1-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Size2;
    src: url(../../fonts/katexKaTeX_Size2-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Size2-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Size2-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Size3;
    src: url(../../fonts/katexKaTeX_Size3-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Size3-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Size3-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Size4;
    src: url(../../fonts/katexKaTeX_Size4-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Size4-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Size4-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: KaTeX_Typewriter;
    src: url(../../fonts/katexKaTeX_Typewriter-Regular.html) format("woff2"), url(../../fonts/katexKaTeX_Typewriter-Regular-2.html) format("woff"), url(../../fonts/katexKaTeX_Typewriter-Regular-3.html) format("truetype");
    font-weight: 400;
    font-style: normal
}

.katex {
    font: normal 1.21em KaTeX_Main, Times New Roman, serif;
    line-height: 1.2;
    text-indent: 0;
    text-rendering: auto
}

.katex * {
    -ms-high-contrast-adjust: none !important;
    border-color: currentColor
}

.katex .katex-version::after {
    content: "0.15.1"
}

.katex .katex-mathml {
    position: absolute;
    clip: rect(1px, 1px, 1px, 1px);
    padding: 0;
    border: 0;
    height: 1px;
    width: 1px;
    overflow: hidden
}

.katex .katex-html>.newline {
    display: block
}

.katex .base {
    position: relative;
    display: inline-block;
    white-space: nowrap;
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content
}

.katex .strut {
    display: inline-block
}

.katex .textbf {
    font-weight: 700
}

.katex .textit {
    font-style: italic
}

.katex .textrm {
    font-family: KaTeX_Main
}

.katex .textsf {
    font-family: KaTeX_SansSerif
}

.katex .texttt {
    font-family: KaTeX_Typewriter
}

.katex .mathnormal {
    font-family: KaTeX_Math;
    font-style: italic
}

.katex .mathit {
    font-family: KaTeX_Main;
    font-style: italic
}

.katex .mathrm {
    font-style: normal
}

.katex .mathbf {
    font-family: KaTeX_Main;
    font-weight: 700
}

.katex .boldsymbol {
    font-family: KaTeX_Math;
    font-weight: 700;
    font-style: italic
}

.katex .amsrm {
    font-family: KaTeX_AMS
}

.katex .mathbb,
.katex .textbb {
    font-family: KaTeX_AMS
}

.katex .mathcal {
    font-family: KaTeX_Caligraphic
}

.katex .mathfrak,
.katex .textfrak {
    font-family: KaTeX_Fraktur
}

.katex .mathtt {
    font-family: KaTeX_Typewriter
}

.katex .mathscr,
.katex .textscr {
    font-family: KaTeX_Script
}

.katex .mathsf,
.katex .textsf {
    font-family: KaTeX_SansSerif
}

.katex .mathboldsf,
.katex .textboldsf {
    font-family: KaTeX_SansSerif;
    font-weight: 700
}

.katex .mathitsf,
.katex .textitsf {
    font-family: KaTeX_SansSerif;
    font-style: italic
}

.katex .mainrm {
    font-family: KaTeX_Main;
    font-style: normal
}

.katex .vlist-t {
    display: inline-table;
    table-layout: fixed;
    border-collapse: collapse
}

.katex .vlist-r {
    display: table-row
}

.katex .vlist {
    display: table-cell;
    vertical-align: bottom;
    position: relative
}

.katex .vlist>span {
    display: block;
    height: 0;
    position: relative
}

.katex .vlist>span>span {
    display: inline-block
}

.katex .vlist>span>.pstrut {
    overflow: hidden;
    width: 0
}

.katex .vlist-t2 {
    margin-right: -2px
}

.katex .vlist-s {
    display: table-cell;
    vertical-align: bottom;
    font-size: 1px;
    width: 2px;
    min-width: 2px
}

.katex .vbox {
    display: inline-flex;
    flex-direction: column;
    align-items: baseline
}

.katex .hbox {
    display: inline-flex;
    flex-direction: row;
    width: 100%
}

.katex .thinbox {
    display: inline-flex;
    flex-direction: row;
    width: 0;
    max-width: 0
}

.katex .msupsub {
    text-align: left
}

.katex .mfrac>span>span {
    text-align: center
}

.katex .mfrac .frac-line {
    display: inline-block;
    width: 100%;
    border-bottom-style: solid
}

.katex .hdashline,
.katex .hline,
.katex .mfrac .frac-line,
.katex .overline .overline-line,
.katex .rule,
.katex .underline .underline-line {
    min-height: 1px
}

.katex .mspace {
    display: inline-block
}

.katex .clap,
.katex .llap,
.katex .rlap {
    width: 0;
    position: relative
}

.katex .clap>.inner,
.katex .llap>.inner,
.katex .rlap>.inner {
    position: absolute
}

.katex .clap>.fix,
.katex .llap>.fix,
.katex .rlap>.fix {
    display: inline-block
}

.katex .llap>.inner {
    right: 0
}

.katex .clap>.inner,
.katex .rlap>.inner {
    left: 0
}

.katex .clap>.inner>span {
    margin-left: -50%;
    margin-right: 50%
}

.katex .rule {
    display: inline-block;
    border: solid 0;
    position: relative
}

.katex .hline,
.katex .overline .overline-line,
.katex .underline .underline-line {
    display: inline-block;
    width: 100%;
    border-bottom-style: solid
}

.katex .hdashline {
    display: inline-block;
    width: 100%;
    border-bottom-style: dashed
}

.katex .sqrt>.root {
    margin-left: .27777778em;
    margin-right: -.55555556em
}

.katex .fontsize-ensurer.reset-size1.size1,
.katex .sizing.reset-size1.size1 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size1.size2,
.katex .sizing.reset-size1.size2 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size1.size3,
.katex .sizing.reset-size1.size3 {
    font-size: 1.4em
}

.katex .fontsize-ensurer.reset-size1.size4,
.katex .sizing.reset-size1.size4 {
    font-size: 1.6em
}

.katex .fontsize-ensurer.reset-size1.size5,
.katex .sizing.reset-size1.size5 {
    font-size: 1.8em
}

.katex .fontsize-ensurer.reset-size1.size6,
.katex .sizing.reset-size1.size6 {
    font-size: 2em
}

.katex .fontsize-ensurer.reset-size1.size7,
.katex .sizing.reset-size1.size7 {
    font-size: 2.4em
}

.katex .fontsize-ensurer.reset-size1.size8,
.katex .sizing.reset-size1.size8 {
    font-size: 2.88em
}

.katex .fontsize-ensurer.reset-size1.size9,
.katex .sizing.reset-size1.size9 {
    font-size: 3.456em
}

.katex .fontsize-ensurer.reset-size1.size10,
.katex .sizing.reset-size1.size10 {
    font-size: 4.148em
}

.katex .fontsize-ensurer.reset-size1.size11,
.katex .sizing.reset-size1.size11 {
    font-size: 4.976em
}

.katex .fontsize-ensurer.reset-size2.size1,
.katex .sizing.reset-size2.size1 {
    font-size: .83333333em
}

.katex .fontsize-ensurer.reset-size2.size2,
.katex .sizing.reset-size2.size2 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size2.size3,
.katex .sizing.reset-size2.size3 {
    font-size: 1.16666667em
}

.katex .fontsize-ensurer.reset-size2.size4,
.katex .sizing.reset-size2.size4 {
    font-size: 1.33333333em
}

.katex .fontsize-ensurer.reset-size2.size5,
.katex .sizing.reset-size2.size5 {
    font-size: 1.5em
}

.katex .fontsize-ensurer.reset-size2.size6,
.katex .sizing.reset-size2.size6 {
    font-size: 1.66666667em
}

.katex .fontsize-ensurer.reset-size2.size7,
.katex .sizing.reset-size2.size7 {
    font-size: 2em
}

.katex .fontsize-ensurer.reset-size2.size8,
.katex .sizing.reset-size2.size8 {
    font-size: 2.4em
}

.katex .fontsize-ensurer.reset-size2.size9,
.katex .sizing.reset-size2.size9 {
    font-size: 2.88em
}

.katex .fontsize-ensurer.reset-size2.size10,
.katex .sizing.reset-size2.size10 {
    font-size: 3.45666667em
}

.katex .fontsize-ensurer.reset-size2.size11,
.katex .sizing.reset-size2.size11 {
    font-size: 4.14666667em
}

.katex .fontsize-ensurer.reset-size3.size1,
.katex .sizing.reset-size3.size1 {
    font-size: .71428571em
}

.katex .fontsize-ensurer.reset-size3.size2,
.katex .sizing.reset-size3.size2 {
    font-size: .85714286em
}

.katex .fontsize-ensurer.reset-size3.size3,
.katex .sizing.reset-size3.size3 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size3.size4,
.katex .sizing.reset-size3.size4 {
    font-size: 1.14285714em
}

.katex .fontsize-ensurer.reset-size3.size5,
.katex .sizing.reset-size3.size5 {
    font-size: 1.28571429em
}

.katex .fontsize-ensurer.reset-size3.size6,
.katex .sizing.reset-size3.size6 {
    font-size: 1.42857143em
}

.katex .fontsize-ensurer.reset-size3.size7,
.katex .sizing.reset-size3.size7 {
    font-size: 1.71428571em
}

.katex .fontsize-ensurer.reset-size3.size8,
.katex .sizing.reset-size3.size8 {
    font-size: 2.05714286em
}

.katex .fontsize-ensurer.reset-size3.size9,
.katex .sizing.reset-size3.size9 {
    font-size: 2.46857143em
}

.katex .fontsize-ensurer.reset-size3.size10,
.katex .sizing.reset-size3.size10 {
    font-size: 2.96285714em
}

.katex .fontsize-ensurer.reset-size3.size11,
.katex .sizing.reset-size3.size11 {
    font-size: 3.55428571em
}

.katex .fontsize-ensurer.reset-size4.size1,
.katex .sizing.reset-size4.size1 {
    font-size: .625em
}

.katex .fontsize-ensurer.reset-size4.size2,
.katex .sizing.reset-size4.size2 {
    font-size: .75em
}

.katex .fontsize-ensurer.reset-size4.size3,
.katex .sizing.reset-size4.size3 {
    font-size: .875em
}

.katex .fontsize-ensurer.reset-size4.size4,
.katex .sizing.reset-size4.size4 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size4.size5,
.katex .sizing.reset-size4.size5 {
    font-size: 1.125em
}

.katex .fontsize-ensurer.reset-size4.size6,
.katex .sizing.reset-size4.size6 {
    font-size: 1.25em
}

.katex .fontsize-ensurer.reset-size4.size7,
.katex .sizing.reset-size4.size7 {
    font-size: 1.5em
}

.katex .fontsize-ensurer.reset-size4.size8,
.katex .sizing.reset-size4.size8 {
    font-size: 1.8em
}

.katex .fontsize-ensurer.reset-size4.size9,
.katex .sizing.reset-size4.size9 {
    font-size: 2.16em
}

.katex .fontsize-ensurer.reset-size4.size10,
.katex .sizing.reset-size4.size10 {
    font-size: 2.5925em
}

.katex .fontsize-ensurer.reset-size4.size11,
.katex .sizing.reset-size4.size11 {
    font-size: 3.11em
}

.katex .fontsize-ensurer.reset-size5.size1,
.katex .sizing.reset-size5.size1 {
    font-size: .55555556em
}

.katex .fontsize-ensurer.reset-size5.size2,
.katex .sizing.reset-size5.size2 {
    font-size: .66666667em
}

.katex .fontsize-ensurer.reset-size5.size3,
.katex .sizing.reset-size5.size3 {
    font-size: .77777778em
}

.katex .fontsize-ensurer.reset-size5.size4,
.katex .sizing.reset-size5.size4 {
    font-size: .88888889em
}

.katex .fontsize-ensurer.reset-size5.size5,
.katex .sizing.reset-size5.size5 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size5.size6,
.katex .sizing.reset-size5.size6 {
    font-size: 1.11111111em
}

.katex .fontsize-ensurer.reset-size5.size7,
.katex .sizing.reset-size5.size7 {
    font-size: 1.33333333em
}

.katex .fontsize-ensurer.reset-size5.size8,
.katex .sizing.reset-size5.size8 {
    font-size: 1.6em
}

.katex .fontsize-ensurer.reset-size5.size9,
.katex .sizing.reset-size5.size9 {
    font-size: 1.92em
}

.katex .fontsize-ensurer.reset-size5.size10,
.katex .sizing.reset-size5.size10 {
    font-size: 2.30444444em
}

.katex .fontsize-ensurer.reset-size5.size11,
.katex .sizing.reset-size5.size11 {
    font-size: 2.76444444em
}

.katex .fontsize-ensurer.reset-size6.size1,
.katex .sizing.reset-size6.size1 {
    font-size: .5em
}

.katex .fontsize-ensurer.reset-size6.size2,
.katex .sizing.reset-size6.size2 {
    font-size: .6em
}

.katex .fontsize-ensurer.reset-size6.size3,
.katex .sizing.reset-size6.size3 {
    font-size: .7em
}

.katex .fontsize-ensurer.reset-size6.size4,
.katex .sizing.reset-size6.size4 {
    font-size: .8em
}

.katex .fontsize-ensurer.reset-size6.size5,
.katex .sizing.reset-size6.size5 {
    font-size: .9em
}

.katex .fontsize-ensurer.reset-size6.size6,
.katex .sizing.reset-size6.size6 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size6.size7,
.katex .sizing.reset-size6.size7 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size6.size8,
.katex .sizing.reset-size6.size8 {
    font-size: 1.44em
}

.katex .fontsize-ensurer.reset-size6.size9,
.katex .sizing.reset-size6.size9 {
    font-size: 1.728em
}

.katex .fontsize-ensurer.reset-size6.size10,
.katex .sizing.reset-size6.size10 {
    font-size: 2.074em
}

.katex .fontsize-ensurer.reset-size6.size11,
.katex .sizing.reset-size6.size11 {
    font-size: 2.488em
}

.katex .fontsize-ensurer.reset-size7.size1,
.katex .sizing.reset-size7.size1 {
    font-size: .41666667em
}

.katex .fontsize-ensurer.reset-size7.size2,
.katex .sizing.reset-size7.size2 {
    font-size: .5em
}

.katex .fontsize-ensurer.reset-size7.size3,
.katex .sizing.reset-size7.size3 {
    font-size: .58333333em
}

.katex .fontsize-ensurer.reset-size7.size4,
.katex .sizing.reset-size7.size4 {
    font-size: .66666667em
}

.katex .fontsize-ensurer.reset-size7.size5,
.katex .sizing.reset-size7.size5 {
    font-size: .75em
}

.katex .fontsize-ensurer.reset-size7.size6,
.katex .sizing.reset-size7.size6 {
    font-size: .83333333em
}

.katex .fontsize-ensurer.reset-size7.size7,
.katex .sizing.reset-size7.size7 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size7.size8,
.katex .sizing.reset-size7.size8 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size7.size9,
.katex .sizing.reset-size7.size9 {
    font-size: 1.44em
}

.katex .fontsize-ensurer.reset-size7.size10,
.katex .sizing.reset-size7.size10 {
    font-size: 1.72833333em
}

.katex .fontsize-ensurer.reset-size7.size11,
.katex .sizing.reset-size7.size11 {
    font-size: 2.07333333em
}

.katex .fontsize-ensurer.reset-size8.size1,
.katex .sizing.reset-size8.size1 {
    font-size: .34722222em
}

.katex .fontsize-ensurer.reset-size8.size2,
.katex .sizing.reset-size8.size2 {
    font-size: .41666667em
}

.katex .fontsize-ensurer.reset-size8.size3,
.katex .sizing.reset-size8.size3 {
    font-size: .48611111em
}

.katex .fontsize-ensurer.reset-size8.size4,
.katex .sizing.reset-size8.size4 {
    font-size: .55555556em
}

.katex .fontsize-ensurer.reset-size8.size5,
.katex .sizing.reset-size8.size5 {
    font-size: .625em
}

.katex .fontsize-ensurer.reset-size8.size6,
.katex .sizing.reset-size8.size6 {
    font-size: .69444444em
}

.katex .fontsize-ensurer.reset-size8.size7,
.katex .sizing.reset-size8.size7 {
    font-size: .83333333em
}

.katex .fontsize-ensurer.reset-size8.size8,
.katex .sizing.reset-size8.size8 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size8.size9,
.katex .sizing.reset-size8.size9 {
    font-size: 1.2em
}

.katex .fontsize-ensurer.reset-size8.size10,
.katex .sizing.reset-size8.size10 {
    font-size: 1.44027778em
}

.katex .fontsize-ensurer.reset-size8.size11,
.katex .sizing.reset-size8.size11 {
    font-size: 1.72777778em
}

.katex .fontsize-ensurer.reset-size9.size1,
.katex .sizing.reset-size9.size1 {
    font-size: .28935185em
}

.katex .fontsize-ensurer.reset-size9.size2,
.katex .sizing.reset-size9.size2 {
    font-size: .34722222em
}

.katex .fontsize-ensurer.reset-size9.size3,
.katex .sizing.reset-size9.size3 {
    font-size: .40509259em
}

.katex .fontsize-ensurer.reset-size9.size4,
.katex .sizing.reset-size9.size4 {
    font-size: .46296296em
}

.katex .fontsize-ensurer.reset-size9.size5,
.katex .sizing.reset-size9.size5 {
    font-size: .52083333em
}

.katex .fontsize-ensurer.reset-size9.size6,
.katex .sizing.reset-size9.size6 {
    font-size: .5787037em
}

.katex .fontsize-ensurer.reset-size9.size7,
.katex .sizing.reset-size9.size7 {
    font-size: .69444444em
}

.katex .fontsize-ensurer.reset-size9.size8,
.katex .sizing.reset-size9.size8 {
    font-size: .83333333em
}

.katex .fontsize-ensurer.reset-size9.size9,
.katex .sizing.reset-size9.size9 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size9.size10,
.katex .sizing.reset-size9.size10 {
    font-size: 1.20023148em
}

.katex .fontsize-ensurer.reset-size9.size11,
.katex .sizing.reset-size9.size11 {
    font-size: 1.43981481em
}

.katex .fontsize-ensurer.reset-size10.size1,
.katex .sizing.reset-size10.size1 {
    font-size: .24108004em
}

.katex .fontsize-ensurer.reset-size10.size2,
.katex .sizing.reset-size10.size2 {
    font-size: .28929605em
}

.katex .fontsize-ensurer.reset-size10.size3,
.katex .sizing.reset-size10.size3 {
    font-size: .33751205em
}

.katex .fontsize-ensurer.reset-size10.size4,
.katex .sizing.reset-size10.size4 {
    font-size: .38572806em
}

.katex .fontsize-ensurer.reset-size10.size5,
.katex .sizing.reset-size10.size5 {
    font-size: .43394407em
}

.katex .fontsize-ensurer.reset-size10.size6,
.katex .sizing.reset-size10.size6 {
    font-size: .48216008em
}

.katex .fontsize-ensurer.reset-size10.size7,
.katex .sizing.reset-size10.size7 {
    font-size: .57859209em
}

.katex .fontsize-ensurer.reset-size10.size8,
.katex .sizing.reset-size10.size8 {
    font-size: .69431051em
}

.katex .fontsize-ensurer.reset-size10.size9,
.katex .sizing.reset-size10.size9 {
    font-size: .83317261em
}

.katex .fontsize-ensurer.reset-size10.size10,
.katex .sizing.reset-size10.size10 {
    font-size: 1em
}

.katex .fontsize-ensurer.reset-size10.size11,
.katex .sizing.reset-size10.size11 {
    font-size: 1.19961427em
}

.katex .fontsize-ensurer.reset-size11.size1,
.katex .sizing.reset-size11.size1 {
    font-size: .20096463em
}

.katex .fontsize-ensurer.reset-size11.size2,
.katex .sizing.reset-size11.size2 {
    font-size: .24115756em
}

.katex .fontsize-ensurer.reset-size11.size3,
.katex .sizing.reset-size11.size3 {
    font-size: .28135048em
}

.katex .fontsize-ensurer.reset-size11.size4,
.katex .sizing.reset-size11.size4 {
    font-size: .32154341em
}

.katex .fontsize-ensurer.reset-size11.size5,
.katex .sizing.reset-size11.size5 {
    font-size: .36173633em
}

.katex .fontsize-ensurer.reset-size11.size6,
.katex .sizing.reset-size11.size6 {
    font-size: .40192926em
}

.katex .fontsize-ensurer.reset-size11.size7,
.katex .sizing.reset-size11.size7 {
    font-size: .48231511em
}

.katex .fontsize-ensurer.reset-size11.size8,
.katex .sizing.reset-size11.size8 {
    font-size: .57877814em
}

.katex .fontsize-ensurer.reset-size11.size9,
.katex .sizing.reset-size11.size9 {
    font-size: .69453376em
}

.katex .fontsize-ensurer.reset-size11.size10,
.katex .sizing.reset-size11.size10 {
    font-size: .83360129em
}

.katex .fontsize-ensurer.reset-size11.size11,
.katex .sizing.reset-size11.size11 {
    font-size: 1em
}

.katex .delimsizing.size1 {
    font-family: KaTeX_Size1
}

.katex .delimsizing.size2 {
    font-family: KaTeX_Size2
}

.katex .delimsizing.size3 {
    font-family: KaTeX_Size3
}

.katex .delimsizing.size4 {
    font-family: KaTeX_Size4
}

.katex .delimsizing.mult .delim-size1>span {
    font-family: KaTeX_Size1
}

.katex .delimsizing.mult .delim-size4>span {
    font-family: KaTeX_Size4
}

.katex .nulldelimiter {
    display: inline-block;
    width: .12em
}

.katex .delimcenter {
    position: relative
}

.katex .op-symbol {
    position: relative
}

.katex .op-symbol.small-op {
    font-family: KaTeX_Size1
}

.katex .op-symbol.large-op {
    font-family: KaTeX_Size2
}

.katex .op-limits>.vlist-t {
    text-align: center
}

.katex .accent>.vlist-t {
    text-align: center
}

.katex .accent .accent-body {
    position: relative
}

.katex .accent .accent-body:not(.accent-full) {
    width: 0
}

.katex .overlay {
    display: block
}

.katex .mtable .vertical-separator {
    display: inline-block;
    min-width: 1px
}

.katex .mtable .arraycolsep {
    display: inline-block
}

.katex .mtable .col-align-c>.vlist-t {
    text-align: center
}

.katex .mtable .col-align-l>.vlist-t {
    text-align: left
}

.katex .mtable .col-align-r>.vlist-t {
    text-align: right
}

.katex .svg-align {
    text-align: left
}

.katex svg {
    display: block;
    position: absolute;
    width: 100%;
    height: inherit;
    fill: currentColor;
    stroke: currentColor;
    fill-rule: nonzero;
    fill-opacity: 1;
    stroke-width: 1;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    stroke-miterlimit: 4;
    stroke-dasharray: none;
    stroke-dashoffset: 0;
    stroke-opacity: 1
}

.katex svg path {
    stroke: none
}

.katex img {
    border-style: none;
    min-width: 0;
    min-height: 0;
    max-width: none;
    max-height: none
}

.katex .stretchy {
    width: 100%;
    display: block;
    position: relative;
    overflow: hidden
}

.katex .stretchy::after,
.katex .stretchy::before {
    content: ""
}

.katex .hide-tail {
    width: 100%;
    position: relative;
    overflow: hidden
}

.katex .halfarrow-left {
    position: absolute;
    left: 0;
    width: 50.2%;
    overflow: hidden
}

.katex .halfarrow-right {
    position: absolute;
    right: 0;
    width: 50.2%;
    overflow: hidden
}

.katex .brace-left {
    position: absolute;
    left: 0;
    width: 25.1%;
    overflow: hidden
}

.katex .brace-center {
    position: absolute;
    left: 25%;
    width: 50%;
    overflow: hidden
}

.katex .brace-right {
    position: absolute;
    right: 0;
    width: 25.1%;
    overflow: hidden
}

.katex .x-arrow-pad {
    padding: 0 .5em
}

.katex .cd-arrow-pad {
    padding: 0 .55556em 0 .27778em
}

.katex .mover,
.katex .munder,
.katex .x-arrow {
    text-align: center
}

.katex .boxpad {
    padding: 0 .3em
}

.katex .fbox,
.katex .fcolorbox {
    box-sizing: border-box;
    border: .04em solid
}

.katex .cancel-pad {
    padding: 0 .2em
}

.katex .cancel-lap {
    margin-left: -.2em;
    margin-right: -.2em
}

.katex .sout {
    border-bottom-style: solid;
    border-bottom-width: .08em
}

.katex .angl {
    box-sizing: border-box;
    border-top: .049em solid;
    border-right: .049em solid;
    margin-right: .03889em
}

.katex .anglpad {
    padding: 0 .03889em
}

.katex .eqn-num::before {
    counter-increment: katexEqnNo;
    content: "(" counter(katexEqnNo) ")"
}

.katex .mml-eqn-num::before {
    counter-increment: mmlEqnNo;
    content: "(" counter(mmlEqnNo) ")"
}

.katex .mtr-glue {
    width: 50%
}

.katex .cd-vert-arrow {
    display: inline-block;
    position: relative
}

.katex .cd-label-left {
    display: inline-block;
    position: absolute;
    right: calc(50% + .3em);
    text-align: left
}

.katex .cd-label-right {
    display: inline-block;
    position: absolute;
    left: calc(50% + .3em);
    text-align: right
}

.katex-display {
    display: block;
    margin: 1em 0;
    text-align: center
}

.katex-display>.katex {
    display: block;
    text-align: center;
    white-space: nowrap
}

.katex-display>.katex>.katex-html {
    display: block;
    position: relative
}

.katex-display>.katex>.katex-html>.tag {
    position: absolute;
    right: 0
}

.katex-display.leqno>.katex>.katex-html>.tag {
    left: 0;
    right: auto
}

.katex-display.fleqn>.katex {
    text-align: left;
    padding-left: 2em
}

body {
    counter-reset: katexEqnNo mmlEqnNo
}

.quill {
    display: flex;
    flex-direction: column
}

.ql-container {
    flex: 1;
    position: relative;
    font-family: var(--bs-font-sans-serif);
    font-size: 1rem;
    margin: 0;
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.ql-container a {
    color: #2196f3
}

.ql-container.ql-disabled .ql-tooltip {
    visibility: hidden
}

.ql-container.ql-disabled .ql-editor ul[data-checked]>li::before {
    pointer-events: none
}

.ql-editor {
    height: 100%;
    line-height: 1.5;
    padding: 1rem 1.25rem;
    outline: 0;
    text-align: left;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-y: auto;
    tab-size: 4;
    -moz-tab-size: 4;
    counter-reset: list-0
}

.ql-editor>* {
    cursor: text
}

.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor ol,
.ql-editor p,
.ql-editor pre {
    margin: 0;
    padding: 0
}

.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor p {
    counter-reset: list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor table {
    border-collapse: collapse
}

.ql-editor td {
    padding: .75rem .75rem;
    border: 1px solid var(--bs-bg-level-3);
    border-radius: .3rem
}

.ql-editor ol,
.ql-editor ul {
    padding-left: 1.5em
}

.ql-editor li {
    position: relative;
    padding-left: 1.5em;
    list-style-type: none
}

.ql-editor li>.ql-ui:before {
    display: inline-block;
    width: 1.25em;
    margin-left: -1.5em;
    margin-right: .25em;
    text-align: right;
    white-space: nowrap;
    color: var(--bs-text-level-1)
}

.ql-editor ul>li::before {
    content: "•"
}

.ql-editor ul[data-checked=false],
.ql-editor ul[data-checked=true] {
    pointer-events: none
}

.ql-editor ul[data-checked=false]>li *,
.ql-editor ul[data-checked=true]>li * {
    pointer-events: all
}

.ql-editor ul[data-checked=false]>li::before,
.ql-editor ul[data-checked=true]>li::before {
    cursor: pointer;
    pointer-events: all
}

.ql-editor ul[data-checked=true]>li::before {
    content: "☑"
}

.ql-editor ul[data-checked=false]>li::before {
    content: "☐"
}

.ql-editor li::before {
    display: inline-block;
    white-space: nowrap;
    width: 1.25em
}

.ql-editor li:not(.ql-direction-rtl)::before {
    margin-left: -1.5em;
    margin-right: .25em;
    text-align: right
}

.ql-editor li.ql-direction-rtl::before {
    margin-left: .25em;
    margin-right: -1.5em
}

.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 1.5em
}

.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
    padding-right: 1.5em
}

.ql-editor ol li {
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
    counter-increment: list-0
}

.ql-editor ol li:before {
    content: counter(list-0, decimal) ". "
}

.ql-editor ol li.ql-indent-1 {
    counter-increment: list-1
}

.ql-editor ol li.ql-indent-1:before {
    content: counter(list-1, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-1 {
    counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-2 {
    counter-increment: list-2
}

.ql-editor ol li.ql-indent-2:before {
    content: counter(list-2, lower-roman) ". "
}

.ql-editor ol li.ql-indent-2 {
    counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-3 {
    counter-increment: list-3
}

.ql-editor ol li.ql-indent-3:before {
    content: counter(list-3, decimal) ". "
}

.ql-editor ol li.ql-indent-3 {
    counter-reset: list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-4 {
    counter-increment: list-4
}

.ql-editor ol li.ql-indent-4:before {
    content: counter(list-4, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-4 {
    counter-reset: list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-5 {
    counter-increment: list-5
}

.ql-editor ol li.ql-indent-5:before {
    content: counter(list-5, lower-roman) ". "
}

.ql-editor ol li.ql-indent-5 {
    counter-reset: list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-6 {
    counter-increment: list-6
}

.ql-editor ol li.ql-indent-6:before {
    content: counter(list-6, decimal) ". "
}

.ql-editor ol li.ql-indent-6 {
    counter-reset: list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-7 {
    counter-increment: list-7
}

.ql-editor ol li.ql-indent-7:before {
    content: counter(list-7, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-7 {
    counter-reset: list-8 list-9
}

.ql-editor ol li.ql-indent-8 {
    counter-increment: list-8
}

.ql-editor ol li.ql-indent-8:before {
    content: counter(list-8, lower-roman) ". "
}

.ql-editor ol li.ql-indent-8 {
    counter-reset: list-9
}

.ql-editor ol li.ql-indent-9 {
    counter-increment: list-9
}

.ql-editor ol li.ql-indent-9:before {
    content: counter(list-9, decimal) ". "
}

.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 3em
}

.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 4.5em
}

.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-right: 3em
}

.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-right: 4.5em
}

.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 6em
}

.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 7.5em
}

.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-right: 6em
}

.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-right: 7.5em
}

.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
    padding-left: 9em
}

.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
    padding-left: 10.5em
}

.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-right: 9em
}

.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-right: 10.5em
}

.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
    padding-left: 12em
}

.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
    padding-left: 13.5em
}

.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-right: 12em
}

.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-right: 13.5em
}

.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
    padding-left: 15em
}

.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
    padding-left: 16.5em
}

.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-right: 15em
}

.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-right: 16.5em
}

.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
    padding-left: 18em
}

.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
    padding-left: 19.5em
}

.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-right: 18em
}

.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-right: 19.5em
}

.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
    padding-left: 21em
}

.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
    padding-left: 22.5em
}

.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-right: 21em
}

.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-right: 22.5em
}

.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
    padding-left: 24em
}

.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
    padding-left: 25.5em
}

.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-right: 24em
}

.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-right: 25.5em
}

.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
    padding-left: 27em
}

.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
    padding-left: 28.5em
}

.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-right: 27em
}

.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-right: 28.5em
}

.ql-editor table {
    table-layout: fixed;
    width: 100%
}

.ql-editor table td {
    outline: 0
}

.ql-editor h1 {
    font-size: 2.5rem
}

.ql-editor h2 {
    font-size: 2rem
}

.ql-editor h3 {
    font-size: 1.75rem
}

.ql-editor h4 {
    font-size: 1.5rem
}

.ql-editor h5 {
    font-size: 1.25rem
}

.ql-editor h6 {
    font-size: 1rem
}

.ql-editor img {
    max-width: 100%
}

.ql-editor a {
    text-decoration: underline
}

.ql-editor blockquote {
    border-left: .25rem solid #bdbdbd;
    padding-left: .75rem;
    margin-bottom: .5rem;
    margin-top: .5rem
}

.ql-editor .ql-code-block-container {
    position: relative;
    font-family: "Roboto Mono", monospace;
    background: #212121;
    padding: .25rem .5rem;
    margin-bottom: .5rem;
    margin-top: .5rem;
    overflow: visible;
    border-radius: .25rem
}

.ql-editor .ql-code-block-container .ql-ui {
    right: .5rem;
    top: .5rem
}

.ql-editor .ql-video {
    display: block;
    max-width: 100%
}

.ql-editor .ql-video.ql-align-center {
    margin: 0 auto
}

.ql-editor .ql-video.ql-align-right {
    margin: 0 0 0 auto
}

.ql-editor .ql-bg-black {
    background: #000
}

.ql-editor .ql-bg-red {
    background: #f44336
}

.ql-editor .ql-bg-orange {
    background: #ff9800
}

.ql-editor .ql-bg-yellow {
    background: #ffeb3b
}

.ql-editor .ql-bg-green {
    background: #4caf50
}

.ql-editor .ql-bg-blue {
    background: #2196f3
}

.ql-editor .ql-bg-purple {
    background: #9c27b0
}

.ql-editor .ql-color-white {
    color: #fff
}

.ql-editor .ql-color-red {
    color: #f44336
}

.ql-editor .ql-color-orange {
    color: #ff9800
}

.ql-editor .ql-color-yellow {
    color: #ffeb3b
}

.ql-editor .ql-color-green {
    color: #4caf50
}

.ql-editor .ql-color-blue {
    color: #2196f3
}

.ql-editor .ql-color-purple {
    color: #9c27b0
}

.ql-editor .ql-font-serif {
    font-family: Poppins, sans-serif
}

.ql-editor .ql-font-monospace {
    font-family: "Roboto Mono", monospace
}

.ql-editor .ql-size-small {
    font-size: .8rem
}

.ql-editor .ql-size-large {
    font-size: 1.6rem
}

.ql-editor .ql-size-huge {
    font-size: 2.4rem
}

.ql-editor .ql-align-center {
    text-align: center
}

.ql-editor .ql-align-justify {
    text-align: justify
}

.ql-editor .ql-align-right {
    text-align: right
}

.ql-editor .ql-ui {
    position: absolute
}

.ql-editor.ql-blank::before {
    content: attr(data-placeholder);
    position: absolute;
    left: 1.25rem;
    right: 1.25rem;
    pointer-events: none;
    color: var(--bs-text-level-1)
}

.ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before {
    content: attr(data-label)
}

.ql-picker.ql-header {
    width: 8rem
}

.ql-picker.ql-header .ql-picker-item::before,
.ql-picker.ql-header .ql-picker-label::before {
    content: "Normal"
}

.ql-picker.ql-header .ql-picker-item[data-value="1"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="1"]::before {
    content: "Heading 1"
}

.ql-picker.ql-header .ql-picker-item[data-value="2"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="2"]::before {
    content: "Heading 2"
}

.ql-picker.ql-header .ql-picker-item[data-value="3"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="3"]::before {
    content: "Heading 3"
}

.ql-picker.ql-header .ql-picker-item[data-value="4"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="4"]::before {
    content: "Heading 4"
}

.ql-picker.ql-header .ql-picker-item[data-value="5"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="5"]::before {
    content: "Heading 5"
}

.ql-picker.ql-header .ql-picker-item[data-value="6"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="6"]::before {
    content: "Heading 6"
}

.ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    font-size: 2.5rem
}

.ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    font-size: 2rem
}

.ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    font-size: 1.75rem
}

.ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    font-size: 1.5rem
}

.ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    font-size: 1.25rem
}

.ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    font-size: 1rem
}

.ql-picker.ql-font {
    width: 8rem
}

.ql-picker.ql-font .ql-picker-item::before,
.ql-picker.ql-font .ql-picker-label::before {
    content: "Sans Serif"
}

.ql-picker.ql-font .ql-picker-item[data-value=serif]::before,
.ql-picker.ql-font .ql-picker-label[data-value=serif]::before {
    content: "Serif"
}

.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before,
.ql-picker.ql-font .ql-picker-label[data-value=monospace]::before {
    content: "Monospace"
}

.ql-picker.ql-font .ql-picker-item[data-value=poppins]::before,
.ql-picker.ql-font .ql-picker-label[data-value=poppins]::before {
    content: "Poppins"
}

.ql-picker.ql-font .ql-picker-item[data-value="roboto mono"]::before,
.ql-picker.ql-font .ql-picker-label[data-value="roboto mono"]::before {
    content: "Roboto Mono"
}

.ql-picker.ql-font .ql-picker-item[data-value="sans serif"]::before {
    font-family: Poppins, sans-serif
}

.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
    font-family: "Roboto Mono", monospace
}

.ql-picker.ql-font .ql-picker-item[data-value=poppins]::before {
    font-family: Poppins, sans-serif
}

.ql-picker.ql-font .ql-picker-item[data-value="roboto mono"]::before {
    font-family: "Roboto Mono", monospace
}

.ql-picker.ql-size {
    width: 8rem
}

.ql-picker.ql-size .ql-picker-item::before,
.ql-picker.ql-size .ql-picker-label::before {
    content: "Normal"
}

.ql-picker.ql-size .ql-picker-item[data-value=small]::before,
.ql-picker.ql-size .ql-picker-label[data-value=small]::before {
    content: "Small"
}

.ql-picker.ql-size .ql-picker-item[data-value=large]::before,
.ql-picker.ql-size .ql-picker-label[data-value=large]::before {
    content: "Large"
}

.ql-picker.ql-size .ql-picker-item[data-value=huge]::before,
.ql-picker.ql-size .ql-picker-label[data-value=huge]::before {
    content: "Huge"
}

.ql-picker.ql-size .ql-picker-item[data-value=small]::before {
    font-size: .8rem
}

.ql-picker.ql-size .ql-picker-item[data-value=large]::before {
    font-size: 1.6rem
}

.ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
    font-size: 2.4rem
}

.ql-stroke {
    fill: none;
    stroke: currentColor;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 2
}

.ql-stroke-miter {
    fill: none;
    stroke: currentColor;
    stroke-miterlimit: 10;
    stroke-width: 2
}

.ql-fill {
    fill: currentColor
}

.ql-empty {
    fill: none
}

.ql-even {
    fill-rule: evenodd
}

.ql-thin {
    stroke-width: 1
}

.ql-transparent {
    opacity: .4
}

.ql-direction svg:last-child {
    display: none
}

.ql-direction.ql-active svg:last-child {
    display: inline
}

.ql-direction.ql-active svg:first-child {
    display: none
}

.ql-hidden {
    display: none !important
}

.ql-out-bottom,
.ql-out-top {
    visibility: hidden
}

.ql-clipboard {
    position: absolute;
    height: 1px;
    overflow-y: hidden;
    top: 50%;
    left: -100000px
}

.ql-clipboard p {
    margin: 0;
    padding: 0
}

.ql-toolbar.ql-snow {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    padding: .5rem .5rem;
    border: 1px solid var(--bs-border-color);
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem
}

.ql-toolbar.ql-snow button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: calc(1.5em + (.75rem + 2px));
    width: calc(1.5em + (.75rem + 2px));
    color: var(--bs-text-level-1);
    background: 0 0;
    margin: .125rem;
    padding: 0;
    border: 0;
    outline: 0;
    cursor: pointer;
    border-radius: .25rem
}

.ql-toolbar.ql-snow button:focus,
.ql-toolbar.ql-snow button:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-toolbar.ql-snow button.ql-active {
    color: #fff;
    background: #2196f3
}

.ql-toolbar.ql-snow svg {
    height: 60%;
    color: inherit
}

.ql-toolbar.ql-snow .ql-formats {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    margin-right: .5rem
}

.ql-toolbar.ql-snow .ql-picker {
    position: relative;
    display: inline-block;
    width: calc(1.5em + (.75rem + 2px));
    height: calc(1.5em + (.75rem + 2px));
    margin: 0 .125rem
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    z-index: 2;
    color: #fff;
    background: #2196f3
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
    display: block;
    top: 100%;
    z-index: 1
}

.ql-toolbar.ql-snow .ql-picker.ql-font,
.ql-toolbar.ql-snow .ql-picker.ql-header,
.ql-toolbar.ql-snow .ql-picker.ql-size {
    width: 8rem
}

.ql-toolbar.ql-snow .ql-picker-label {
    position: relative;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    color: var(--bs-text-level-1);
    background: 0 0;
    font-size: 1rem;
    font-weight: 400;
    padding: 0 .5rem;
    outline: 0;
    cursor: pointer;
    border-radius: .25rem
}

.ql-toolbar.ql-snow .ql-picker-label:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-toolbar.ql-snow .ql-picker-options {
    position: absolute;
    display: none;
    min-width: 100%;
    padding: .25rem 0;
    white-space: nowrap;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.ql-toolbar.ql-snow .ql-picker-item {
    display: flex;
    align-items: center;
    color: var(--bs-text-level-2);
    padding: .5rem 1rem;
    outline: 0;
    cursor: pointer
}

.ql-toolbar.ql-snow .ql-picker-item:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-toolbar.ql-snow .ql-color-picker .ql-picker-label,
.ql-toolbar.ql-snow .ql-icon-picker .ql-picker-label {
    justify-content: center;
    padding: 0
}

.ql-toolbar.ql-snow .ql-icon-picker .ql-picker-item {
    justify-content: center;
    height: calc(1.5em + (.75rem + 2px));
    width: calc(1.5em + (.75rem + 2px));
    padding: 0
}

.ql-toolbar.ql-snow .ql-color-picker .ql-picker-options {
    padding: .25rem .5rem;
    width: 17rem
}

.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item {
    height: 1.25rem;
    width: 1.25rem;
    margin: .25rem;
    padding: 0;
    float: left
}

.ql-toolbar.ql-snow .ql-color-picker.ql-background .ql-picker-item {
    background: #fff
}

.ql-toolbar.ql-snow .ql-color-picker.ql-color .ql-picker-item {
    background: #000
}

.ql-toolbar.ql-snow+.ql-container {
    border-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ql-toolbar.ql-snow input.ql-image[type=file] {
    display: none
}

.ql-snow .ql-tooltip {
    position: absolute;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: .5rem .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    white-space: nowrap;
    transform: translateY(1rem);
    border-radius: .25rem
}

.ql-snow .ql-tooltip::before {
    content: "Visit URL:";
    margin-right: .5rem
}

.ql-snow .ql-tooltip input[type=text] {
    display: none;
    width: 15rem;
    height: calc(1.5em + (.75rem + 2px));
    font-size: 1rem;
    padding: .375rem .75rem;
    margin: 0;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-bg-level-3);
    border-radius: .25rem
}

.ql-snow .ql-tooltip input[type=text]:focus {
    outline: 0;
    border-color: #2196f3
}

.ql-snow .ql-tooltip input[type=text]::placeholder {
    opacity: 1;
    color: var(--bs-text-level-1)
}

.ql-snow .ql-tooltip a {
    text-decoration: none;
    cursor: pointer
}

.ql-snow .ql-tooltip a:focus,
.ql-snow .ql-tooltip a:hover {
    color: #2196f3
}

.ql-snow .ql-tooltip a.ql-preview {
    display: inline-block;
    max-width: 20rem;
    text-overflow: ellipsis;
    overflow-x: hidden
}

.ql-snow .ql-tooltip a.ql-action::after {
    content: "Edit";
    border-right: 1px solid var(--bs-border-color);
    padding-right: .5rem;
    margin-left: .5rem
}

.ql-snow .ql-tooltip a.ql-remove::before {
    content: "Remove";
    margin-left: .5rem
}

.ql-snow .ql-tooltip.ql-flip {
    transform: translateY(-1rem)
}

.ql-snow .ql-tooltip.ql-editing a.ql-preview,
.ql-snow .ql-tooltip.ql-editing a.ql-remove {
    display: none
}

.ql-snow .ql-tooltip.ql-editing input[type=text] {
    display: inline-block
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    content: "Save";
    border-right: 0;
    padding-right: 0
}

.ql-snow .ql-tooltip[data-mode=link]::before {
    content: "Enter link:"
}

.ql-snow .ql-tooltip[data-mode=formula]::before {
    content: "Enter formula:"
}

.ql-snow .ql-tooltip[data-mode=video]::before {
    content: "Enter video:"
}

.quill {
    display: flex;
    flex-direction: column
}

.ql-container {
    flex: 1;
    position: relative;
    font-family: var(--bs-font-sans-serif);
    font-size: 1rem;
    margin: 0;
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.ql-container a {
    color: #2196f3
}

.ql-container.ql-disabled .ql-tooltip {
    visibility: hidden
}

.ql-container.ql-disabled .ql-editor ul[data-checked]>li::before {
    pointer-events: none
}

.ql-editor {
    height: 100%;
    line-height: 1.5;
    padding: 1rem 1.25rem;
    outline: 0;
    text-align: left;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-y: auto;
    tab-size: 4;
    -moz-tab-size: 4;
    counter-reset: list-0
}

.ql-editor>* {
    cursor: text
}

.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor ol,
.ql-editor p,
.ql-editor pre {
    margin: 0;
    padding: 0
}

.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.ql-editor p {
    counter-reset: list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor table {
    border-collapse: collapse
}

.ql-editor td {
    padding: .75rem .75rem;
    border: 1px solid var(--bs-bg-level-3);
    border-radius: .3rem
}

.ql-editor ol,
.ql-editor ul {
    padding-left: 1.5em
}

.ql-editor li {
    position: relative;
    padding-left: 1.5em;
    list-style-type: none
}

.ql-editor li>.ql-ui:before {
    display: inline-block;
    width: 1.25em;
    margin-left: -1.5em;
    margin-right: .25em;
    text-align: right;
    white-space: nowrap;
    color: var(--bs-text-level-1)
}

.ql-editor ul>li::before {
    content: "•"
}

.ql-editor ul[data-checked=false],
.ql-editor ul[data-checked=true] {
    pointer-events: none
}

.ql-editor ul[data-checked=false]>li *,
.ql-editor ul[data-checked=true]>li * {
    pointer-events: all
}

.ql-editor ul[data-checked=false]>li::before,
.ql-editor ul[data-checked=true]>li::before {
    cursor: pointer;
    pointer-events: all
}

.ql-editor ul[data-checked=true]>li::before {
    content: "☑"
}

.ql-editor ul[data-checked=false]>li::before {
    content: "☐"
}

.ql-editor li::before {
    display: inline-block;
    white-space: nowrap;
    width: 1.25em
}

.ql-editor li:not(.ql-direction-rtl)::before {
    margin-left: -1.5em;
    margin-right: .25em;
    text-align: right
}

.ql-editor li.ql-direction-rtl::before {
    margin-left: .25em;
    margin-right: -1.5em
}

.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
    padding-left: 1.5em
}

.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
    padding-right: 1.5em
}

.ql-editor ol li {
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
    counter-increment: list-0
}

.ql-editor ol li:before {
    content: counter(list-0, decimal) ". "
}

.ql-editor ol li.ql-indent-1 {
    counter-increment: list-1
}

.ql-editor ol li.ql-indent-1:before {
    content: counter(list-1, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-1 {
    counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-2 {
    counter-increment: list-2
}

.ql-editor ol li.ql-indent-2:before {
    content: counter(list-2, lower-roman) ". "
}

.ql-editor ol li.ql-indent-2 {
    counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-3 {
    counter-increment: list-3
}

.ql-editor ol li.ql-indent-3:before {
    content: counter(list-3, decimal) ". "
}

.ql-editor ol li.ql-indent-3 {
    counter-reset: list-4 list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-4 {
    counter-increment: list-4
}

.ql-editor ol li.ql-indent-4:before {
    content: counter(list-4, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-4 {
    counter-reset: list-5 list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-5 {
    counter-increment: list-5
}

.ql-editor ol li.ql-indent-5:before {
    content: counter(list-5, lower-roman) ". "
}

.ql-editor ol li.ql-indent-5 {
    counter-reset: list-6 list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-6 {
    counter-increment: list-6
}

.ql-editor ol li.ql-indent-6:before {
    content: counter(list-6, decimal) ". "
}

.ql-editor ol li.ql-indent-6 {
    counter-reset: list-7 list-8 list-9
}

.ql-editor ol li.ql-indent-7 {
    counter-increment: list-7
}

.ql-editor ol li.ql-indent-7:before {
    content: counter(list-7, lower-alpha) ". "
}

.ql-editor ol li.ql-indent-7 {
    counter-reset: list-8 list-9
}

.ql-editor ol li.ql-indent-8 {
    counter-increment: list-8
}

.ql-editor ol li.ql-indent-8:before {
    content: counter(list-8, lower-roman) ". "
}

.ql-editor ol li.ql-indent-8 {
    counter-reset: list-9
}

.ql-editor ol li.ql-indent-9 {
    counter-increment: list-9
}

.ql-editor ol li.ql-indent-9:before {
    content: counter(list-9, decimal) ". "
}

.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 3em
}

.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
    padding-left: 4.5em
}

.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-right: 3em
}

.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
    padding-right: 4.5em
}

.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 6em
}

.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
    padding-left: 7.5em
}

.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-right: 6em
}

.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
    padding-right: 7.5em
}

.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
    padding-left: 9em
}

.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
    padding-left: 10.5em
}

.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-right: 9em
}

.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
    padding-right: 10.5em
}

.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
    padding-left: 12em
}

.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
    padding-left: 13.5em
}

.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-right: 12em
}

.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
    padding-right: 13.5em
}

.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
    padding-left: 15em
}

.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
    padding-left: 16.5em
}

.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-right: 15em
}

.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
    padding-right: 16.5em
}

.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
    padding-left: 18em
}

.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
    padding-left: 19.5em
}

.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-right: 18em
}

.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
    padding-right: 19.5em
}

.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
    padding-left: 21em
}

.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
    padding-left: 22.5em
}

.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-right: 21em
}

.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
    padding-right: 22.5em
}

.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
    padding-left: 24em
}

.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
    padding-left: 25.5em
}

.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-right: 24em
}

.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
    padding-right: 25.5em
}

.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
    padding-left: 27em
}

.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
    padding-left: 28.5em
}

.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-right: 27em
}

.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
    padding-right: 28.5em
}

.ql-editor table {
    table-layout: fixed;
    width: 100%
}

.ql-editor table td {
    outline: 0
}

.ql-editor h1 {
    font-size: 2.5rem
}

.ql-editor h2 {
    font-size: 2rem
}

.ql-editor h3 {
    font-size: 1.75rem
}

.ql-editor h4 {
    font-size: 1.5rem
}

.ql-editor h5 {
    font-size: 1.25rem
}

.ql-editor h6 {
    font-size: 1rem
}

.ql-editor img {
    max-width: 100%
}

.ql-editor a {
    text-decoration: underline
}

.ql-editor blockquote {
    border-left: .25rem solid #bdbdbd;
    padding-left: .75rem;
    margin-bottom: .5rem;
    margin-top: .5rem
}

.ql-editor .ql-code-block-container {
    position: relative;
    font-family: "Roboto Mono", monospace;
    background: #212121;
    padding: .25rem .5rem;
    margin-bottom: .5rem;
    margin-top: .5rem;
    overflow: visible;
    border-radius: .25rem
}

.ql-editor .ql-code-block-container .ql-ui {
    right: .5rem;
    top: .5rem
}

.ql-editor .ql-video {
    display: block;
    max-width: 100%
}

.ql-editor .ql-video.ql-align-center {
    margin: 0 auto
}

.ql-editor .ql-video.ql-align-right {
    margin: 0 0 0 auto
}

.ql-editor .ql-bg-black {
    background: #000
}

.ql-editor .ql-bg-red {
    background: #f44336
}

.ql-editor .ql-bg-orange {
    background: #ff9800
}

.ql-editor .ql-bg-yellow {
    background: #ffeb3b
}

.ql-editor .ql-bg-green {
    background: #4caf50
}

.ql-editor .ql-bg-blue {
    background: #2196f3
}

.ql-editor .ql-bg-purple {
    background: #9c27b0
}

.ql-editor .ql-color-white {
    color: #fff
}

.ql-editor .ql-color-red {
    color: #f44336
}

.ql-editor .ql-color-orange {
    color: #ff9800
}

.ql-editor .ql-color-yellow {
    color: #ffeb3b
}

.ql-editor .ql-color-green {
    color: #4caf50
}

.ql-editor .ql-color-blue {
    color: #2196f3
}

.ql-editor .ql-color-purple {
    color: #9c27b0
}

.ql-editor .ql-font-serif {
    font-family: Poppins, sans-serif
}

.ql-editor .ql-font-monospace {
    font-family: "Roboto Mono", monospace
}

.ql-editor .ql-size-small {
    font-size: .8rem
}

.ql-editor .ql-size-large {
    font-size: 1.6rem
}

.ql-editor .ql-size-huge {
    font-size: 2.4rem
}

.ql-editor .ql-align-center {
    text-align: center
}

.ql-editor .ql-align-justify {
    text-align: justify
}

.ql-editor .ql-align-right {
    text-align: right
}

.ql-editor .ql-ui {
    position: absolute
}

.ql-editor.ql-blank::before {
    content: attr(data-placeholder);
    position: absolute;
    left: 1.25rem;
    right: 1.25rem;
    pointer-events: none;
    color: var(--bs-text-level-1)
}

.ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""])::before,
.ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""])::before,
.ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""])::before {
    content: attr(data-label)
}

.ql-picker.ql-header {
    width: 8rem
}

.ql-picker.ql-header .ql-picker-item::before,
.ql-picker.ql-header .ql-picker-label::before {
    content: "Normal"
}

.ql-picker.ql-header .ql-picker-item[data-value="1"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="1"]::before {
    content: "Heading 1"
}

.ql-picker.ql-header .ql-picker-item[data-value="2"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="2"]::before {
    content: "Heading 2"
}

.ql-picker.ql-header .ql-picker-item[data-value="3"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="3"]::before {
    content: "Heading 3"
}

.ql-picker.ql-header .ql-picker-item[data-value="4"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="4"]::before {
    content: "Heading 4"
}

.ql-picker.ql-header .ql-picker-item[data-value="5"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="5"]::before {
    content: "Heading 5"
}

.ql-picker.ql-header .ql-picker-item[data-value="6"]::before,
.ql-picker.ql-header .ql-picker-label[data-value="6"]::before {
    content: "Heading 6"
}

.ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    font-size: 2.5rem
}

.ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    font-size: 2rem
}

.ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    font-size: 1.75rem
}

.ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    font-size: 1.5rem
}

.ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    font-size: 1.25rem
}

.ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    font-size: 1rem
}

.ql-picker.ql-font {
    width: 8rem
}

.ql-picker.ql-font .ql-picker-item::before,
.ql-picker.ql-font .ql-picker-label::before {
    content: "Sans Serif"
}

.ql-picker.ql-font .ql-picker-item[data-value=serif]::before,
.ql-picker.ql-font .ql-picker-label[data-value=serif]::before {
    content: "Serif"
}

.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before,
.ql-picker.ql-font .ql-picker-label[data-value=monospace]::before {
    content: "Monospace"
}

.ql-picker.ql-font .ql-picker-item[data-value=poppins]::before,
.ql-picker.ql-font .ql-picker-label[data-value=poppins]::before {
    content: "Poppins"
}

.ql-picker.ql-font .ql-picker-item[data-value="roboto mono"]::before,
.ql-picker.ql-font .ql-picker-label[data-value="roboto mono"]::before {
    content: "Roboto Mono"
}

.ql-picker.ql-font .ql-picker-item[data-value="sans serif"]::before {
    font-family: Poppins, sans-serif
}

.ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
    font-family: "Roboto Mono", monospace
}

.ql-picker.ql-font .ql-picker-item[data-value=poppins]::before {
    font-family: Poppins, sans-serif
}

.ql-picker.ql-font .ql-picker-item[data-value="roboto mono"]::before {
    font-family: "Roboto Mono", monospace
}

.ql-picker.ql-size {
    width: 8rem
}

.ql-picker.ql-size .ql-picker-item::before,
.ql-picker.ql-size .ql-picker-label::before {
    content: "Normal"
}

.ql-picker.ql-size .ql-picker-item[data-value=small]::before,
.ql-picker.ql-size .ql-picker-label[data-value=small]::before {
    content: "Small"
}

.ql-picker.ql-size .ql-picker-item[data-value=large]::before,
.ql-picker.ql-size .ql-picker-label[data-value=large]::before {
    content: "Large"
}

.ql-picker.ql-size .ql-picker-item[data-value=huge]::before,
.ql-picker.ql-size .ql-picker-label[data-value=huge]::before {
    content: "Huge"
}

.ql-picker.ql-size .ql-picker-item[data-value=small]::before {
    font-size: .8rem
}

.ql-picker.ql-size .ql-picker-item[data-value=large]::before {
    font-size: 1.6rem
}

.ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
    font-size: 2.4rem
}

.ql-stroke {
    fill: none;
    stroke: currentColor;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-width: 2
}

.ql-stroke-miter {
    fill: none;
    stroke: currentColor;
    stroke-miterlimit: 10;
    stroke-width: 2
}

.ql-fill {
    fill: currentColor
}

.ql-empty {
    fill: none
}

.ql-even {
    fill-rule: evenodd
}

.ql-thin {
    stroke-width: 1
}

.ql-transparent {
    opacity: .4
}

.ql-direction svg:last-child {
    display: none
}

.ql-direction.ql-active svg:last-child {
    display: inline
}

.ql-direction.ql-active svg:first-child {
    display: none
}

.ql-hidden {
    display: none !important
}

.ql-out-bottom,
.ql-out-top {
    visibility: hidden
}

.ql-clipboard {
    position: absolute;
    height: 1px;
    overflow-y: hidden;
    top: 50%;
    left: -100000px
}

.ql-clipboard p {
    margin: 0;
    padding: 0
}

.ql-bubble .ql-toolbar {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap
}

.ql-bubble .ql-toolbar button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: calc(1.5em + (.75rem + 2px));
    width: calc(1.5em + (.75rem + 2px));
    color: var(--bs-text-level-1);
    background: 0 0;
    margin: .125rem;
    padding: 0;
    border: 0;
    outline: 0;
    cursor: pointer;
    border-radius: .25rem
}

.ql-bubble .ql-toolbar button:focus,
.ql-bubble .ql-toolbar button:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-bubble .ql-toolbar button.ql-active {
    color: #fff;
    background: #2196f3
}

.ql-bubble .ql-toolbar svg {
    height: 60%;
    color: inherit
}

.ql-bubble .ql-toolbar .ql-formats {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap
}

.ql-bubble .ql-toolbar .ql-formats+.ql-formats {
    margin-left: .5rem
}

.ql-bubble .ql-picker {
    position: relative;
    display: inline-block;
    width: calc(1.5em + (.75rem + 2px));
    height: calc(1.5em + (.75rem + 2px));
    margin: 0 .125rem
}

.ql-bubble .ql-picker.ql-expanded .ql-picker-label {
    z-index: 2;
    color: #fff;
    background: #2196f3
}

.ql-bubble .ql-picker.ql-expanded .ql-picker-options {
    display: block;
    top: 100%;
    z-index: 1
}

.ql-bubble .ql-picker.ql-font,
.ql-bubble .ql-picker.ql-header,
.ql-bubble .ql-picker.ql-size {
    width: 8rem
}

.ql-bubble .ql-picker-label {
    position: relative;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    color: var(--bs-text-level-1);
    background: 0 0;
    font-size: 1rem;
    font-weight: 400;
    padding: 0 .5rem;
    outline: 0;
    cursor: pointer;
    border-radius: .25rem
}

.ql-bubble .ql-picker-label:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-bubble .ql-picker-options {
    position: absolute;
    display: none;
    min-width: 100%;
    padding: .25rem 0;
    white-space: nowrap;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.ql-bubble .ql-picker-item {
    display: flex;
    align-items: center;
    color: var(--bs-text-level-2);
    padding: .5rem 1rem;
    outline: 0;
    cursor: pointer
}

.ql-bubble .ql-picker-item:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.ql-bubble .ql-color-picker .ql-picker-label,
.ql-bubble .ql-icon-picker .ql-picker-label {
    justify-content: center;
    padding: 0
}

.ql-bubble .ql-icon-picker .ql-picker-item {
    justify-content: center;
    height: calc(1.5em + (.75rem + 2px));
    width: calc(1.5em + (.75rem + 2px));
    padding: 0
}

.ql-bubble .ql-color-picker .ql-picker-options {
    padding: .25rem .5rem;
    width: 17rem
}

.ql-bubble .ql-color-picker .ql-picker-item {
    height: 1.25rem;
    width: 1.25rem;
    margin: .25rem;
    padding: 0;
    float: left
}

.ql-bubble .ql-color-picker.ql-background .ql-picker-item {
    background: #fff
}

.ql-bubble .ql-color-picker.ql-color .ql-picker-item {
    background: #000
}

.ql-bubble+.ql-container {
    border-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ql-bubble input.ql-image[type=file] {
    display: none
}

.ql-bubble .ql-tooltip {
    z-index: 9999;
    position: absolute;
    padding: .5rem .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    border-radius: .25rem
}

.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow {
    top: -5px
}

.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow:after {
    border-bottom: 6px solid var(--bs-bg-level-2)
}

.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow {
    bottom: 0
}

.ql-bubble .ql-tooltip.ql-flip .ql-tooltip-arrow:after {
    border-top: 6px solid var(--bs-bg-level-2)
}

.ql-bubble .ql-tooltip.ql-editing .ql-tooltip-editor {
    display: block
}

.ql-bubble .ql-tooltip.ql-editing .ql-toolbar {
    display: none
}

.ql-bubble .ql-tooltip-arrow {
    position: absolute;
    margin-left: -6px;
    left: 50%
}

.ql-bubble .ql-tooltip-arrow:after {
    content: "";
    position: absolute;
    display: block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent
}

.ql-bubble .ql-tooltip-editor {
    display: none
}

.ql-bubble .ql-tooltip-editor input[type=text] {
    height: 100%;
    width: 100%;
    background: 0 0;
    color: var(--bs-text-level-3);
    font-size: 1rem;
    padding: .375rem .75rem;
    border: 0;
    outline: 0
}

.ql-bubble.ql-container:not(.ql-disabled) a {
    position: relative
}

.ql-bubble.ql-container:not(.ql-disabled) a:after,
.ql-bubble.ql-container:not(.ql-disabled) a:before {
    display: none;
    position: absolute;
    margin-left: 50%;
    left: 0;
    transform: translate(-50%, -100%)
}

.ql-bubble.ql-container:not(.ql-disabled) a:before {
    content: attr(href);
    font-size: 1rem;
    font-weight: 400;
    font-style: normal;
    padding: .5rem .75rem;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-border-color);
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    top: -.5rem;
    z-index: 1;
    border-radius: .25rem
}

.ql-bubble.ql-container:not(.ql-disabled) a:after {
    content: "";
    width: 0;
    height: 0;
    border-left: .5rem solid transparent;
    border-right: .5rem solid transparent;
    border-top: .5rem solid var(--bs-bg-level-2);
    top: 0
}

.ql-bubble.ql-container:not(.ql-disabled) a:hover:after,
.ql-bubble.ql-container:not(.ql-disabled) a:hover:before {
    display: block
}

.select2 {
    width: 100% !important
}

.select2-selection {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: calc(1.5em + (.75rem + 2px));
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-bg-level-3);
    outline: 0;
    user-select: none;
    cursor: pointer;
    border-radius: .25rem
}

.select2-selection__rendered {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: .375rem .75rem;
    margin: 0;
    list-style: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container {
    position: relative;
    display: inline-block
}

.select2-search__field {
    border-color: var(--bs-bg-level-3) !important
}

.select2-search--inline .select2-search__field {
    height: auto;
    background: 0 0;
    border: 0;
    padding: 0;
    margin: 0
}

.select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-selection__clear {
    position: absolute;
    color: var(--bs-text-level-1);
    font-size: 1rem;
    transform: translateY(-50%);
    top: 50%;
    cursor: pointer
}

.select2-selection__clear:hover {
    color: var(--bs-text-level-3)
}

.select2-container--open .select2-selection {
    border-color: #2196f3
}

.select2-container--open.select2-container--above .select2-selection {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-container--open.select2-container--below .select2-selection {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.select2-container--disabled .select2-selection {
    background: var(--bs-bg-level-2);
    border-color: var(--bs-bg-level-2);
    opacity: .65;
    cursor: default
}

.select2-hidden-accessible {
    display: none !important
}

.select2-selection--single .select2-selection__placeholder {
    color: var(--bs-text-level-1)
}

.select2-selection--single .select2-selection__arrow {
    width: 2rem;
    text-align: center;
    position: relative
}

.select2-selection--single .select2-selection__arrow:after {
    content: "";
    position: relative;
    display: inline-block;
    color: inherit;
    width: .55rem;
    height: .55rem;
    border: solid currentColor;
    border-width: 0 2px 2px 0;
    border-bottom-right-radius: .1rem;
    transition: transform ease-in-out .2s, top ease-in-out .2s, bottom ease-in-out .2s;
    transform: rotate(45deg);
    bottom: 2px
}

@media (prefers-reduced-motion:reduce) {
    .select2-selection--single .select2-selection__arrow:after {
        transition: none
    }
}

.select2-selection--single .select2-selection__clear {
    right: 2rem
}

.select2-container--open.select2-container--above .select2-selection__arrow::after {
    transform: rotate(-135deg);
    bottom: 2px
}

.select2-container--disabled .select2-selection__clear {
    display: none
}

.select2-selection--multiple {
    height: auto;
    min-height: calc(1.5em + (.75rem + 2px))
}

.select2-selection--multiple .select2-selection__choice {
    font-size: 1rem;
    font-weight: 400;
    padding: .25rem .5rem;
    margin-top: .25rem;
    margin-bottom: .25rem;
    margin-right: .5rem;
    cursor: default;
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-2);
    border: 1px solid var(--bs-bg-level-3);
    border-radius: .25rem
}

.select2-selection--multiple .select2-selection__choice__remove {
    display: inline-block;
    color: var(--bs-text-level-1);
    margin-right: .25rem;
    cursor: pointer
}

.select2-selection--multiple .select2-selection__choice__remove:hover {
    color: var(--bs-text-level-3)
}

.select2-selection--multiple .select2-selection__clear {
    right: .75rem
}

.select2-container--disabled .select2-selection__choice__remove {
    display: none
}

.select2-dropdown {
    position: absolute;
    display: block;
    width: 100%;
    z-index: 1120;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-bg-level-3);
    border-radius: .25rem
}

.select2-results {
    display: block
}

.select2-results>.select2-results__options {
    max-height: 15rem;
    overflow-y: auto
}

.select2-results__options {
    list-style: none;
    padding: 0;
    margin: 0
}

.select2-results__option {
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-1);
    font-size: 1rem;
    font-weight: 400;
    padding: .5rem 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none
}

.select2-results__option[role=group] {
    padding: 0
}

.select2-results__option.select2-results__option--highlighted {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.select2-results__option[aria-selected=true] {
    color: #fff;
    background: #2196f3
}

.select2-results__option[aria-disabled=true] {
    color: var(--bs-text-level-1);
    background: var(--bs-bg-level-2);
    opacity: .65
}

.select2-results__option .select2-results__option {
    padding-left: 1.5rem
}

.select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0
}

.select2-results__group {
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    font-size: 1rem;
    font-weight: 500;
    padding: .5rem 1rem;
    cursor: default
}

.select2-search--dropdown {
    display: block;
    padding: .75rem
}

.select2-search--dropdown.select2-search--hide {
    display: none
}

.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

[data-simplebar] {
    position: relative;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start
}

.simplebar-wrapper {
    overflow: hidden;
    width: inherit;
    height: inherit;
    max-width: inherit;
    max-height: inherit
}

.simplebar-mask {
    direction: inherit;
    position: absolute;
    overflow: hidden;
    padding: 0;
    margin: 0;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: auto !important;
    height: auto !important;
    z-index: 0
}

.simplebar-offset {
    direction: inherit !important;
    box-sizing: inherit !important;
    resize: none !important;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 0;
    margin: 0;
    -webkit-overflow-scrolling: touch
}

.simplebar-content-wrapper {
    direction: inherit;
    box-sizing: border-box !important;
    position: relative;
    display: block;
    height: 100%;
    width: auto;
    max-width: 100%;
    max-height: 100%;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0
}

.simplebar-content::after,
.simplebar-content::before {
    content: " ";
    display: table
}

.simplebar-placeholder {
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    pointer-events: none
}

.simplebar-height-auto-observer-wrapper {
    box-sizing: inherit !important;
    height: 100%;
    width: 100%;
    max-width: 1px;
    position: relative;
    float: left;
    max-height: 1px;
    overflow: hidden;
    z-index: -1;
    padding: 0;
    margin: 0;
    pointer-events: none;
    flex-grow: inherit;
    flex-shrink: 0;
    flex-basis: 0
}

.simplebar-height-auto-observer {
    box-sizing: inherit;
    display: block;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 1000%;
    width: 1000%;
    min-height: 1px;
    min-width: 1px;
    overflow: hidden;
    pointer-events: none;
    z-index: -1
}

.simplebar-track {
    z-index: 1;
    position: absolute;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden
}

[data-simplebar].simplebar-dragging .simplebar-content {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none
}

[data-simplebar].simplebar-dragging .simplebar-track {
    pointer-events: all
}

.simplebar-scrollbar {
    position: absolute;
    left: 0;
    right: 0;
    min-height: 1.25rem
}

.simplebar-scrollbar:before {
    position: absolute;
    content: "";
    opacity: 0;
    background: #bdbdbd;
    left: .4rem;
    right: .4rem;
    border-radius: .2rem;
    transition: opacity .2s linear
}

@media (prefers-reduced-motion:reduce) {
    .simplebar-scrollbar:before {
        transition: none
    }
}

.simplebar-scrollbar.simplebar-visible:before {
    opacity: .65;
    transition: opacity 0s linear
}

.simplebar-track.simplebar-vertical {
    top: 0;
    width: 1.25rem
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
    top: .4rem;
    bottom: .4rem
}

[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
    right: auto;
    left: 0
}

.simplebar-track.simplebar-horizontal {
    left: 0;
    height: 1.25rem
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
    right: auto;
    left: 0;
    top: .4rem;
    height: .85rem;
    min-height: 0;
    min-width: 1.25rem;
    width: auto
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
    height: 100%;
    left: .4rem;
    right: .4rem
}

.hs-dummy-scrollbar-size {
    direction: rtl;
    position: fixed;
    opacity: 0;
    visibility: hidden;
    height: 500px;
    width: 500px;
    overflow-y: hidden;
    overflow-x: scroll
}

.simplebar-hide-scrollbar {
    position: fixed;
    left: 0;
    visibility: hidden;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none
}

.simplebar-solid-track .simplebar-track {
    background: var(--bs-bg-level-2)
}

.simplebar-hover-track .simplebar-hover.simplebar-track,
.simplebar-hover-track.simplebar-dragging .simplebar-track {
    background: var(--bs-bg-level-2)
}

.slick-slider {
    position: relative;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent
}

.slick-slider .slick-list,
.slick-slider .slick-track {
    transform: translate3d(0, 0, 0)
}

.slick-list {
    position: relative;
    overflow: hidden;
    display: block;
    margin: 0;
    padding: 0
}

.slick-list:focus {
    outline: 0
}

.slick-list.dragging {
    cursor: pointer;
    cursor: hand
}

.slick-track {
    position: relative;
    left: 0;
    top: 0;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.slick-track:after,
.slick-track:before {
    content: "";
    display: table
}

.slick-track:after {
    clear: both
}

.slick-loading .slick-track {
    visibility: hidden
}

.slick-slide {
    float: left;
    height: 100%;
    min-height: 1px;
    outline: 0
}

[dir=rtl] .slick-slide {
    float: right
}

.slick-slide img {
    display: block
}

.slick-slide.slick-loading img {
    display: none
}

.slick-slide.dragging img {
    pointer-events: none
}

.slick-initialized .slick-slide {
    display: block
}

.slick-loading .slick-slide {
    visibility: hidden
}

.slick-vertical .slick-slide {
    display: block;
    height: auto
}

.slick-arrow.slick-hidden {
    display: none
}

[class*=slick-next],
[class*=slick-prev] {
    position: absolute;
    display: block;
    border: 0;
    z-index: 1;
    cursor: pointer
}

[class*=slick-next]>i,
[class*=slick-prev]>i {
    font-size: 1.25rem
}

[class*=slick-next]>svg,
[class*=slick-prev]>svg {
    font-size: 1.25rem;
    height: 1em
}

.slick-next,
.slick-prev {
    bottom: .75rem
}

.slick-prev {
    left: 1.25rem
}

.slick-next {
    right: 1.25rem
}

.slick-next-2,
.slick-prev-2 {
    height: 3.75rem;
    top: 50%;
    transform: translateY(-50%)
}

.slick-prev-2 {
    left: .5rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.slick-next-2 {
    right: .5rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.slick-next-3,
.slick-prev-3 {
    display: flex !important;
    align-items: center;
    justify-content: center;
    height: calc(1.5em + (.75rem + 2px));
    width: calc(1.5em + (.75rem + 2px));
    bottom: .75rem;
    padding: 0
}

.slick-prev-3 {
    right: calc(calc(1.5em + (.75rem + 2px)) + 2rem)
}

.slick-next-3 {
    right: 1.25rem
}

.slick-dots {
    position: absolute;
    display: flex !important;
    align-items: center;
    justify-content: center;
    left: 50%;
    transform: translateX(-50%);
    bottom: .75rem;
    padding: .5rem !important;
    background: var(--bs-bg-level-1);
    border-radius: 2rem;
    list-style: none;
    margin: 0;
    z-index: 1
}

.slick-dots li {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.25rem;
    width: 1.25rem;
    margin: 0;
    padding: 0
}

.slick-dots li+li {
    margin-left: .5rem
}

.slick-dots li button {
    display: block;
    height: 65%;
    width: 65%;
    background: var(--bs-bg-level-3);
    color: transparent;
    padding: 0;
    outline: 0;
    border: 0;
    border-radius: 50%;
    transition: all .2s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .slick-dots li button {
        transition: none
    }
}

.slick-dots li button:focus,
.slick-dots li button:hover {
    background: #bdbdbd
}

.slick-dots li.slick-active button {
    width: 100%;
    height: 100%;
    background: #2196f3
}

.sortable {
    display: flex;
    flex-direction: column
}

.sortable .sortable {
    padding-left: 1.25rem
}

.sortable-handle {
    cursor: move;
    padding: .25rem
}

.sortable-handle>i {
    font-size: 1.25rem
}

.sortable-handle>svg {
    font-size: 1.25rem;
    height: 1em
}

.sortable-ghost {
    opacity: .65
}

body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
    overflow: hidden
}

body.swal2-height-auto {
    height: auto !important
}

body.swal2-no-backdrop .swal2-container {
    background-color: transparent !important;
    pointer-events: none
}

body.swal2-no-backdrop .swal2-container .swal2-popup {
    pointer-events: all
}

@media print {
    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
        overflow-y: scroll !important
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true] {
        display: none
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {
        position: static !important
    }
}

body.swal2-toast-shown .swal2-container {
    box-sizing: border-box;
    width: 20rem;
    max-width: 100%;
    background-color: transparent;
    pointer-events: none
}

body.swal2-toast-shown .swal2-container.swal2-top {
    top: 0;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-top-end,
body.swal2-toast-shown .swal2-container.swal2-top-right {
    top: 0;
    right: 0;
    bottom: auto;
    left: auto
}

body.swal2-toast-shown .swal2-container.swal2-top-left,
body.swal2-toast-shown .swal2-container.swal2-top-start {
    top: 0;
    right: auto;
    bottom: auto;
    left: 0
}

body.swal2-toast-shown .swal2-container.swal2-center-left,
body.swal2-toast-shown .swal2-container.swal2-center-start {
    top: 50%;
    right: auto;
    bottom: auto;
    left: 0;
    transform: translateY(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-center {
    top: 50%;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translate(-50%, -50%)
}

body.swal2-toast-shown .swal2-container.swal2-center-end,
body.swal2-toast-shown .swal2-container.swal2-center-right {
    top: 50%;
    right: 0;
    bottom: auto;
    left: auto;
    transform: translateY(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-bottom-left,
body.swal2-toast-shown .swal2-container.swal2-bottom-start {
    top: auto;
    right: auto;
    bottom: 0;
    left: 0
}

body.swal2-toast-shown .swal2-container.swal2-bottom {
    top: auto;
    right: auto;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-bottom-end,
body.swal2-toast-shown .swal2-container.swal2-bottom-right {
    top: auto;
    right: 0;
    bottom: 0;
    left: auto
}

@keyframes swal2-toast-show {
    0% {
        transform: translateY(-.625em) rotateZ(2deg)
    }

    33% {
        transform: translateY(0) rotateZ(-2deg)
    }

    66% {
        transform: translateY(.3125em) rotateZ(2deg)
    }

    100% {
        transform: translateY(0) rotateZ(0)
    }
}

@keyframes swal2-toast-hide {
    100% {
        transform: rotateZ(1deg);
        opacity: 0
    }
}

@keyframes swal2-toast-animate-success-line-tip {
    0% {
        top: .5625em;
        left: .0625em;
        width: 0
    }

    54% {
        top: .125em;
        left: .125em;
        width: 0
    }

    70% {
        top: .625em;
        left: -.25em;
        width: 1.625em
    }

    84% {
        top: 1.0625em;
        left: .75em;
        width: .5em
    }

    100% {
        top: 1.125em;
        left: .1875em;
        width: .75em
    }
}

@keyframes swal2-toast-animate-success-line-long {
    0% {
        top: 1.625em;
        right: 1.375em;
        width: 0
    }

    65% {
        top: 1.25em;
        right: .9375em;
        width: 0
    }

    84% {
        top: .9375em;
        right: 0;
        width: 1.125em
    }

    100% {
        top: .9375em;
        right: .1875em;
        width: 1.375em
    }
}

@keyframes swal2-show {
    0% {
        transform: scale(.7)
    }

    100% {
        transform: scale(1)
    }
}

@keyframes swal2-hide {
    0% {
        transform: scale(1);
        opacity: 1
    }

    100% {
        transform: scale(.5);
        opacity: 0
    }
}

@keyframes swal2-animate-success-line-tip {
    0% {
        top: 1.1875em;
        left: .0625em;
        width: 0
    }

    54% {
        top: 1.0625em;
        left: .125em;
        width: 0
    }

    70% {
        top: 2.1875em;
        left: -.375em;
        width: 3.125em
    }

    84% {
        top: 3em;
        left: 1.3125em;
        width: 1.0625em
    }

    100% {
        top: 2.8125em;
        left: .8125em;
        width: 1.5625em
    }
}

@keyframes swal2-animate-success-line-long {
    0% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    65% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    84% {
        top: 2.1875em;
        right: 0;
        width: 3.4375em
    }

    100% {
        top: 2.375em;
        right: .5em;
        width: 2.9375em
    }
}

@keyframes swal2-rotate-success-circular-line {
    0% {
        transform: rotate(-45deg)
    }

    5% {
        transform: rotate(-45deg)
    }

    12% {
        transform: rotate(-405deg)
    }

    100% {
        transform: rotate(-405deg)
    }
}

@keyframes swal2-animate-error-x-mark {
    0% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    50% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    80% {
        margin-top: -.375em;
        transform: scale(1.15)
    }

    100% {
        margin-top: 0;
        transform: scale(1);
        opacity: 1
    }
}

@keyframes swal2-animate-error-icon {
    0% {
        transform: rotateX(100deg);
        opacity: 0
    }

    100% {
        transform: rotateX(0);
        opacity: 1
    }
}

@keyframes swal2-rotate-loading {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

@keyframes swal2-animate-question-mark {
    0% {
        transform: rotateY(-360deg)
    }

    100% {
        transform: rotateY(0)
    }
}

@keyframes swal2-animate-i-mark {
    0% {
        transform: rotateZ(45deg);
        opacity: 0
    }

    25% {
        transform: rotateZ(-25deg);
        opacity: .4
    }

    50% {
        transform: rotateZ(15deg);
        opacity: .8
    }

    75% {
        transform: rotateZ(-5deg);
        opacity: 1
    }

    100% {
        transform: rotateX(0);
        opacity: 1
    }
}

.swal2-popup.swal2-toast {
    box-sizing: border-box;
    grid-column: 1/4 !important;
    grid-row: 1/4 !important;
    grid-template-columns: 1fr 99fr 1fr;
    padding: .75rem;
    overflow-y: hidden;
    background: var(--bs-bg-level-1);
    pointer-events: all
}

.swal2-popup.swal2-toast>* {
    grid-column: 2
}

.swal2-popup.swal2-toast .swal2-title {
    margin: .75rem;
    padding: 0;
    font-size: 1rem;
    text-align: initial
}

.swal2-popup.swal2-toast .swal2-loading {
    justify-content: center
}

.swal2-popup.swal2-toast .swal2-input {
    height: calc(1.5em + (.75rem + 2px));
    margin: .5rem;
    font-size: 1rem
}

.swal2-popup.swal2-toast .swal2-validation-message {
    font-size: 1rem
}

.swal2-popup.swal2-toast .swal2-footer {
    margin: .5rem 0 0;
    padding: .5rem 0 0;
    font-size: .875rem
}

.swal2-popup.swal2-toast .swal2-close {
    grid-column: 3/3;
    grid-row: 1/99;
    align-self: center;
    width: .8rem;
    height: .8rem;
    margin: 0;
    font-size: 2em
}

.swal2-popup.swal2-toast .swal2-html-container {
    margin: .5rem 1rem;
    padding: 0;
    font-size: 1rem;
    text-align: initial
}

.swal2-popup.swal2-toast .swal2-html-container:empty {
    padding: 0
}

.swal2-popup.swal2-toast .swal2-loader {
    grid-column: 1;
    grid-row: 1/99;
    align-self: center;
    width: 2em;
    height: 2em;
    margin: .25em
}

.swal2-popup.swal2-toast .swal2-icon {
    grid-column: 1;
    grid-row: 1/99;
    align-self: center;
    width: 2em;
    min-width: 2em;
    height: 2em;
    margin: 0 .5em 0 0
}

.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
    display: flex;
    align-items: center;
    font-size: calc(1.305rem + .66vw);
    font-weight: 700
}

@media (min-width:1200px) {
    .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
        font-size: 1.8rem
    }
}

.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
    width: 2em;
    height: 2em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    top: .875em;
    width: 1.375em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: .3125em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: .3125em
}

.swal2-popup.swal2-toast .swal2-actions {
    justify-content: flex-start;
    height: auto;
    margin: 0;
    margin-top: .5em;
    padding: 0 .5em
}

.swal2-popup.swal2-toast .swal2-styled {
    margin: .25em .5em;
    padding: .4em .6em;
    font-size: 1rem
}

.swal2-popup.swal2-toast .swal2-success {
    border-color: #4caf50
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line] {
    position: absolute;
    width: 1.6em;
    height: 3em;
    transform: rotate(45deg);
    border-radius: 50%
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left] {
    top: -.8em;
    left: -.5em;
    transform: rotate(-45deg);
    transform-origin: 2em 2em;
    border-radius: 4em 0 0 4em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right] {
    top: -.25em;
    left: .9375em;
    transform-origin: 0 1.5em;
    border-radius: 0 4em 4em 0
}

.swal2-popup.swal2-toast .swal2-success .swal2-success-ring {
    width: 2em;
    height: 2em
}

.swal2-popup.swal2-toast .swal2-success .swal2-success-fix {
    top: 0;
    left: .4375em;
    width: .4375em;
    height: 2.6875em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line] {
    height: .3125em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip] {
    top: 1.125em;
    left: .1875em;
    width: .75em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long] {
    top: .9375em;
    right: .1875em;
    width: 1.375em
}

.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip {
    animation: swal2-toast-animate-success-line-tip .75s
}

.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long {
    animation: swal2-toast-animate-success-line-long .75s
}

.swal2-popup.swal2-toast.swal2-show {
    animation: swal2-toast-show .5s
}

.swal2-popup.swal2-toast.swal2-hide {
    animation: swal2-toast-hide .2s forwards
}

body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
    overflow: hidden
}

body.swal2-height-auto {
    height: auto !important
}

body.swal2-no-backdrop .swal2-container {
    background-color: transparent !important;
    pointer-events: none
}

body.swal2-no-backdrop .swal2-container .swal2-popup {
    pointer-events: all
}

@media print {
    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
        overflow-y: scroll !important
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true] {
        display: none
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {
        position: static !important
    }
}

.swal2-container {
    display: grid;
    position: fixed;
    z-index: 1140;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    grid-template-areas: "top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";
    grid-template-rows: minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);
    height: 100%;
    padding: .75rem;
    overflow-x: hidden;
    transition: background-color .1s linear;
    -webkit-overflow-scrolling: touch
}

.swal2-container.swal2-backdrop-show,
.swal2-container.swal2-noanimation {
    background: rgba(33, 33, 33, .65)
}

.swal2-container.swal2-backdrop-hide {
    background: 0 0 !important
}

.swal2-container.swal2-bottom-start,
.swal2-container.swal2-center-start,
.swal2-container.swal2-top-start {
    grid-template-columns: minmax(0, 1fr) auto auto
}

.swal2-container.swal2-bottom,
.swal2-container.swal2-center,
.swal2-container.swal2-top {
    grid-template-columns: auto minmax(0, 1fr) auto
}

.swal2-container.swal2-bottom-end,
.swal2-container.swal2-center-end,
.swal2-container.swal2-top-end {
    grid-template-columns: auto auto minmax(0, 1fr)
}

.swal2-container.swal2-top-start>.swal2-popup {
    align-self: start
}

.swal2-container.swal2-top>.swal2-popup {
    grid-column: 2;
    align-self: start;
    justify-self: center
}

.swal2-container.swal2-top-end>.swal2-popup,
.swal2-container.swal2-top-right>.swal2-popup {
    grid-column: 3;
    align-self: start;
    justify-self: end
}

.swal2-container.swal2-center-left>.swal2-popup,
.swal2-container.swal2-center-start>.swal2-popup {
    grid-row: 2;
    align-self: center
}

.swal2-container.swal2-center>.swal2-popup {
    grid-column: 2;
    grid-row: 2;
    align-self: center;
    justify-self: center
}

.swal2-container.swal2-center-end>.swal2-popup,
.swal2-container.swal2-center-right>.swal2-popup {
    grid-column: 3;
    grid-row: 2;
    align-self: center;
    justify-self: end
}

.swal2-container.swal2-bottom-left>.swal2-popup,
.swal2-container.swal2-bottom-start>.swal2-popup {
    grid-column: 1;
    grid-row: 3;
    align-self: end
}

.swal2-container.swal2-bottom>.swal2-popup {
    grid-column: 2;
    grid-row: 3;
    justify-self: center;
    align-self: end
}

.swal2-container.swal2-bottom-end>.swal2-popup,
.swal2-container.swal2-bottom-right>.swal2-popup {
    grid-column: 3;
    grid-row: 3;
    align-self: end;
    justify-self: end
}

.swal2-container.swal2-grow-fullscreen>.swal2-popup,
.swal2-container.swal2-grow-row>.swal2-popup {
    grid-column: 1/4;
    width: 100%
}

.swal2-container.swal2-grow-column>.swal2-popup,
.swal2-container.swal2-grow-fullscreen>.swal2-popup {
    grid-row: 1/4;
    align-self: stretch
}

.swal2-container.swal2-no-transition {
    transition: none !important
}

.swal2-popup {
    display: none;
    position: relative;
    box-sizing: border-box;
    grid-template-columns: minmax(0, 100%);
    width: 32rem;
    max-width: 100%;
    padding: 1rem;
    color: var(--bs-text-level-2);
    background: var(--bs-bg-level-1);
    font-size: 1rem;
    font-family: var(--bs-font-sans-serif);
    border: 1px solid var(--bs-border-color);
    border-radius: .3rem
}

.swal2-popup:focus {
    outline: 0
}

.swal2-popup.swal2-loading {
    overflow-y: hidden
}

.swal2-title {
    position: relative;
    max-width: 100%;
    margin: 0;
    padding: .75rem 1rem 0;
    color: var(--bs-text-level-3);
    font-size: calc(1.305rem + .66vw);
    font-weight: 600;
    text-align: center;
    text-transform: none;
    word-wrap: break-word
}

@media (min-width:1200px) {
    .swal2-title {
        font-size: 1.8rem
    }
}

.swal2-actions {
    display: flex;
    z-index: 1;
    box-sizing: border-box;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 1.75rem auto 0;
    padding: 0
}

.swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
    opacity: .4
}

.swal2-actions:not(.swal2-loading) .swal2-styled:hover {
    background-image: linear-gradient(#1c80cf, #1c80cf)
}

.swal2-actions:not(.swal2-loading) .swal2-styled:active {
    background-image: linear-gradient(#1a78c2, #1a78c2)
}

.swal2-loader {
    display: none;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    margin: 0 1.75rem;
    animation: swal2-rotate-loading .75s linear 0s infinite normal;
    border-width: .25em;
    border-style: solid;
    border-radius: 100%;
    border-color: #2196f3 #2196f3 #2196f3 transparent
}

.swal2-styled {
    margin: .25rem;
    padding: .375rem .75rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    font-weight: 400
}

.swal2-styled:not([disabled]) {
    cursor: pointer
}

.swal2-styled.swal2-confirm {
    border: 0;
    border-radius: .25rem;
    background: initial;
    background-color: #2196f3;
    color: #fff;
    font-size: 1rem
}

.swal2-styled.swal2-deny {
    border: 0;
    border-radius: .25rem;
    background: initial;
    background-color: #f44336;
    color: #fff;
    font-size: 1rem
}

.swal2-styled.swal2-cancel {
    border: 0;
    border-radius: .25rem;
    background: initial;
    background-color: #757575;
    color: #fff;
    font-size: 1rem
}

.swal2-styled:focus {
    outline: 0
}

.swal2-styled::-moz-focus-inner {
    border: 0
}

.swal2-footer {
    justify-content: center;
    margin: 1.25rem 0 0;
    padding: 1rem 0 0;
    color: var(--bs-text-level-1);
    font-size: 1rem;
    border-top: 1px solid var(--bs-border-color)
}

.swal2-timer-progress-bar-container {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    grid-column: auto !important;
    height: .25rem;
    overflow: hidden;
    border-bottom-right-radius: .3rem;
    border-bottom-left-radius: .3rem
}

.swal2-timer-progress-bar {
    width: 100%;
    height: .25rem;
    background: var(--bs-bg-level-2)
}

.swal2-image {
    max-width: 100%;
    margin: 1.25rem auto
}

.swal2-close {
    position: absolute;
    z-index: 2;
    align-items: center;
    justify-content: center;
    width: calc(1.5em + (1rem + 2px));
    height: calc(1.5em + (1rem + 2px));
    margin-top: 0;
    margin-right: 0;
    margin-bottom: calc((1.5em + (1rem + 2px)) * -1);
    padding: 0;
    overflow: hidden;
    transition: color .1s ease-out;
    border: none;
    background: 0 0;
    color: #bdbdbd;
    font-family: inherit;
    font-family: monospace;
    font-size: 2.4rem;
    cursor: pointer;
    justify-self: end;
    border-radius: .3rem
}

.swal2-close:focus,
.swal2-close:hover {
    transform: none;
    background: 0 0;
    color: #f44336
}

.swal2-close::-moz-focus-inner {
    border: 0
}

.swal2-html-container {
    z-index: 1;
    justify-content: center;
    margin: 1rem 1.5rem .25rem;
    padding: 0;
    overflow: auto;
    color: inherit;
    font-size: 1rem;
    font-weight: 400;
    line-height: normal;
    text-align: center;
    word-wrap: break-word;
    word-break: break-word
}

.swal2-checkbox,
.swal2-file,
.swal2-input,
.swal2-radio,
.swal2-select,
.swal2-textarea {
    margin: 1rem auto
}

.swal2-file,
.swal2-input,
.swal2-textarea {
    box-sizing: border-box;
    width: 100%;
    color: var(--bs-text-level-3);
    background: var(--bs-bg-level-1);
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    border: 1px solid var(--bs-bg-level-3);
    font-size: 1.25rem;
    border-radius: .3rem
}

.swal2-file.swal2-inputerror,
.swal2-input.swal2-inputerror,
.swal2-textarea.swal2-inputerror {
    border-color: #f44336 !important
}

.swal2-file:focus,
.swal2-input:focus,
.swal2-textarea:focus {
    border-color: #2196f3;
    outline: 0
}

.swal2-file::placeholder,
.swal2-input::placeholder,
.swal2-textarea::placeholder {
    color: var(--bs-text-level-1)
}

.swal2-range {
    margin: 1rem auto;
    background: var(--bs-bg-level-1)
}

.swal2-range input {
    width: 80%
}

.swal2-range output {
    width: 20%;
    color: var(--bs-text-level-3);
    font-weight: 600;
    text-align: center
}

.swal2-range input,
.swal2-range output {
    height: calc(1.5em + (.75rem + 2px));
    padding: 0;
    font-size: 1.25rem;
    line-height: calc(1.5em + (.75rem + 2px))
}

.swal2-input {
    height: calc(1.5em + (.75rem + 2px));
    padding: .5rem 1rem
}

.swal2-file {
    width: 75%;
    margin-right: auto;
    margin-left: auto;
    background: var(--bs-bg-level-1);
    font-size: 1.25rem
}

.swal2-textarea {
    height: 6.75em;
    padding: .375rem .75rem
}

.swal2-select {
    min-width: 50%;
    max-width: 100%;
    padding: .375em .625em;
    background: var(--bs-bg-level-1);
    color: var(--bs-text-level-3);
    font-size: 1.25rem
}

.swal2-checkbox,
.swal2-radio {
    align-items: center;
    justify-content: center;
    background: var(--bs-bg-level-1);
    color: var(--bs-text-level-3)
}

.swal2-checkbox label,
.swal2-radio label {
    margin: 0 .6em;
    font-size: 1.25rem
}

.swal2-checkbox input,
.swal2-radio input {
    flex-shrink: 0;
    margin: 0 .4em
}

.swal2-input-label {
    display: flex;
    justify-content: center;
    margin: 1rem auto 0
}

.swal2-validation-message {
    align-items: center;
    justify-content: center;
    margin: 1rem 0 0;
    padding: .5rem;
    overflow: hidden;
    background: var(--bs-bg-level-2);
    color: var(--bs-text-level-3);
    font-size: 1rem;
    font-weight: 400
}

.swal2-validation-message::before {
    content: "!";
    display: inline-block;
    width: 1.5em;
    min-width: 1.5em;
    height: 1.5em;
    margin: 0 .625em;
    border-radius: 50%;
    background-color: #f44336;
    color: #fff;
    font-weight: 600;
    line-height: 1.5em;
    text-align: center
}

.swal2-icon {
    position: relative;
    box-sizing: content-box;
    justify-content: center;
    width: 5rem;
    height: 5rem;
    margin: 1.25rem auto 1.75rem;
    border: .25em solid transparent;
    border-radius: 50%;
    border-color: var(--bs-border-color);
    font-family: inherit;
    line-height: 5rem;
    cursor: default;
    user-select: none
}

.swal2-icon .swal2-icon-content {
    display: flex;
    align-items: center;
    font-size: calc(1.485rem + 2.82vw)
}

@media (min-width:1200px) {
    .swal2-icon .swal2-icon-content {
        font-size: 3.6rem
    }
}

.swal2-icon.swal2-error {
    border-color: #f44336;
    color: #f44336
}

.swal2-icon.swal2-error .swal2-x-mark {
    position: relative;
    flex-grow: 1
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    display: block;
    position: absolute;
    top: 2.3125em;
    width: 2.9375em;
    height: .3125em;
    border-radius: .125em;
    background-color: #f44336
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: 1.0625em;
    transform: rotate(45deg)
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: 1em;
    transform: rotate(-45deg)
}

.swal2-icon.swal2-error.swal2-icon-show {
    animation: swal2-animate-error-icon .5s
}

.swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark {
    animation: swal2-animate-error-x-mark .5s
}

.swal2-icon.swal2-warning {
    border-color: #ffef5f;
    color: #ffeb3b
}

.swal2-icon.swal2-warning.swal2-icon-show {
    animation: swal2-animate-error-icon .5s
}

.swal2-icon.swal2-warning.swal2-icon-show .swal2-icon-content {
    animation: swal2-animate-i-mark .5s
}

.swal2-icon.swal2-info {
    border-color: #3be9ff;
    color: #00bcd4
}

.swal2-icon.swal2-info.swal2-icon-show {
    animation: swal2-animate-error-icon .5s
}

.swal2-icon.swal2-info.swal2-icon-show .swal2-icon-content {
    animation: swal2-animate-i-mark .8s
}

.swal2-icon.swal2-question {
    border-color: #82c4f8;
    color: #2196f3
}

.swal2-icon.swal2-question.swal2-icon-show {
    animation: swal2-animate-error-icon .5s
}

.swal2-icon.swal2-question.swal2-icon-show .swal2-icon-content {
    animation: swal2-animate-question-mark .8s
}

.swal2-icon.swal2-success {
    border-color: #4caf50;
    color: #4caf50
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line] {
    position: absolute;
    width: 3.75em;
    height: 7.5em;
    transform: rotate(45deg);
    border-radius: 50%
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left] {
    top: -.4375em;
    left: -2.0635em;
    transform: rotate(-45deg);
    transform-origin: 3.75em 3.75em;
    border-radius: 7.5em 0 0 7.5em
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right] {
    top: -.6875em;
    left: 1.875em;
    transform: rotate(-45deg);
    transform-origin: 0 3.75em;
    border-radius: 0 7.5em 7.5em 0
}

.swal2-icon.swal2-success .swal2-success-ring {
    position: absolute;
    z-index: 2;
    top: -.25em;
    left: -.25em;
    box-sizing: content-box;
    width: 100%;
    height: 100%;
    border: .25em solid rgba(76, 175, 80, .3);
    border-radius: 50%
}

.swal2-icon.swal2-success .swal2-success-fix {
    position: absolute;
    z-index: 1;
    top: .5em;
    left: 1.625em;
    width: .4375em;
    height: 5.625em;
    transform: rotate(-45deg)
}

.swal2-icon.swal2-success [class^=swal2-success-line] {
    display: block;
    position: absolute;
    z-index: 2;
    height: .3125em;
    border-radius: .125em;
    background-color: #4caf50
}

.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
    top: 2.875em;
    left: .8125em;
    width: 1.5625em;
    transform: rotate(45deg)
}

.swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {
    top: 2.375em;
    right: .5em;
    width: 2.9375em;
    transform: rotate(-45deg)
}

.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip {
    animation: swal2-animate-success-line-tip .75s
}

.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long {
    animation: swal2-animate-success-line-long .75s
}

.swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right {
    animation: swal2-rotate-success-circular-line 4.25s ease-in
}

.swal2-progress-steps {
    flex-wrap: wrap;
    align-items: center;
    max-width: 100%;
    margin: 1.25rem auto;
    padding: 0;
    background: inherit;
    font-weight: 600
}

.swal2-progress-steps li {
    display: inline-block;
    position: relative
}

.swal2-progress-steps .swal2-progress-step {
    z-index: 20;
    flex-shrink: 0;
    width: 2em;
    height: 2em;
    border-radius: 2em;
    background: #2196f3;
    color: #fff;
    line-height: 2em;
    text-align: center
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
    background: #2196f3
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step {
    background: rgba(33, 150, 243, .1);
    color: #2196f3
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line {
    background: rgba(33, 150, 243, .1)
}

.swal2-progress-steps .swal2-progress-step-line {
    z-index: 10;
    flex-shrink: 0;
    width: 2.5em;
    height: .4em;
    margin: 0 -1px;
    background: #2196f3
}

[class^=swal2] {
    -webkit-tap-highlight-color: transparent
}

.swal2-show {
    animation: swal2-show .2s
}

.swal2-hide {
    animation: swal2-hide .2s forwards
}

.swal2-noanimation {
    transition: none
}

.swal2-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

.swal2-rtl .swal2-close {
    margin-right: initial;
    margin-left: 0
}

.swal2-rtl .swal2-timer-progress-bar {
    right: 0;
    left: auto
}

.toast {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 25rem;
    padding: 1.25rem calc(calc(1.5em + (.5rem + 2px)) + .5rem) 1.25rem 4.5rem;
    margin-bottom: .75rem;
    opacity: .8;
    overflow: hidden;
    cursor: pointer;
    border-radius: .25rem
}

.toast:hover {
    opacity: 1
}

.toast-title {
    font-size: 1.25rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.toast-message {
    font-size: 1rem;
    font-weight: 400;
    word-break: break-word
}

.toast-message a {
    font-weight: 500
}

.toast-message a:hover {
    text-decoration: none
}

.toast-progress {
    position: absolute;
    left: 0;
    bottom: 0;
    height: .35rem;
    background-color: #bdbdbd;
    opacity: .65
}

.toast-close-button {
    position: absolute;
    top: .5rem;
    right: .5rem;
    width: calc(1.5em + (.5rem + 2px));
    height: calc(1.5em + (.5rem + 2px));
    font-size: calc(1.275rem + .3vw);
    font-weight: 600;
    background-color: transparent;
    color: inherit;
    padding: 0;
    border: 0;
    outline: 0;
    cursor: pointer;
    -webkit-appearance: none
}

@media (min-width:1200px) {
    .toast-close-button {
        font-size: 1.5rem
    }
}

.toast-close-button:focus,
.toast-close-button:hover {
    text-decoration: none
}

.toast {
    background-color: #2196f3;
    color: #fff
}

.toast a,
.toast label {
    color: #fff
}

.toast-success {
    background-color: #4caf50;
    color: #fff
}

.toast-success a,
.toast-success label {
    color: #fff
}

.toast-info {
    background-color: #00bcd4;
    color: #fff
}

.toast-info a,
.toast-info label {
    color: #fff
}

.toast-warning {
    background-color: #ffeb3b;
    color: #000
}

.toast-warning a,
.toast-warning label {
    color: #000
}

.toast-error {
    background-color: #f44336;
    color: #fff
}

.toast-error a,
.toast-error label {
    color: #fff
}

.toast:before {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    font-family: "Font Awesome 5 Free";
    font-style: normal;
    font-weight: 900;
    font-size: calc(1.305rem + .66vw);
    width: 4rem;
    min-width: 4rem;
    left: .5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1
}

@media (min-width:1200px) {
    .toast:before {
        font-size: 1.8rem
    }
}

.toast-success:before {
    content: "\f00c"
}

.toast-info:before {
    content: "\f0a1"
}

.toast-warning:before {
    content: "\f129"
}

.toast-error:before {
    content: "\f00d"
}

#toast-container {
    position: fixed;
    z-index: 2000
}

.toast-top-center {
    top: 0;
    right: 0;
    width: 100%
}

.toast-bottom-center {
    bottom: 0;
    right: 0;
    width: 100%
}

.toast-top-full-width {
    top: 0;
    right: 0;
    width: 100%
}

.toast-bottom-full-width {
    bottom: 0;
    right: 0;
    width: 100%
}

.toast-top-left {
    top: 1rem;
    left: 1rem
}

.toast-top-right {
    top: 1rem;
    right: 1rem
}

.toast-bottom-right {
    right: 1rem;
    bottom: 1rem
}

.toast-bottom-left {
    bottom: 1rem;
    left: 1rem
}

#toast-container.toast-bottom-center>div,
#toast-container.toast-top-center>div {
    width: 25rem;
    margin-left: auto;
    margin-right: auto
}

#toast-container.toast-bottom-full-width>div,
#toast-container.toast-top-full-width>div {
    width: 96%;
    margin-left: auto;
    margin-right: auto
}

.twitter-typeahead {
    width: 100%
}

.tt-hint {
    direction: ltr;
    color: var(--bs-text-level-1);
    background: 0 0 !important
}

.tt-menu {
    position: absolute;
    width: 100%;
    overflow: hidden;
    background: var(--bs-bg-level-1);
    border: 1px solid var(--bs-bg-level-3);
    border-top-color: #2196f3;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.tt-menu-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: 0 0;
    color: var(--bs-text-level-1);
    padding: .5rem .75rem;
    margin: 0;
    cursor: default;
    text-decoration: none
}

.tt-menu-item:focus,
.tt-menu-item:hover {
    background: var(--bs-bg-level-2)
}

.tt-menu-item:focus .tt-menu-title,
.tt-menu-item:hover .tt-menu-title {
    color: #2196f3
}

.tt-menu-item.active,
.tt-menu-item:active {
    background: var(--bs-bg-level-3)
}

.tt-menu-item.active .tt-menu-title,
.tt-menu-item:active .tt-menu-title {
    color: #2196f3
}

.tt-menu-item.disabled,
.tt-menu-item:disabled {
    background: var(--bs-bg-level-2);
    opacity: .65;
    pointer-events: none;
    cursor: default
}

.tt-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column
}

.tt-menu-subtitle,
.tt-menu-title {
    margin: 0;
    padding: 0;
    font-size: 1rem;
    line-height: 1.5;
    text-align: left
}

.tt-menu-title {
    color: var(--bs-text-level-3);
    font-weight: 500
}

.tt-menu-subtitle {
    color: var(--bs-text-level-1);
    font-weight: 400
}

.tt-menu-append,
.tt-menu-prepend {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

.tt-menu-append>.avatar,
.tt-menu-prepend>.avatar {
    margin: 0
}

.tt-menu-prepend {
    margin-right: .5rem
}

.tt-menu-append {
    margin-left: .5rem
}

.tt-suggestion {
    color: var(--bs-text-level-1);
    font-size: 1rem;
    font-weight: 400;
    padding: .5rem
}

.tt-suggestion:hover {
    color: #2196f3;
    background: var(--bs-bg-level-2)
}

.tt-highlight {
    color: #2196f3 !important;
    font-size: inherit;
    font-weight: inherit
}

.tt-empty-message {
    color: var(--bs-text-level-2);
    font-size: 1rem;
    font-weight: 500;
    padding: .5rem .75rem
}

.tt-header {
    color: var(--bs-text-level-3);
    font-size: 1rem;
    font-weight: 500;
    padding: .5rem .75rem;
    margin: 0
}

.tt-header~.tt-suggestion {
    padding-left: 1.25rem
}

.tt-input {
    direction: ltr
}

.tt-input.form-control~.tt-menu {
    top: calc(100% - .25rem) !important
}

.tt-input.form-control-lg~.tt-menu {
    top: calc(100% - .3rem) !important
}

.tt-input.form-control-sm~.tt-menu {
    top: calc(100% - .2rem) !important
}