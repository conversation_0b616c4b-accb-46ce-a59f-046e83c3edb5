body{
  background-color: #000;
  color: #fff;
}
.bg::-webkit-scrollbar {
  display: none;
}

.bg {
  /* background: url(https://i.ibb.co/87GbbFP/2799006.jpg) no-repeat; */
  background:  black;
  background-size: cover;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -3;
  overflow-y: scroll;
      scroll-behavior: auto;
}

.bg:before {
  content: "";
  width: 100%;
  height: 100%;
  background: #000;
  position: fixed;
  z-index: -1;
  top: 0;
  left: 0;
  opacity: 0.3;
}
.fs-16
{
  font-size: 16px;
}
@keyframes sf-fly-by-1 {
  from {
      transform: translateZ(-600px);
      opacity: 0.5;
  }
  to {
      transform: translateZ(0);
      opacity: 0.5;
  }
}
@keyframes sf-fly-by-2 {
  from {
      transform: translateZ(-1200px);
      opacity: 0.5;
  }
  to {
      transform: translateZ(-600px);
      opacity: 0.5;
  }
}
@keyframes sf-fly-by-3 {
  from {
      transform: translateZ(-1800px);
      opacity: 0.5;
  }
  to {
      transform: translateZ(-1200px);
      opacity: 0.5;
  }
}
.star-field {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  perspective: 600px;
  -webkit-perspective: 600px;
  z-index: -1;
}
.star-field .layer {
  box-shadow: -411px -476px #cccccc, 777px -407px #d4d4d4, -387px -477px #fcfcfc, -91px -235px #d4d4d4, 491px -460px #f7f7f7, 892px -128px #f7f7f7, 758px -277px #ededed, 596px 378px #cccccc, 647px 423px whitesmoke, 183px 389px #c7c7c7,
      524px -237px #f0f0f0, 679px -535px #e3e3e3, 158px 399px #ededed, 157px 249px #ededed, 81px -450px #ebebeb, 719px -360px #c2c2c2, -499px 473px #e8e8e8, -158px -349px #d4d4d4, 870px -134px #cfcfcf, 446px 404px #c2c2c2,
      440px 490px #d4d4d4, 414px 507px #e6e6e6, -12px 246px #fcfcfc, -384px 369px #e3e3e3, 641px -413px #fcfcfc, 822px 516px #dbdbdb, 449px 132px #c2c2c2, 727px 146px #f7f7f7, -315px -488px #e6e6e6, 952px -70px #e3e3e3,
      -869px -29px #dbdbdb, 502px 80px #dedede, 764px 342px #e0e0e0, -150px -380px #dbdbdb, 654px -426px #e3e3e3, -325px -263px #c2c2c2, 755px -447px #c7c7c7, 729px -177px #c2c2c2, -682px -391px #e6e6e6, 554px -176px #ededed,
      -85px -428px #d9d9d9, 714px 55px #e8e8e8, 359px -285px #cfcfcf, -362px -508px #dedede, 468px -265px #fcfcfc, 74px -500px #c7c7c7, -514px 383px #dbdbdb, 730px -92px #cfcfcf, -112px 287px #c9c9c9, -853px 79px #d6d6d6,
      828px 475px #d6d6d6, -681px 13px #fafafa, -176px 209px #f0f0f0, 758px 457px #fafafa, -383px -454px #ededed, 813px 179px #d1d1d1, 608px 98px whitesmoke, -860px -65px #c4c4c4, -572px 272px #f7f7f7, 459px 533px #fcfcfc,
      624px -481px #e6e6e6, 790px 477px #dedede, 731px -403px #ededed, 70px -534px #cccccc, -23px 510px #cfcfcf, -652px -237px whitesmoke, -690px 367px #d1d1d1, 810px 536px #d1d1d1, 774px 293px #c9c9c9, -362px 97px #c2c2c2,
      563px 47px #dedede, 313px 475px #e0e0e0, 839px -491px #e3e3e3, -217px 377px #d4d4d4, -581px 239px #c2c2c2, -857px 72px #cccccc, -23px 340px #dedede, -837px 246px white, 170px -502px #cfcfcf, 822px -443px #e0e0e0, 795px 497px #e0e0e0,
      -814px -337px #cfcfcf, 206px -339px #f2f2f2, -779px 108px #e6e6e6, 808px 2px #d4d4d4, 665px 41px #d4d4d4, -564px 64px #cccccc, -380px 74px #cfcfcf, -369px -60px #f7f7f7, 47px -495px #e3e3e3, -383px 368px #f7f7f7, 419px 288px #d1d1d1,
      -598px -50px #c2c2c2, -833px 187px #c4c4c4, 378px 325px whitesmoke, -703px 375px #d6d6d6, 392px 520px #d9d9d9, -492px -60px #c4c4c4, 759px 288px #ebebeb, 98px -412px #c4c4c4, -911px -277px #c9c9c9;
  transform-style: preserve-3d;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 7px;
  width: 7px;
  border-radius: 20px;
}
.star-field .layer:nth-child(1) {
  animation: sf-fly-by-1 1s linear infinite;
}
.star-field .layer:nth-child(2) {
  animation: sf-fly-by-2 1s linear infinite;
}
.star-field .layer:nth-child(3) {
  animation: sf-fly-by-3 1s linear infinite;
}
.circle_row
{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  /* margin-top: 100px; */
}
.RD_Data_text
{
  padding: 20px 20px 10px 20px;
  font-size: 3rem;
}
.cricle1 {
  height: 300px;
  width: 300px;
  border:5px solid #fff;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  position: relative; 
}
.cricle2 {
  height: 520px;
  width: 520px;
  border:5px solid #fff;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  position: relative;
}
.cricle3 {
  height: 250px;
  width: 250px;
  border: 5px solid #fff;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  position: relative;
  margin-top: 65px;
  margin-left: 166px;
}
.cricle1_inner {
  height: 300px;
  width: 300px;
  border:5px solid #fff;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  position: relative; 
}

.fs_6{
  font-size: 6rem;
}
.fs_3{
  font-size: 3rem;
}
/* .p-20
{
  padding: 20px;
} */
.m-b-50
{
  margin-bottom: 50px !important;
}
.m-l-20
{
  margin-left: 20px;
}
.inner-cricle1
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px 7px;
  position: absolute;
  float: left;
  top: 82px !important;
  left: -20px;
  font-size: 20px;
  font-weight: 800;
  opacity: 0.8;
}

.inner-cricle2
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px 7px;
  position: absolute;
  float: right;
  top: 82px !important;
  right: -20px;
  font-size: 20px;
  font-weight: 800;
  opacity: 0.8;
}
.inner-cricle3
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  text-align: center !important;
  align-items: center !important;
  padding: 12px 7px;
  position: absolute;
  bottom: -30px;
  left: 110px;
  font-size: 20px;
  font-weight: 800;
  opacity: 0.8;
}
.functional_inner_cricle1
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px 7px;
  position: absolute;
  float: left;
  bottom: 70px !important;
  left: 18px;
  opacity: 0.9;
  font-size: 20px;
  font-weight: 800;
}
.functional_inner_cricle2
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px 7px;
  position: absolute;
  float: right;
  bottom: 70px !important;
  right: 30px;
  opacity: 0.9;
  font-size: 20px;
  font-weight: 800;   
}
.functional_inner_cricle3
{
  width: 60px;
  height: 60px;
  border:5px solid #469571;
  background-color: #fff;
  border-radius: 50px;
  text-align: center !important;
  align-items: center !important;
  padding: 12px 7px;
  position: absolute;
  top: -30px;
  left: 277px;
  opacity: 0.9;
  font-size: 20px;
  font-weight: 800;
}
.text_90
{
  top:50px;
  position: relative;
  width: 60%;
  margin: auto;
}
.business_text
{
  top:125px;
  position: absolute;
}
.functional_text
{
  top: 135px;
  position: absolute;
  left: 0;
}
.NPD_text
{
  right: 76px;
  top: 11px;
  position: absolute;
  color: #fff;
  font-size: 17px;
}
.EPD_text
{
  left: 77px;
  top: 9px;
  position: absolute;
  color: #fff;
  font-size: 17px;
}
.Squeeze_text
{
  right: 80px;
  top: 19px;
  position: absolute;
  color: #fff;
  font-size: 17px;
}
.LD_text
{
  right: 72px;
  top: 11px;
  position: absolute;
  color: #9ECB3B;
  font-size: 17px;
}
.IPR_text
{
  right: 72px;
  top: 11px;
  position: absolute;
  color: #9ECB3B;
  font-size: 17px;
}
.RD_text
{
  left: -15px;
  top: 62px;
  position: absolute;
  color: #9ECB3B;
  font-size: 17px;
  white-space: nowrap;
}
.Preferred_text
{
  left: 74px;
  top: 8px;
  position: absolute;
  color: #9ECB3B;
  font-size: 17px;
  white-space: nowrap;
}
.Productivity_text
{
  right:70px; top:9px; position:absolute; color:#9ECB3B;font-size: 17px;
}
.preferred_text
{
  left:70px; top:11px; position:absolute; color:#9ECB3B;font-size: 17px;
}
.triangle-left {
  width: 0;
  height: 0;
  border-right: solid 15px #469571;
  border-bottom: solid 10px transparent;
  border-top: solid 10px transparent;
  position: absolute;
  right: 53px;
  top: 15px;
}
.triangle-right {
width: 00px;
height: 00px;
border-left: solid 15px #469571;
border-bottom: solid 10px transparent;
border-top: solid 10px transparent;
position: absolute;
left: 52px;
top: 13px;
}
.triangle-bottom {
width: 0px;
height: 0px;
border-top: solid 15px #469571;
border-left: solid 10px transparent;
border-right: solid 10px transparent;
position: absolute;
left: 15px;
top: 51px;
}
.triangle-top {
  width: 0px;
  height: 0px;
  border-bottom: solid 8px rgb(13 47 66);
  border-left: solid 5px transparent;
  border-right: solid 5px transparent;
  position: absolute;
  left: -5px;
  top: 48px;
}
.m-t-50
{
  margin-top:50px;
}
.npd_10 , .epd_10 ,.squeeze_10
{
cursor: pointer;
}
.npd_10_text
{
cursor: pointer;
}
.functional_dash_cricle
{
   border: 2px dashed #9ECB3B;
  border-radius: 112px;
  width: 220px;
  height: 220px;
  padding: 7px;
  position: relative; 
}
.functional_cricle
{
  width: 200px;
  height: 200px;
  border: 5px solid #fff;
  border-radius: 100px;
  background-color: #fff;
  color: #493C2D;
  font-size: 2rem;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;

}
.functional_cricle .bus_count
{
    font-size: 60px;
}
.functional_cricle p
{
  top: 90px;
  position: absolute;
  left: 45px;
}
.npd_15_row
{
    top: 65px;
    position: relative;
}
.NPD_CFT_border
{
  border: 1px solid #fff;
  padding: 12px 100px;
  border-radius: 6px;
  font-size: 20px;
  background: #40647dc2;
  position: relative;
  white-space: nowrap;
}
.npd_15_border {
  background: #5974FF;
    color: #fff;
    font-size: 2rem;
    border-radius: 6px;
    z-index: 1;
}
.functional_text_inner_cricle1
{
  width: 50px;
  height: 50px;
  border:5px solid #3596BF;
  background-color: #fff;
  border-radius: 50px;
  padding: 5px;
  position: absolute;
  float: left;
  top: 82px !important;
  left: -20px;
  font-size: 20px;
  z-index: 1;
}
.functional_text_inner_cricle2
{
  width: 60px;
  height: 60px;
  border: 5px solid #3596BF;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px;
  position: absolute;
  float: right;
  top: 44px !important;
  right: -20px;
  font-size: 20px;
  font-weight: 800;
  z-index: 100;
}
.functional_text_inner_cricle3
{
   width: 50px;
    height: 50px;
    border: 5px solid #1CA5A2;
    background-color: #fff;
    border-radius: 50px;
    text-align: center !important;
    align-items: center !important;
    padding: 5px;
    position: absolute;
    top: 191px;
    left: 70px;
    font-size: 20px;
}
.npd_line
{
  height: 183px;
  background: aliceblue;
  width: 4px;
  text-align: center;
  /* align-items: center; */
  position: relative;
  left: 158px;
  top: 2px;
  border-radius: 20px;
  margin: 0;
}
.dot_line
{
  background: aliceblue;
}
.functional_dash_cricle1
{
  border:2px dashed #9ECB3B;
  border-radius: 112px;
  width: 120px;
  height: 120px;
  padding: 7px;
  position: relative; 
}
.delay_dash_cricle1
{
  border:2px dashed #9ECB3B;
  border-radius: 112px;
  width: 120px;
  height: 120px;
  padding: 7px;
  position: relative; 
  bottom: 70px;
  right: 100px;

}
.functional_cricle1
{
  width: 100px;
  height: 100px;
  border: 5px solid #fff;
  border-radius: 100px;
  background-color: #fff;
  color: #493C2D;
  font-size: 1rem;
  position: absolute;
}
.functional_cricle1 p
{
  /* position: absolute;
  top: 35px;
  left: 10px; */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  top: 35px;

}
.perferred_functional_cricle {
  width: 200px;
  height: 200px;
  border: 5px solid #fff;
  border-radius: 100px;
  background-color: #fff;
  color: #000;
  font-size: 2rem;
  position: absolute;
}
.perferred_functional_cricle span {
  top: 60px;
  position: absolute;
  left: 80px;
}
.perferred_functional_cricle p {
  top: 90px;
  position: absolute;
  left: 28px;
}

.EPD_text_border span
{
    background-color: #2073a169;
    padding: 6px 12px;
    position: absolute;
    right: 25px;
    top: 56px;
    color: #fff;
    font-size: 16px;
    font-size: 500 !important;
    cursor: pointer;
    border-radius: 5px;
}
.Squeeze_text_border
{
  background-color: #2073a169;
  padding: 6px 12px;
  position: absolute;
  right: 25px;
  top: 56px;
  color: #fff;
  font-size: 16px;
  font-size: 500 !important;
  cursor: pointer;
  border-radius: 5px;
}
.RD_text_border
{
  background-color: #2073a169;
  padding: 6px 12px;
  position: absolute;
  right: 25px;
  top: 56px;
  color: #fff;
  font-size: 16px;
  font-size: 500 !important;
  cursor: pointer;
  white-space: nowrap;
  border-radius: 5px;
}

#stage2 , #squeeze_stage2 , #epd_stage2
{
  padding:10px 30px;
}
.division_wise_text
{
  background: aliceblue;
  color: #000;
  border-radius: 0px;
  padding: 15px 45px;
  border-bottom-right-radius: 66px 41px;
  left: 32px;
  position: relative;
  font-size: 1.2rem;
}
.division_30_text
{
  background: #5153e2;
    padding: 10px;
    border-radius: 20px;
    font-size: 1.2rem;
    left: 7px;
    position: relative;
    top: -4px;
}
.division_30_line
{
  width: 4%;
  position: absolute;
  height: 4px;
  background: #5153e2;
}
.division_text_row
{
  position: relative;
  top: 20px;
  left: 27px;
}
.npd_cft_types
{
    position: relative;
    top: 21px;
}
/* .npd_cft_types1 {
  position: relative;
  top: 70px;
} */
.npd_cft_types1 {
  /* position: relative; */
  /* margin-top: 70px; */
  margin-top: 131px;
}
.divi_small_cricle
{
  background-color: #fff;
  width: 10px;
  height: 10px;
  border-radius: 20px;
}

#myImg {
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s;
}

#myImg:hover {opacity: 0.7;}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.9); /* Black w/ opacity */
}

/* Modal Content (image) */
.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 700px;
}

/* Caption of Modal Image */
#caption {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 700px;
  text-align: center;
  color: #ccc;
  padding: 10px 0;
  height: 150px;
}

/* Add Animation */
.modal-content, #caption {  
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {-webkit-transform:scale(0)} 
  to {-webkit-transform:scale(1)}
}

@keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}

/* The Close Button */
.close {
  position: absolute;
  top: 15px;
  color: #f1f1f1;
  font-size: 2rem;
  font-weight: bold;
  transition: 0.3s;
  cursor: pointer;
}



/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px){
  .npd_cft_blw
{
  position: relative;
  top: 70px;
/* right: 30px; */
left: 70px;
}
  .delay_dash_cricle1 {
    border: 2px dashed #9ECB3B;
    border-radius: 112px;
    width: 120px;
    height: 120px;
    padding: 7px;
    position: relative;
    /* bottom: 70px;
    right: 100px; */
    bottom: 21px;
    left: 100px;
}
  .modal-content {
    width: 100%;
  }
  .rd_project_cricle_blw
{
  position: relative;
  top: 100px;
  left: 67px;
}
  .ld_wise_row {
    position: relative;
    top: 124px;
    right: 75px;
    /* float: left; */
}
.div_three_col {
  align-items: flex-start;
  left: 0px !important;
  position: relative;
}
.preferred_functional_dash_cricle
{
  left: 80px;
}
.cricle3 {
  height: 150px;
  width: 150px;
  border: 5px solid #fff;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  position: relative;
  margin-top: 30px;
  margin-left: 0;
}
}

/* business start */
.functional_open , .division_open , .preferred_open ,.business_open ,.accord_delayed_open ,.accord_scientist_open ,.accord_nodelay_open ,.npd_10_open ,.project_open,
.division_business_open ,.division_scientist_open , .epd_business_open ,.epd_nodelay_open ,.division1_Scientist_open ,.epd_division_open ,.epd_project_open ,.delayed_project_open,
.scientist_division_open , .status_scientist_open ,.bus_status_open ,.bus_scientist_open ,.status_div_open ,.status_div_scientist_open ,.business1_Scientist_open,.epd_scientist_open ,
.epd_division_business_open , .epd_division_scientist_open , .epd_div_sci_open , .epd_status_scientist_open ,.epd_bus_open , .epd_project_status_open ,.epd_project_scientist_open,
.epd_business_status_open ,.epd_business_scientist_open ,.epd_business_sci_open ,.squeeze_division_open , .squeeze_project_open ,.squeeze_scientist_open ,.squeeze_division_business_open,
.squeeze_division_scientist_open , .squeeze_division1_Scientist_open ,.squeeze_division1_Scientist_open ,.squeeze_business_open ,.squeeze_bus_project_open ,.squeeze_bus_scientist_open ,.squeeze_business_status_open ,
.squeeze_scientist_scientist_open , .squeeze_business1_Scientist_open ,.squeeze_status_open ,.squeeze_status_scientist_open
{  
  cursor: pointer;
}

#stage2 ,#functional_stage2 ,#division_stage1 ,#preferred_stage1  , #business_stage2 ,#accord_delayed_stage1 ,#accord_scientist_stage1,#accord_nodelay_stage1,#pre_division_divi1,
#epd_stage2 ,#epd_functional_stage2 ,#squeeze_stage2 ,#business_stage1 , #scientist_open_stage1 ,#division_business_stage1 ,#division_scientist_stage2 ,#division_scientist_stage1,
#division1_scientist_stage1 ,#epd_business_stage2 ,#squeeze_stage2 , #epd_functional_stage2 ,#epd_functional_stage2 , #npd_status_stage1 ,#epd_division_stage1 ,#epd_accord_bus_stage1,
#npd_accord_scientist_stage1 , #status_scientist_stage1 , #npd_division_stage1 , #npd_business_stage1 ,#accord_status_stage1 , #npd_bus_scientist_stage1 ,#status_accord_division_stage1,
#npd_accord_bus_stage1 , #npd_status_accord_sci_stage1 , #business_scientist_stage1 , #epd_bus_sci_stage1 , #epd_status_stage1 ,#epd_bus_status_stage1 , #epd_sci_status_stage1 , 
#epd_div_sci_stage1,#epd_status_sci_stage1 , #epd_business_stage1 , #epd_project_status_stage1 ,#epd_project_scientist_stage1 ,#epd_status_business_stage1 ,#epd_status_scientist_stage1 ,
#epd_scientist_stage1 , #squeeze_division_stage1 ,#squeeze_accord_scientist_stage1 , #squeeze_project_stage1 , #squeeze_scientist_stage1 , #squeeze_status_business_stage1  , 
#squeeze_status_scientist_stage1 , #squeeze_division1_scientist_stage1 ,#squeeze_div_Scientist_stage1 , #squeeze_businees_stage1 , #squeeze_bus_project_stage1 , #squeeze_bus_scientist_stage1 ,
#squeeze_bus_status_stage1 , #squeeze_bus_status_scientist_stage ,#squeeze_business_scientist_stage ,#squeeze_status_stage1 ,#squeeze_status_sci_stage1 ,#rd_delay_stage ,#rd_delay_hair , #personalcare_stage,
#rdbus_pc_stage , #bus_hair_stage ,#rdbus_hair_stage ,#rdbus_hair_stage  , #cft_delay_stage , #cft_divdelay_stage , #cft_pcdelay_stage ,#rdcft_hair_stage , #personalcare_stage , #cft_personalcare_stage  ,
#rd_business_cricle ,#in100_techinsight_stage , #id1000_inpro_stage
{  
  display:none;
  padding: 10px 30px;
}
#preferred_stage1
{
  padding: 10px 45px;
}

.fun_btn , .stage2_back ,.division_back ,.preferred_back, .bus_btn , .accord_bus_btn , .accord_bus_btn1 ,.epd_stage2_back ,.squeeze_stage2_back ,
.Scientist_back_btn , .division_bus_btn ,.division1_bus_btn ,.epd_stage2_back ,.epd_fun_btn ,.epd_division_back ,.npd_accord_back_btn ,
.status_bus_btn , .npd_division_back , .npd_business_back , .npd_accord_status_btn , .npd_bus_scientist_btn ,.npd_accord_bus_back ,.npd_status_accord_sci_back ,.bus_sci_btn ,.epd_bus_sci_back,
.epd_accord_bus_back , .epd_bus_status_back ,.epd_div_scientist_back , .epd_status_sci_back ,.epd_business_back ,.epd_project_status_back ,.epd_project_scientist_back , .epd_status_business_back,
.epd_status_scientist_back  , .epd_scientist_back , .squeeze_division_back ,.squeeze_accord_back_btn  ,.squeeze_scientist_back_btn  ,.squeeze_status_bus_back ,.squeeze_status_sci_back ,.squeeze_division_sci_back,
.squeeze_status_sci_back ,.squeeze_business_back ,.squeeze_accord_status_back ,.squeeze_accord_scientist_back ,.squeeze_acc_status_bus_back ,.squeeze_acc_status_sci_back , .squeeze_acc_business_sci_back , .squeeze_status_sci_btn,
.squeeze_status_back ,.squeeze_status_sci_btn ,.npd_status_div_btn ,.npd_status_sci_btn ,.rd_delay_back ,.delay_hair_back ,.personal_care_back ,.delay_bus_back ,.delayhair_back , .cftdelay_div_back
.cft_delay_back ,.delay_cfthair_back , .cft_delay_back ,.cftdelay_div_back ,.delay_cftbus_back ,.cft_hc_back ,.delay_business_back ,.in1000_technical_back ,.in1000_inpro_back
{  
  font-size: 2rem;
  cursor: pointer;
}

/* functional start */
.pre_division_open , .pre_passed_open , .pre_takenup_open , .ld_open , .takenup_division_open ,.pre_converted_open ,.converted_division_open  ,.techical_open ,.techical_division_open ,.idea_productidea_open ,.product_division_open ,.product_npd_division_open ,.product_npd_open,
.implement_converted_open ,.implemented_convert_division_open ,.implemented_G_converted_open ,.implement_G_convert_divi_open
{
  cursor: pointer;
}

#pre_passed_stage , #pre_takenup_stage, #ld_stage1 ,#takenup_division_stage ,#pre_converted_stage , #converted_division_stage , #insight_technicalinsight_stage ,#technicalinsight_division_stage ,#idea_productidea_stage ,#productidea_division_stage,
#G_idea_productidea_stage ,#G_productidea_division_stage ,#implemented_converted_stage ,#implemented_converted_division_stage , #implement_G_convert_stage ,#implement_G_convert_divi_stage , #scientist_tl_stage
{
  display:none;
  padding: 10px 45px;
}

.ld_back ,.pre_passed_btn ,.pre_takeup_btn  ,.takeup_division_btn ,.pre_converted_btn ,.converted_division_btn ,.insight_technical_back ,.technical_division_back ,.idea_productidea_back ,.productidea_division_back ,.G_idea_productidea_back  ,.G_productidea_division_back,
.implemented_converted_back ,.implemented_converted_division_back ,.implement_G_convert_back ,.implement_G_convert_divi_back 
{
  font-size: 2rem;
  cursor: pointer;
}

/* ipr start */
.IPR_open ,.IPR_division_open ,.IPR_passed_open ,.filled_division_open,.IPR_grant_open ,.grant_division_open ,.IPR_other_open ,.RD_product_div_open
{
  cursor: pointer;
}

#IPR_stage1 ,#IPR_division_stage ,#IPR_filled_stage ,#IPR_filled_division_stage ,#IPR_grant_stage  ,#IPR_grant_division_stage ,#IPR_other_division_stage
{
  display: none;
  padding: 10px 45px;
}

.IPR_back ,.IPR_division_back ,.IPR_filled_back  ,.IPR_filled_division_back ,.IPR_grant_back ,.IPR_grant_division_back ,.IPR_other_division_back 
{
  font-size: 2rem;
  cursor: pointer;
}
/* ipr end */

/* R&D START */
.RD_open ,.RD_inprogress_open ,.rd_inprogress_ready_open ,.rd_inprogress_scientist_open ,.RD_ready_open ,.RD_RSScientist_open ,.RD_takenup_open ,.RD_nottaken_open ,.rdproduct_ready_open ,
.rd_product_scientist_open ,.RD_product_ready_open ,.RDproduct_RSScientist_open ,.RD_product_takenup_open ,.RD_product_nottakenup_open ,.rdproduct_innerdivision_open ,.RD_appliedreserch_open,
.RDapplied_completed_open ,.RDtakenup_open ,.RDnottakenup_open ,.RDinernal_inprogress_open ,.RDinternal_tech_open ,.RDcritical_inprogress_open ,.RDcritical_completed_open ,.RDcritical_division_open ,.RDCritical_division_open ,
.RDMethod_inprogress_open ,.RDMethod_implemented_open ,.preferredproduct_open ,.PP_division_open ,.PP_passed_open ,.Passed_division_open ,.PP_takenup_open ,.taken_division_open ,.PP_nottaken_open ,.nottaken_division_open ,
.productivity_outsourcing_open .division_progress
{ 
  cursor: pointer;
}

#RD_stage1 , #RD_inprogress_stage , #rd_ready_stage ,#RDscientist_stage ,#RD_ready_stage ,#RDReady_stage ,#RD_RSScientist_stage ,#rd_takenup_stage ,#rd_nottakenup_stage,
#RDdivision_stage ,#rdproduct_ready_stage ,#RDproduct_scientist_stage ,#RDproductReady_stage ,#RDproduct_RSScientist_stage ,#rdproduct_takenup_stage ,#rdproduct_nottakenup_stage,
#rdproduct_innerdivision_stage ,#RDapplied_reserch_stage ,#RDapplied_completed_stage ,#RDtakenup_stage ,#RDnottakenup_stage ,#RDinternal_inprogress_stage, #RDcritical_division_stage , #RDMethod_inprogress_stage ,
#RDinternal_tech_stage ,#RDcritical_inprogress_stage ,#RDcritical_completed_stage ,#RDMethod_implemented_stage ,#preferredproduct_stage ,#PP_division_stage ,#PP_passed_stage ,#passed_division_stage ,#PP_takenup_stage,
#PPtakenup_division_stage ,#PP_nottakenup_stage ,#nottakenup_division_stage ,#productivity_outsourcing_stage,#div_progress_stage
{ 
  display: none;
  padding: 10px 20px;
}
.RD_back ,.RD_inprogress_back ,.rd_ready_back ,.RDscientist_back ,.RDReady_back ,.RD_RSScientist_back ,.rd_takenup_back ,.rd_nottakenup_back ,.rdproduct_ready_back ,.RDdivision_back,
.RDproduct_scientist_back  ,.RDproduct_scientist_back1  ,.RDproductReady_back ,.RDproduct_RSScientist_back ,.rdproduct_takenup_back ,.rdproduct_nottakenup_back ,.rdproduct_innerdivision_back ,.RDapplied_reserch_back,
.RDapplied_completed_back ,.RDtakenup_back , .RDnottakenup_back ,.RDinternal_inprogress_back ,.RDinernal_inprogress_back , .RDinternal_tech_back ,.RDcritical_inprogress_back ,.RDcritical_completed_back ,.RDcritical_division_back ,
.RDMethod_inprogress_back ,.RDMethod_implemented_back ,.preferredproduct_back ,.PP_division_back ,.PP_passed_back ,.passed_division_back , .PP_takenup_back ,.taken_division_back ,.PPtakenup_division_back ,
.PP_nottakenup_back ,.nottakenup_division_back ,.productivity_outsourcing_back
{
  font-size: 2rem;
  cursor: pointer;
}

#RDled_stage ,#RDproduct_stage ,#RDapplied_stage ,#RDinternal_stage ,#RDcritical_stage ,#RDmethod_stage ,#pc1000_stage ,#pc100_stage , #pp1000_stage , #pp100_stage
{
  display: none;
  padding: 0px;
}
#RDled_open ,#RDproduct_open ,#RDapplied_open ,#RDinternal_open ,#RDcritical_open ,#RDmethod_open ,#pc1000_open ,#pc100_open , #pp1000_open ,#pp100_open 
{
  cursor: pointer;
}


/* R&D END */

.functional_90_business
{
  position: relative;
  bottom: 49px;
  left: 0;
}

.division_top_border{
  height:30px; border-top: 3px solid #fff; border-left: 3px solid #fff; position:relative;
}
.division_cricle
{
  position:absolute; left:-10px; top:20px; width:15px; height:15px; background:#fff; border-radius:50%;
}
.business_top_border1
{
  height:30px; border-top: 3px solid #fff;
}
.business_cricle
{
  position:absolute; left:-10px; top:20px; width:15px; height:15px; background:#fff; border-radius:50%;
}
.business_line
{
  height:30px; border-top: 3px solid #fff; border-left: 3px solid #fff; position:relative;
}
.status_top_border
{
  height:30px; border-top: 3px solid #fff;border-right: 3px solid #fff;position:relative;
}
.token_up_top
{
  height:30px; border-top: 3px solid #fff;position:relative;
}
.status_cricle
{
  position:absolute; left:-10px; top:20px; width:15px; height:15px; background:#fff; border-radius:50%;
}
.status_line{
  position:relative;
}
.wise_row
{
  position: relative;top: 40px;
}
.pre_wise_row
{
  position: relative;top: 40px;
}
.busniess_connect_line
{
  border-top: 2px dashed;position: relative;top: 81px;z-index: -1;
}
.division1_types {
  position: relative;
  top: 75px;
}
.rd_division1_types {
  position: relative;
  top: 0px;
}
.rd_border_top
{
  border-top:3px solid #fff;
}
.division_30_border
{
  background: #ffff;
  padding: 10px 20px 9px 20px;
  color: #000;
  position: absolute;
  top: -10px;
  font-size: 2rem;
  border-radius: 6px;
  z-index: 1;
  font-weight: 800;
}
.division1_border
{
  border: 1px solid #fff;
    padding: 10px 150px;
    border-radius: 6px;
    font-size: 2rem;
    background: #555a5dc2;
    position: relative;
}
.division1_wise_text {
   background: #9ebdbc;
    color: #000;
    padding: 2px 39px;
    font-size: 1.5rem;
    color:#214493;
    font-weight: 500;
    border-bottom-right-radius: 84px 55px;
}
.division_15_text {
  background: #8e8fc1;
  padding: 10px 10px;
  border-radius: 50px;
  left: 7px;
  position: relative;
}
/* .division_15_line
{
  width: 4%;
  position: absolute;
  height: 4px;
  background: #fff;
} */
.division_15_line
{
  border-top: 4px solid #fff;
  /* margin: 10px -12px 10px -45px; */
}
.perferred_text_inner_cricle1 {
  width: 50px;
  height: 51px;
  border: 5px solid #20a186;
  background-color: #fff;
  border-radius: 50px;
  padding: 5px;
  position: absolute;
  float: left;
  top: -28px !important;
  left: 83px;
  font-size: 20px;
  z-index: 1;
}
.ld_text_border span {
  background-color: #2073a169;
  padding: 6px 12px;
  position: absolute;
  bottom: 55px;
  color: #fff;
  font-size: 15px;
  font-size: 500 !important;
  cursor: pointer;
  white-space: nowrap;
  right: -18px;
}
.preferred_text_border span {
  background-color: #2073a169;
  padding: 6px 12px;
  position: absolute;
  bottom: 55px;
  color: #fff;
  font-size: 15px;
  font-size: 500 !important;
  cursor: pointer;
  white-space: nowrap;
  left: 0;
}
.PP_text_border span{
  background-color: #2073a169;
    padding: 6px 12px;
    position: absolute;
    bottom: 55px;
    color: #fff;
    font-size: 15px;
    font-size: 500 !important;
    cursor: pointer;
    white-space: nowrap;
    right: -36px;
}
.po_text_border span
{
  background-color: #2073a169;
    padding: 6px 12px;
    position: absolute;
    color: #fff;
    font-size: 15px;
    font-size: 500 !important;
    cursor: pointer;
    white-space: nowrap;
    left: 8px;
    top: 54px;
}
.light_triangle_top {
  width: 0px;
  height: 0px;
  border-top: solid 8px rgb(13 47 66);
  border-left: solid 5px transparent;
  border-right: solid 5px transparent;
  position: absolute;
  left: 22px;
  top: -14px;
}
.light_triangle_bottom {
  width: 0px;
  height: 0px;
  border-bottom: solid 8px rgb(13 47 66);
  border-left: solid 5px transparent;
  border-right: solid 5px transparent;
  position: absolute;
  left: 22px;
  top: 46px;
}
.perferred_cricle1
{
  width: 100px;
  height: 100px;
  border: 5px solid #fff;
  border-radius: 100px;
  background-color: #fff;
  color: #000;
  font-size: 1rem;
  position: absolute;
}
.perferred_cricle1 p
{
  position: absolute;
  top: 35px;
  left: 17px;
}

.perferred_text_inner_cricle2 {
  width: 50px;
    height: 51px;
    border: 5px solid #2073a1;
    background-color: #fff;
    border-radius: 50px;
    padding: 5px;
    position: absolute;
    float: left;
    top: 2px !important;
    left: 0;
    font-size: 20px;
    z-index: 1;
}
.perferred_text_inner_cricle3 {
  width: 50px;
  height: 50px;
  border: 5px solid #f58400;
  background-color: #fff;
  border-radius: 50px;
  padding: 5px;
  position: absolute;
  float: left;
  top: 82px !important;
  left: -20px;
  font-size: 20px;
  z-index: 1;
}
.perferred_text_inner_cricle4{
  width: 60px;
  height: 60px;
  border: 5px solid #2073a1;
  background-color: #fff;
  border-radius: 50px;
  padding: 12px;
  position: absolute;
  float: right;
  top: 44px !important;
  right: -20px;
  font-size: 20px;
  font-weight: 800;
  z-index: 1000;
}
.perferred_text_inner_cricle5
{
  width: 50px;
    height: 51px;
    border: 5px solid #4ee321d6;
    background-color: #fff;
    border-radius: 50px;
    padding: 5px;
    position: absolute;
    float: left;
    top: 173px !important;
    left: 149px;
    font-size: 20px;
    z-index: 1;
}
.perferred_text_inner_cricle6
{
  width: 50px;
    height: 50px;
    border: 5px solid #1b1c1d;
    background-color: #fff;
    border-radius: 50px;
    text-align: center !important;
    align-items: center !important;
    padding: 5px;
    position: absolute;
    top: 181px;
    left: 43px;
    font-size: 20px;
}
.single_below_line
{
  width: 50%;
    border-right: 3px solid #fff;
    height: 50px;
    position: relative;
    top: 0px;
}
.division_below_line
{
  width: 50%;
    border-right: 3px solid #fff;
    height: 50px;
    position: relative;
    top: 0px;
}
.preferred_functional_dash_cricle
{
  border: 2px dashed #9ECB3B;
  border-radius: 112px;
  width: 220px;
  height: 220px;
  padding: 7px;
  position: relative; 
  /* left:100px; */
}
.passed_text
{
  background: aliceblue;
  color: #000;
  border-radius: 0px;
  padding: 15px 55px;
  border-bottom-right-radius: 66px 41px;
  left: 32px;
  position: relative;
  font-size: 1.2rem;
}

.Token_Up_Business_text
{
  background: aliceblue;
  color: #000;
  border-radius: 0px;
  padding: 15px 24px; 
  border-bottom-right-radius: 66px 41px;
  left: 32px;
  position: relative;
  font-size: 1.2rem;
}

.converted_to_product_text
{
  background: aliceblue;
    color: #000;
    border-radius: 0px;
    padding: 15px 5px;
    border-bottom-right-radius: 66px 41px;
    left: 42px;
    position: relative;
    font-size: 1.2rem;
}
.division_wise_top
{
  height: auto;
    top: 34px;
    position: relative;
}
.business_wise_top
{
  height: auto;
}
.status_wise_top
{
  height: auto;
}
.divi_topic
{
  background: #fff;
    color: #000;
    padding: 2px 39px;
    font-size: 1.5rem;
    border-bottom-right-radius: 84px 55px;
}
.divi_line
{
  border-top: 4px solid #5153e2;
  /* margin: 10px -12px 10px -43px; */
}
.divi_left_number
{
  background: #5153e2;
  padding: 10px 10px;
  border-radius: 50px;
}
.divi_row {
  text-align: justify;
  left: 80px;
  position: relative;
}
.divi_30_row
{
  top:0;
  position: relative;
}
.division_30_row {
  top: 65px;
  position: relative;
}
.pre_text_row {
  top: 65px;
  position: relative;
}
.pre_divi_line
{
  border-top: 4px solid #5153e2;
    margin: 10px -29px 10px -15px;
}
.bus_btn
{
  padding: 0 41px;
}
.accordion-button:not(.collapsed)
{
  background-color: unset;
  box-shadow:unset;
}
.accordion-item
{
  border: unset ;
  background-color: unset;
}
.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1.5rem;
  color: #fff;
  text-align: left;
  background-color: unset;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,border-radius .15s ease;
}
.accordion-button::after {
  content: unset;
}
.accordion_division_15_text
{
  background: #8e8fc1;
  padding: 6px 6px;
  border-radius: 50px;
}
.accordion_divi_row h5 {
  text-align: justify;
  left: 30px;
  position: relative;
  font-size: 13px;
}
.accordion_division1_wise_text
{
  background: #9ebdbc;
    color: #000;
    padding:0;
    font-size: 13px;
    color: #214493;
    font-weight: 500;
    border-bottom-right-radius: 84px 55px;
}
.accordion_division1_types {
  position: relative;
  top: -13px;
  padding: 10px 0;
}
.deployed_status_row
{
  position: relative;
  border-radius: 6px;
  font-size: 18px;
  background: #262626;
  width: 100%;
  border: 1px solid #9c9c9c;
}
.status_below_cricle
{
  text-align: center;
    border-radius: 100px;
    width: 17px;
    height: 17px;
    background: #fff;
}
.accordion_status_wise_text {
  background: #9ebdbc;
  color: #000;
  padding: 0;
  font-size: 1.5rem;
  color: #214493;
  font-weight: 500;
  border-bottom-right-radius: 84px 55px;
  padding: 3px 16px;
}
.accordion_status_02_text
{
  background: #8e8fc1;
  padding: 6px 10px;
  border-radius: 50px;
  font-size: 1.5rem;
  position: relative;
  left: -17px;
}
.side_line {
  border-bottom: 3px solid #fff;
  margin: -15px -40px 0 18px;
}
.accordion_nodelay_line {
  height: 30px;
  /* border-top: 3px solid #fff; */
  border-left: 3px solid #fff;
  position: relative;
}
.status_side_line {
  border-bottom: 3px solid #fff;
  margin: -15px -91px 0 65px;
}
.accordion_bus_02_text
{
  background: #8e8fc1;
  padding: 6px 10px;
  border-radius: 50px;
  font-size: 1.5rem;
  position: relative;
  left: 30px;
}
.accord_bus_space
{
    padding: 10px 0;
    position: relative;
    left: -9px;
    width: 100%;
    background: #414447;
}


.cricle_30{
  background: #9796DA;
    padding: 10px 15px;
    border-radius: 50px;
}

.line_30
{
  
  border-top: 3px solid #7576b2;
  position: relative;
  top: 10px;
  left: -4px;
  z-index: -1;
}

.Business_Wise
{
  background: #DEE8EC;
    color: #000;
    padding: 8px 25px;
    font-size: 17px;
    border-bottom-right-radius: 84px 55px;
    position: relative;
    left: 70px;
    bottom: 30px;
    width: 60%;
}
.m-t-20
{
  margin-top:20px;
}
.div_left
{
  padding: 10px 10px 10px 50px;font-size:14px !important;
  cursor: pointer;
}
.div_three_col
{
  align-items: flex-start;left: 80px;position: relative;
}
/* .wise_row
{
  left: -30px;
} */
.fs_14
{
  font-size: 14px;
}
.status_Wise
{
  background: #DEE8EC;
    color: #000;
    padding: 8px 10px;
    font-size: 17px;
    border-bottom-right-radius: 84px 55px;
    position: relative;
    left: 70px;
    bottom: 30px;
    width: 52%;
}

.Business_Wise_inner {
  background: #AEBEB7;
  color: #000;
  padding: 8px 15px;
  font-size: 13px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: 43px;
  bottom: 40px;
  color: #00367D;
  font-weight: 600;
  width: 65%;
}

.div_left_inner {
  align-items: baseline;
  /* padding: 10px 10px 10px 40px; */
  font-size: 13px !important;
}
.cricle_02
{
  background: #7576b2;
  padding: 10px 12px;
  border-radius: 50px;
  font-size: 13px;
}
.m-t-20
{
  margin-top: 20px;
}
.division_line_15 {
  border-top: 3px solid #fff;
  position: relative;
  top: 10px;
  left: -4px;
}
.pro_div_bg
{
  background: #ececec52;
  padding: 25px 20px;
  margin: 25px;
  line-height: 2;
  width: 100%;
}
.accordion-body
{
  width: 200% !important;
}
.div_Business_Wise
{
    background: #AEBEB7;
    padding: 8px 25px;
    font-size: 17px;
    border-bottom-right-radius: 84px 55px;
    position: relative;
    left: 70px;
    bottom: 30px;
    width: 52%;
    color: #00367D;
    font-weight: 900;
}
.divi_status_Wise
{
  background: #AEBEB7;
  padding: 8px 25px;
  font-size: 17px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: 74px;
  color: #00367D;
  font-weight: 900;
  bottom: 30px;
  width: 54%;
}
.single_project_Wise
{
  background: #ececec;
    color: #000;
    padding: 8px 15px;
    font-size: 15px;
    border-bottom-right-radius: 84px 55px;
    position: relative;
    left: 50px;
    bottom: 30px;
    width: 80%;
    color: #17639b;
    font-weight: 900;
}
.project_Wise
{
  background: #ececec;
  color: #000;
  padding: 8px 15px;
  font-size: 15px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: 50px;
  bottom: 30px;
  width: 52%;
  color: #17639b;
  font-weight: 900 ;

}
.pro_div_bg {
  background: #ececec52;
  padding: 25px 20px;
  margin: 25px;
  line-height: 2;
  width: 100%;
}
/* .single_cricle{
  border: 1px solid #fff;
  width: 0;
  align-items: center;
  text-align: center;
  position: relative;
  left: 70px;
  top: 7px;
  height: 20px;
  background: #ffff;
  border-radius: 25px;

} */

.project_left_align
{
  text-align: initial;
    position: relative;
    font-size: 15px;
    margin-top: 10px ; 
    margin-bottom: 10px ; 
    /* text-align: center; */
}

.Large_text
{
  background: #DEE8EC;
    color: #000;
    padding: 8px 15px;
    font-size: 17px;
    border-bottom-right-radius: 84px 55px;
    position: relative;
    left: 50px;
    bottom: 30px;
    width: 52%;
}
.cricle_mobile_res
{
  margin-top: 95px;
}
.div_single_row {
  text-align: center !important;
}

/* 
  ##Device = Desktops
  ##Screen = 1281px to higher resolution desktops
*/

@media (min-width: 1281px) {
  
  /* CSS */
  
}

/* 
  ##Device = Laptops, Desktops
  ##Screen = B/w 1025px to 1280px
*/

@media (min-width: 1025px) and (max-width: 1280px) {
  
  /* CSS */
  
}

/* 
  ##Device = Tablets, Ipads (portrait)
  ##Screen = B/w 768px to 1024px
*/

@media (min-width: 768px) and (max-width: 1024px) {
  .delay_dash_cricle1 {
    border: 2px dashed #9ECB3B;
    border-radius: 112px;
    width: 120px;
    height: 120px;
    padding: 7px;
    position: relative;
    /* bottom: 70px;
    right: 100px; */
    bottom: 21px;
    left: 100px;
}
  .rd_project_cricle_blw
{
  position: relative;
  top: 100px;
  left: 67px ;
}

  .rd_pro_border
{
  border-radius: 6px;
  font-size: 18px;
  background: #40647dc2;
  text-align: left;
  width: 100%;
  border: 1px solid #fff;
  left: 57px;
  position: relative;
}
.preferred_functional_dash_cricle {

  left: 100px;
}
  .cricle1 ,.cricle1_inner {
    height: 200px;
    width: 200px;
  }
  .cricle3 {
    height: 200px;
    width: 200px;
}
  .NPD_text,.EPD_text,.Squeeze_text
  {
    font-size: 12px;
  }
  .text_90 {
    top: 30px;
    font-size: 50px;
  }
  .business_text {
    top: 88px;
    font-size: 26px;
  }
  .inner-cricle1 ,.inner-cricle2, .inner-cricle3 ,.functional_inner_cricle1, .functional_inner_cricle2,.functional_inner_cricle3 {
    width: 40px;
    height: 40px;
    padding: 6px;
    font-size: 15px;
  }
  .triangle-left {
    right: 30px;
    top: 4px;
  }
  .triangle-bottom {
    left: 5px;
    top: 30px;
  }
  .NPD_text
  {
    right: 46px;
    top: 6px;
  }
  .EPD_text {
    left: 45px;
  }
  .Squeeze_text {
    right: 50px;
      top: 15px;
  }
  .triangle-right {
    left: 30px;
    top: 4px;
  }
  .cricle2 {
    height: 290px;
    width: 290px;
  }
  .cricle_mobile_res
  {
    margin-top: 40px;
  }
  .inner-cricle1 {
    top: 19px !important;
    left: -9px;
  }
  .inner-cricle2
  {
    top: 20px !important;
    right: -5px;
  }
  .inner-cricle3 {
    bottom: -15px;
    left: 82px;
  }
  
  .Preferred_text {
    left: 62px;
    font-size: 13px;
  }
  .functional_text
  {
    font-size: 20px;
    top: 80px;
  }
  .IPR_text {
    right: 48px;
    top: 5px;
  }
  .LD_text {
    right: 38px;
    top: 13px;
    font-size: 13px;
  }
  .preferred_text {
    left: 47px;
    top: 0;
    font-size: 13px;
  }
  .functional_inner_cricle1 {
    bottom: 4px !important;
    left: 50px;
  }
  .functional_inner_cricle2 {
    bottom: 13px !important;
    right: 37px;
  }
  .functional_inner_cricle3
  {
    left: 116px;
  }
  .RD_text {
    left: -24px;
    top: 40px;
    font-size: 13px;
  }
  .Productivity_text {
    right: 40px;
    top: -5px;
    font-size: 13px;
    white-space:unset;
  }
  /*90_business */
  
  .npd_line , .division_top_border , .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.busniess_connect_line ,.token_up_top ,.npd_line_sm
  {
    display: none;
  }
  .functional_90_business {
    position: relative;
    bottom: 49px;
    left: 60px;
    top: 7px;
  }
  .functional_text_inner_cricle1,.functional_text_inner_cricle3 {
    width: 40px;
    height: 40px;
    font-size: 13px;
  }
  .functional_text_inner_cricle1 {
    top: 82px !important;
    left: -20px;
  }
  .EPD_text_border span
  {
    font-size: 13px;
    right: 19px;
    top: 40px;
  }
  .Squeeze_text_border
  {
    font-size: 13px;
    right: 19px;
    top: 40px;
  }
  .triangle-top {
    top: 32px;
  }
  .npd_15_row {
    top: 72px;
    position: relative;
    left: -53px;
  }
  .npd_cft_types
  {
    top: 75px;
  }
  .division1_border {
    padding: 10px 90px;
  }
  .status_Wise {
    padding: 8px 60px;
  }
  .npd_line , .division_top_border ,.division_below_line, .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.busniess_connect_line ,.token_up_top ,.division_below_line,
  .rd_delay_cricle
  {
    display: none;
  }
  .div_single_row {
    text-align: start !important;
    left: 50px !important;
    position: relative;
  }
  .division1_border {
    padding: 10px 90px;
  }
  .status_Wise {
    padding: 8px 60px;
  }
  /* 90functional */
  .functional_90_business1 {
    position: relative;
    bottom: 49px;
    left: -58px;
    top: 66px;
  }
  
  .ld_text_border span , .PP_text_border span,.po_text_border span ,.RD_text_border {
    font-size: 13px;
  }
  .po_text_border span
  {
    white-space:unset;
  }
  .NPD_CFT_border {
    /* padding: 10px 4px 13px 62px; */
    text-align: center;
    font-size: 20px;
  }
  .npd_cft_types1 {
    top: 3px;
    left: -40px;
    position: relative;
  }
  .Large_text {
    padding: 8px 14px;
    font-size: 15px;
  }
  .status_Wise ,.Business_Wise
  {
  font-size: 15px;
  }
  .pre_text_row {
    top: 95px;
    position: relative;
    left: 54px;
  }
  .pre_wise_row {
    position: relative;
    top: 70px;
  }
  .project_top
{
  margin-top:50px !important;
}
.pro_div_bg {
  margin: 0px 121px 20px 0;
}
.internal_cricle1 ,.internal_cricle2 ,.internal_left_line ,.internal_right_line ,.line_right ,.line_left
{
  display: none;
}
}

/* 
  ##Device = Tablets, Ipads (landscape)
  ##Screen = B/w 768px to 1024px
*/

@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  


  
}

/* 
  ##Device = Low Resolution Tablets, Mobiles (Landscape)
  ##Screen = B/w 481px to 767px
*/

@media (min-width: 481px) and (max-width: 767px) {
  .delay_dash_cricle1 {
    border: 2px dashed #9ECB3B;
    border-radius: 112px;
    width: 120px;
    height: 120px;
    padding: 7px;
    position: relative;
    /* bottom: 70px;
    right: 100px; */
    bottom: 21px;
    left: 100px;
}
  .rd_border_top
  {
    display: none;
  }
  .rd_project_cricle_blw {
    position: relative;
    top: 70px;
    left: 67px !important;
}
.npd_line_sm
{
  display: none;
}
  .preferred_functional_dash_cricle {

    left: 100px;
}
  .cricle1 ,.cricle1_inner {
    height: 200px;
    width: 200px;
  }
  .cricle3 {
    height: 200px;
    width: 200px;
}
  .NPD_text,.EPD_text,.Squeeze_text
  {
    font-size: 12px;
  }
  .text_90 {
    top: 30px;
    font-size: 50px;
  }
  .business_text {
    top: 88px;
    font-size: 26px;
  }
  .inner-cricle1 ,.inner-cricle2, .inner-cricle3 ,.functional_inner_cricle1, .functional_inner_cricle2,.functional_inner_cricle3 {
    width: 40px;
    height: 40px;
    padding: 6px;
    font-size: 15px;
  }
  .triangle-left {
    right: 30px;
    top: 4px;
  }
  .triangle-bottom {
    left: 5px;
    top: 30px;
  }
  .NPD_text
  {
    right: 46px;
    top: 6px;
  }
  .EPD_text {
    left: 45px;
  }
  .Squeeze_text {
    right: 50px;
      top: 15px;
  }
  .triangle-right {
    left: 30px;
    top: 4px;
  }
  .cricle2 {
    height: 290px;
    width: 290px;
  }
  .cricle_mobile_res
  {
    margin-top: 40px;
  }
  .inner-cricle1 {
    top: 19px !important;
    left: -9px;
  }
  .inner-cricle2
  {
    top: 20px !important;
    right: -5px;
  }
  .inner-cricle3 {
    bottom: -15px;
    left: 82px;
  }
  
  .Preferred_text {
    left: 62px;
    font-size: 13px;
  }
  .functional_text
  {
    font-size: 20px;
    top: 80px;
  }
  .IPR_text {
    right: 48px;
    top: 5px;
  }
  .LD_text {
    right: 38px;
    top: 13px;
    font-size: 13px;
  }
  .preferred_text {
    left: 47px;
    top: 0;
    font-size: 13px;
  }
  .functional_inner_cricle1 {
    bottom: 4px !important;
    left: 50px;
  }
  .functional_inner_cricle2 {
    bottom: 13px !important;
    right: 37px;
  }
  .functional_inner_cricle3
  {
    left: 116px;
  }
  .RD_text {
    left: -24px;
    top: 40px;
    font-size: 13px;
  }
  .Productivity_text {
    right: 40px;
    top: -5px;
    font-size: 13px;
    white-space:unset;
  }
   /*90_business */
  
   .npd_line , .division_top_border ,.division_below_line,    .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.busniess_connect_line ,.token_up_top ,.rd_delay_cricle
   {
     display: none;
   }
   .functional_90_business {
     position: relative;
     bottom: 49px;
     left: 60px;
     top: 7px;
   }
   .functional_text_inner_cricle1,.functional_text_inner_cricle3 {
     width: 40px;
     height: 40px;
     font-size: 13px;
   }
   .functional_text_inner_cricle1 {
     top: 82px !important;
     left: -20px;
   }
   .EPD_text_border span
   {
     font-size: 13px;
     right: 19px;
     top: 40px;
   }
   .Squeeze_text_border
   {
     font-size: 13px;
     right: 19px;
     top: 40px;
   }
   .triangle-top {
     top: 32px;
   }
   .npd_15_row {
     top: 72px;
     position: relative;
     left: -53px;
   }
   .npd_cft_types
   {
     top: 75px;
   }
   .division1_border {
     padding: 10px 90px;
   }
   .status_Wise {
     padding: 8px 60px;
   }
   .division1_border {
    padding: 10px 90px;
  }
  .status_Wise {
    padding: 8px 60px;
  }
  /* 90functional */
  .functional_90_business1 {
    position: relative;
    bottom: 49px;
    left: -58px;
    top: 66px;
  }
  
  .ld_text_border span , .PP_text_border span,.po_text_border span ,.RD_text_border {
    font-size: 13px;
  }
  .po_text_border span
  {
    white-space:unset;
  }
  .NPD_CFT_border {
    /* padding: 10px 4px 13px 62px; */
    text-align: center;
    font-size: 20px;
  }
  .npd_cft_types1 {
    top: 130px;
    left: -40px;
    position: relative;
  }
  .Large_text {
    padding: 8px 14px;
    font-size: 15px;
  }
  .status_Wise ,.Business_Wise
  {
  font-size: 15px;
  }
  .pre_text_row {
    top: 95px;
    position: relative;
    left: 54px;
  }
  .pre_wise_row {
    position: relative;
    top: 70px;
  }
  .project_top
{
  margin-top:50px !important;
}
.pro_div_bg {
  margin: 0px 72px 20px 0;
} 
.internal_cricle1 ,.internal_cricle2 ,.internal_left_line ,.internal_right_line ,.line_right ,.line_left
{
  display: none;
}
}

/* 
  ##Device = Most of the Smartphones Mobiles (Portrait)
  ##Screen = B/w 320px to 479px
*/

@media (min-width: 320px) and (max-width: 480px) {
  .preferred_functional_dash_cricle {
    left: 100px;
}
.rd_border_top
{
  display: none;
}
.npd_line_sm
{
  display: none;
}
.rd_project_cricle_blw
{
  position: relative;
  top: 100px;
  left: 67px !important;
}

.cricle1 ,.cricle1_inner {
  height: 200px;
  width: 200px;
}
.NPD_text,.EPD_text,.Squeeze_text
{
  font-size: 12px;
}
.text_90 {
  top: 30px;
  font-size: 50px;
}
.business_text {
  top: 88px;
  font-size: 26px;
}
.inner-cricle1 ,.inner-cricle2, .inner-cricle3 ,.functional_inner_cricle1, .functional_inner_cricle2,.functional_inner_cricle3 {
  width: 40px;
  height: 40px;
  padding: 6px;
  font-size: 15px;
}
.triangle-left {
  right: 30px;
  top: 4px;
}
.triangle-bottom {
  left: 5px;
  top: 30px;
}
.NPD_text
{
  right: 46px;
  top: 6px;
}
.EPD_text {
  left: 45px;
}
.Squeeze_text {
  right: 50px;
    top: 15px;
}
.triangle-right {
  left: 30px;
  top: 4px;
}
.cricle2 {
  height: 290px;
  width: 290px;
}
.cricle_mobile_res
{
  margin-top: 40px;
}
.inner-cricle1 {
  top: 19px !important;
  left: -9px;
}
.inner-cricle2
{
  top: 20px !important;
  right: -5px;
}
.inner-cricle3 {
  bottom: -15px;
  left: 82px;
}

.Preferred_text {
  left: 62px;
  font-size: 13px;
}
.functional_text
{
  font-size: 20px;
  top: 80px;
}
.IPR_text {
  right: 48px;
  top: 5px;
}
.LD_text {
  right: 38px;
  top: 13px;
  font-size: 13px;
}
.preferred_text {
  left: 47px;
  top: 0;
  font-size: 13px;
}
.functional_inner_cricle1 {
  bottom: 4px !important;
  left: 50px;
}
.functional_inner_cricle2 {
  bottom: 13px !important;
  right: 37px;
}
.functional_inner_cricle3
{
  left: 116px;
}
.RD_text {
  left: -24px;
  top: 40px;
  font-size: 13px;
}
.Productivity_text {
  right: 40px;
  top: -5px;
  font-size: 13px;
  white-space:unset;
}
/*90_business */

.npd_line , .division_top_border , .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.busniess_connect_line ,.token_up_top
{
  display: none;
}
.npd_line , .division_top_border , .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.division_below_line ,.single_cricle
{
  display: none;
}
.functional_90_business {
  position: relative;
  bottom: 49px;
  left: 60px;
  top: 7px;
}
.functional_text_inner_cricle1,.functional_text_inner_cricle3 {
  width: 40px;
  height: 40px;
  font-size: 13px;
}
.functional_text_inner_cricle1 {
  top: 82px !important;
  left: -20px;
}
.EPD_text_border span
{
  font-size: 13px;
  right: 19px;
  top: 40px;
}
.Squeeze_text_border
{
  font-size: 13px;
  right: 19px;
  top: 40px;
}
.triangle-top {
  top: 32px;
}
.npd_15_row {
  top: 72px;
  position: relative;
  left: -53px;
}
.npd_cft_types
{
  top: 75px;
}
.division1_border {
  padding: 10px 90px;
}
.status_Wise {
  padding: 8px 60px;
}
/* 90functional */
.functional_90_business1 {
  position: relative;
  bottom: 49px;
  left: -58px;
  top: 66px;
}

.ld_text_border span , .PP_text_border span,.po_text_border span ,.RD_text_border {
  font-size: 13px;
}
.po_text_border span
{
  white-space:unset;
}
.NPD_CFT_border {
  padding: 10px 4px 13px 80px;
  text-align: center;
  font-size: 20px;
}
.npd_cft_types1 {
  top: 5px;
  /* left: -40px; */
  position: relative;
}
.Large_text {
  padding: 8px 14px;
  font-size: 15px;
}
.status_Wise ,.Business_Wise
{
font-size: 15px;
}
.pre_text_row {
  top: 95px;
  position: relative;
  left: 54px;
}
.pre_wise_row {
  position: relative;
  top: 70px;
}
.Business_Wise_inner {
  padding: 7px 61px;
}
.project_top
{
  margin-top:50px !important;
}
.pro_div_bg {
  margin: 0px 72px 20px 0;
}
.ld_wise_row {
  position: relative;
  top: 124px;
  right: 75px;
  /* float: left; */
}
.ld_div_col {
  top: 148px;
}
.ld_wise_row {
  position: relative;
  top:0;
  right: 0px !important;
  left: 10px;
}
.internal_cricle1 ,.internal_cricle2 ,.internal_left_line ,.internal_right_line , .line_right ,.line_left
{
  display: none;
}
}

@media (min-width: 280px) and (max-width: 320px) {
  .cricle1 ,.cricle1_inner {
    height: 150px;
    width: 150px;
  }
  .npd_line_sm
{
  display: none;
}
  .rd_project_cricle_blw
{
  position: relative;
  top: 100px;
  left: 67px !important;
}
  .NPD_text,.EPD_text,.Squeeze_text
  {
    font-size: 12px;
  }
  .text_90 {
    top: 30px;
    font-size: 35px;
  }
  .business_text {
    top: 65px;
    font-size: 20px;
  }
  .inner-cricle1 ,.inner-cricle2, .inner-cricle3 ,.functional_inner_cricle1, .functional_inner_cricle2,.functional_inner_cricle3 {
    width: 35px;
    height: 35px;
    padding: 3px;
    font-size: 15px;
  }
  .triangle-left {
    right: 25px;
    top: 2px;;
  }
  .triangle-bottom {
    left: 2px;
    top: 25px;
  }
  .NPD_text
  {
    right: 40px;
    top: 3px;
  }
  .EPD_text {
    left: 45px;
  }
  .Squeeze_text {
    right: 50px;
      top: 15px;
  }
  .triangle-right {
    left: 26px;
    top: 2px;
  }
  .cricle2 {
    height: 250px;
    width: 250px;
  }
  .cricle_mobile_res
  {
    margin-top: 40px;
  }
  .inner-cricle1 {
    top: 1px !important;
    left: 6px;
  }
  .inner-cricle2
  {
    top: 2px !important;
    right: 2px;
  }
  .inner-cricle3 {
    bottom: -13px;
    left: 59px;
  }
  
  .Preferred_text {
    left: 44px;
    font-size: 13px;
    white-space: unset;
    top: -18px;
  }
  .functional_text
  {
    font-size: 20px;
    top: 80px;
  }
  .IPR_text {
    right: 48px;
    top: 5px;
  }
  .LD_text {
    right: 38px;
    top: 13px;
    font-size: 13px;
  }
  .functional_inner_cricle1 {
    bottom: -18px !important;
    left: 75px;
  }
  .functional_inner_cricle2 {
    bottom: -14px !important;
    right: 60px;
  }
  .functional_inner_cricle3
  {
    left: 116px;
    top: -25px;
  }
  .RD_text {
    left: -24px;
    top: 38px;
    font-size: 13px;
  }
  .Productivity_text {
    right: 40px;
    top: -5px;
    font-size: 13px;
    white-space:unset;
  }
  .preferred_text {
    left: 43px;
    top: 1px;
    font-size: 13px;
}

  /*90_business */
  
  .npd_line , .division_top_border , .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.busniess_connect_line ,.token_up_top
  {
    display: none;
  }
  .npd_line , .division_top_border , .business_top_border1 , .business_line , .status_top_border ,.status_cricle ,.division_below_line ,.single_cricle
{
  display: none;
}
  .functional_90_business {
    position: relative;
    bottom: 49px;
    left: 60px;
    top: 7px;
  }
  .functional_text_inner_cricle1,.functional_text_inner_cricle3 {
    width: 40px;
    height: 40px;
    font-size: 13px;
  }
  .functional_text_inner_cricle1 {
    top: 82px !important;
    left: -20px;
  }
  .EPD_text_border span
  {
    font-size: 13px;
    right: 19px;
    top: 40px;
  }
  .Squeeze_text_border
  {
    font-size: 13px;
    right: 19px;
    top: 40px;
  }
  .triangle-top {
    top: 32px;
  }
  .npd_15_row {
    top: 125px;
    left: -53px;
  }
  .npd_cft_types
  {
    top: 75px;
  }
  .division1_border {
    padding: 10px 70px;
  }
  .status_Wise {
    padding: 8px 60px;
  }
  .functional_cricle {
    width: 160px;
    height: 160px;
}
.functional_dash_cricle {
  width: 175px;
  height: 175px;
  left: -5px;
}
.functional_cricle span {
  top: 29px;
  left: 56px;
}
.functional_text_inner_cricle2 {
  width: 50px;
  height: 50px;
  padding: 10px;
  font-size: 17px;
}
.functional_text_inner_cricle3
{
  top: 148px;
}
.functional_cricle p {
  top: 75px;
  position: absolute;
  left: 23px;
}
.NPD_CFT_border {
  /* padding: 10px 17px 10px 80px; */
  text-align: center;
}
.div_left {
  align-items: baseline;
  padding:0; 
  font-size: 14px !important;
}
.wise_row {
  top: 103px;
  left: 0;
}
.div_three_col {
  left: 0;
}
.Business_Wise {
  background: #fff;
  color: #000;
  padding: 9px 10px;
  font-size: 12px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: -25px;
}
.status_Wise{
  background: #fff;
  color: #000;
  padding: 9px 25px;
  font-size: 12px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: -25px;
}
.divi_status_Wise {
  padding: 8px 55px;
}
.pro_div_bg {
  background: #ececec52;
  padding: 25px 0;
  margin: 0px 0 20px 108px;
}
.division1_border {
  padding: 10px 80px;
}
.status_Wise {
  padding: 8px 28px;
}
/* 90functional */
.functional_90_business1 {
  position: relative;
  bottom: 49px;
  left: -58px;
  top: 66px;
}

.ld_text_border span , .PP_text_border span,.po_text_border span ,.RD_text_border {
  font-size: 13px;
}
.po_text_border span
{
  white-space:unset;
}
.NPD_CFT_border {
  /* padding: 9px 4px 13px 37px; */
  text-align: center;
  font-size: 15px;
}
.npd_cft_types1 {
  top: 130px;
  left: -40px;
  position: relative;
}
.Large_text {
  padding: 8px 14px;
  font-size: 15px;
}
.status_Wise ,.Business_Wise
{
font-size: 12px;
}
.pre_text_row {
  top: 95px;
  position: relative;
  left: 54px;
}
.pre_wise_row {
  position: relative;
  top: 70px;
}
.perferred_functional_cricle {
  width: 170px;
  height: 170px;
}
.preferred_functional_dash_cricle {
  border: 2px dashed #659458;
  border-radius: 112px;
  width: 186px;
  height: 186px;
  padding: 7px;
  position: relative;
  left: 100px;
}
.perferred_functional_cricle span {
  top: 29px;
  position: absolute;
  left: 66px;
}
.perferred_functional_cricle p {
  top: 81px;
  position: absolute;
  left: 24px;
  font-size: 20px;
}

.perferred_text_inner_cricle1 , .perferred_text_inner_cricle2,.perferred_text_inner_cricle3 ,.perferred_text_inner_cricle6 ,.perferred_text_inner_cricle5  {
  width: 40px;
  height: 40px;
  font-size: 14px;
}
.ld_text_border span {
  bottom: 44px;
}
.PP_text_border span {
  bottom: 44px;
  font-size: 13px;
  white-space: unset;
}
.perferred_text_inner_cricle6 {
  top: 157px;
}
.RD_text_border {
  top: 40px;
}
.perferred_text_inner_cricle5 {
  top: 135px !important;
  left: 138px;
}
.po_text_border span {
  left: -40px;
}
.npd_15_border {
  padding: 10px 10px 10px 10px;
  font-size: 17px;
  white-space: nowrap;

}
.project_top
{
  margin-top:50px !important;
}
.pro_div_bg {
  margin: 0px 72px 20px 0;
}
}
/* ld start 26/04/23 */
.ld_div_col
{
  align-items: flex-start;
  position: relative;
  top: 10px;
}
/* .ld_wise_row
{
  position: relative;
  top: 40px;
  right: 100px;
} */
.ld_wise_row
{
  position: relative;
  top: 0px;
  left: 10px;
  /* right: 100px; */
}

.ld_row {
  position: relative;
}
.ld_border
{
  
  border-radius: 6px;
  font-size: 18px;
  background: #40647dc2;
  text-align: left;
  width: 100%;
}
.ld_15_border {
  background: #5974FF;
  color: #fff;
  top: 1px;
  font-size: 18px;
  border-radius: 6px;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.ld_inside_border
{
  border: 1px solid #fff;
  border-radius: 6px;
  font-size: 18px;
  background: #40647dc2;
  text-align: left;
  width: 100%;
  margin-left: 10px;
}

.ld_status_cricle
{
  position: absolute;
    left: 33px;
    top: -8px;
    width: 15px;
    height: 15px;
    background: #fff;
    border-radius: 50%;
}
.ld_last_cricle
{
  position: absolute;
    left: 0px;
    top: -8px;
    width: 15px;
    height: 15px;
    background: #fff;
    border-radius: 50%;
}
.ld_below_box
{
  margin-top: 30px;width: 500px; border-left:3px solid #fff; float: left;
}
.ld_below_last_box {
  margin-top: 30px;
  width: 500px;
  border-right: 3px solid #fff;
  float: right;
}
.ld_border_box
{
  border-top: 3px solid #fff;position: relative;left: 8px; top:12px;
}
.ld_last_border_box {
  border-top: 3px solid #fff;
  position: relative;
  left: -9px;
  top: 12px;
}
.ld_last_inside_border {
  border: 1px solid #fff;
  border-radius: 6px;
  font-size: 18px;
  background: #40647dc2;
  text-align: left;
  width: 100%;
}
#insight_open , #idea_in_open ,#ld_G_idea ,#ld_implemented , #implemented_1000g ,#ld_Scientist
{
  cursor: pointer;
}
#insight ,#idea_inprogress ,#idea ,#implemented_idea ,#implemented ,#scientist_ld
{
  display: none;
}
.line_right
{
  border-right: 3px solid #fff;
  height: 55px;
  float: right;
  margin-right: 50px;
  margin-top: 9px;
}
.line_left
{
  border-left: 3px solid #fff;
  height: 55px;
  float: left;
  margin-right: 20px;
  margin-top: 9px;
}
.division_bg_Wise {
  background: #fff;
  color: #000;
  padding: 5px 27px;
  font-size: 15px;
  border-bottom-right-radius: 84px 55px;
  position: relative;
  left: 50px;
  bottom: 30px;
  width: 52%;
}
.internal_cricle1
{
      width: 15px;
      height: 15px;
      background: #fff;
      border-radius: 50%;
      float: left;
      margin: 19px 0px 0 -17px;
      padding: 0;
}
.internal_cricle2
{
      width: 15px;
      height: 15px;
      background: #fff;
      border-radius: 50%;
      right: -116px;
      float: right;
      margin: 19px -17px 0 0px;
      padding: 0;
}
.internal_left_line
{
  border-top: 3px solid #fff;border-left: 3px solid #fff;height: 24px;
}
.internal_right_line
{
  border-top: 3px solid #fff;border-right: 3px solid #fff;height: 24px;
}

.functional_90_business1 {
  top: 60px;
  position: relative;
}

.rd_pro_border
{
  border-radius: 6px;
  font-size: 18px;
  background: #40647dc2;
  text-align: left;
  width: 80%;
  border: 1px solid #9c9c9c;
}
.rd_project_cricle_blw
{
  position: relative;
  top: 70px;
  left: 10px;
}
.npd_cft_blw
{
  position: relative;
  top: 70px;
right: -13px;
}
.npf_pro_border
{
  border-radius: 6px;
    font-size: 18px;
    background: #262626;
    text-align: left;
    width: 100%;
    border: 1px solid #9c9c9c;
}
.npf_count_border
{
  background: #fff;
  color: #000;
  top: 1px;
  font-size: 18px;
  border-radius: 6px;
  z-index: 1;
}
.npd_line_sm
{
  height: 75px;
    background: aliceblue;
    width: 4px;
    text-align: center;
    /* align-items: center; */
    position: relative;
    left: 272px;
    top: 2px;
    border-radius: 20px;
    margin: 0;

}
.mt-80{
  margin-top: 80px !important;
}
.cur-pointer{
  cursor: pointer;
}
.mw-10{
  max-width: 10% !important;
}
.rd_delay_cricle {
  position: relative;
  left: -10px;
  top: 46px;
  width: 15px;
  height: 15px;
  background: #fff;
  border-radius: 50%;
}
.modal-backdrop {
  width: unset; 
  height: unset;
  background-color: rgb(0 0 0 / 60%);
}
.modal {
  background-color: rgb(0 0 0 / 60%);
}
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #fff !important;
  pointer-events: auto;
  background-color: #8c8d8c94;
  background-clip: padding-box;
  border: 1px solid #161414;
  border-radius: 0.3rem;
  outline: 0;
}
.d-none{
  display: none;
}
.d-block{
  display: block;
}
.function_back {
  display: none;
  font-size: 2rem;
  cursor: pointer;
  padding: 10px 20px;
}

.busniesss_back{
  display: none;
  font-size: 2rem;
  cursor: pointer;
  padding: 10px 20px;
}
.business_single{
  display: grid;
  justify-content: center;
}
#functional_stage1 , .mt-70{margin-top:70px}
.mt-130{
  margin-top: 130px;
}
/* .btn-close {
  background: transparent url(../images/close_white.jpg) no-repeat !important;
} */