@include('layouts.main')

<head>
    @include('layouts.title-meta')
    <title>User Management | CK Projects</title> 
    @include('layouts.head-css')
</head>
@include('layouts.body')

 


<style>
    .jconfirm.jconfirm-white .jconfirm-box,
    .jconfirm.jconfirm-light .jconfirm-box {
        width: 100%;
    }
    
    .refineText .formTextbox{
        width: 100px;
    }
    .formTextbox:focus{
        border-color: #2196f3;
        outline: 0;
    }
</style>




<div class="holder">

    <div class="wrapper">

        @include('layouts.topbar')

        <!-- Add test Modal Start -->

        <div class="modal fade" id="add_test_sample_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Create Test Sample</h5>
                        <button type="button" class="btn btn-label-danger btn-icon form_reset" data-bs-dismiss="modal"><i
                                class="fa fa-times"></i></button>
                    </div>
                    <form id="preferedconceptform" action="#">
                        <div class="modal-body">
                            <div class="card-body">
                                <div class="row">

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Date</label>
                                            <input class="form-control" type="date" name="date" id="date" value="<?= date('Y-m-d'); ?>">                                   
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Work Type</label><br>
                                            <select class="form-control select2"  name="st_work_type" id="st_work_type" style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                                    <option value="assays">Assays </option>
                                                    <option value="formulation">Formulation </option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12" >
                                        <div class="mb-3 st_sub_work_type_div" style="display:none;">
                                            <label class="form-label">Sub Work Type</label><br>
                                            <select class="form-control select2"  name="st_sub_work_type" id="st_sub_work_type" style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                                    <option value="measurement">Measurent </option>
                                                    <option value="microbiology">Micro biology </option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Categoty</label><br>
                                            <select class="form-control select2"  name="category" id="category"
                                                style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Sample Code</label>
                                            <input class="form-control" type="text" name="sample_code" id="sample_code" placeholder="Sample Code">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Sample Details/Test</label><br>
                                            <select class="form-control select2"  name="sample_details_test" id="sample_details_test"
                                                style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Quantity</label>
                                            <input class="form-control" type="text" name="quantity" id="quantity" placeholder="Quantity">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Start Time For Single Points</label>
                                            <input class="form-control" type="time" name="start_time_single_point" id="start_time_single_point" >
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">End Time For Single Points</label>
                                            <input class="form-control" type="time" name="end_time_single_point" id="end_time_single_point" >
                                        </div>
                                    </div>


                                <div class="row measurement_div" style="display:none;">
                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Start Time For Analysis</label>
                                            <input class="form-control" type="time" name="start_time_analysis" id="start_time_analysis" >
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">End Time For Analysis</label>
                                            <input class="form-control" type="time" name="end_time_analysis" id="end_time_analysis" >
                                        </div>
                                    </div>
                                </div>



                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Actual Time Taken By HEPL in Minutes</label>
                                            <input class="form-control" type="text" name="actual_measure_time_hepl" id="actual_measure_time_hepl" readonly >
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Standard Time for single Point in Minutes</label>
                                            <input class="form-control" type="text" name="standard_time_single_point" id="standard_time_single_point" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Difference for Single Point</label>
                                            <input class="form-control" type="text" name="difference_single_point" id="difference_single_point" readonly value="20h 30m">
                                        </div>
                                    </div>

                                <div class="row measurement_div" style="display:none;">

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Actual Time Taken By Analysis in Minutes</label>
                                            <input class="form-control" type="text" name="actual_time_analysis" id="actual_time_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Standard Time Taken By HEPL in Minutes</label>
                                            <input class="form-control" type="text" name="standard_time_analysis" id="standard_time_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Difference for Single Analysis</label>
                                            <input class="form-control" type="text" name="diference_analysis" id="diference_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                </div>


                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control" name="remarks" id="remarks" placeholder="remarks"></textarea>
                                        </div>
                                    </div>
                                  
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary form_reset" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary" id="preferedconceptsave">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
     
        <!-- Add test Modal Start -->

        <!-- edit test Modal Start -->

        <div class="modal fade" id="edit_test_sample_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Create Test Sample</h5>
                        <button type="button" class="btn btn-label-danger btn-icon form_reset" data-bs-dismiss="modal"><i
                                class="fa fa-times"></i></button>
                    </div>
                    <form id="preferedconceptform" action="#">
                        <div class="modal-body">
                            <div class="card-body">
                                <div class="row">

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Date</label>
                                            <input class="form-control" type="date" name="date" id="date" value="<?= date('Y-m-d'); ?>">                                   
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Work Type</label><br>
                                            <select class="form-control select2"  name="st_work_type" id="st_work_type" style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                                    <option value="assays">Assays </option>
                                                    <option value="formulation">Formulation </option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12" >
                                        <div class="mb-3 st_sub_work_type_div" style="display:none;">
                                            <label class="form-label">Sub Work Type</label><br>
                                            <select class="form-control select2"  name="st_sub_work_type" id="st_sub_work_type" style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                                    <option value="measurement">Measurent </option>
                                                    <option value="microbiology">Micro biology </option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Categoty</label><br>
                                            <select class="form-control select2"  name="category" id="category"
                                                style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Sample Code</label>
                                            <input class="form-control" type="text" name="sample_code" id="sample_code" placeholder="Sample Code">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Sample Details/Test</label><br>
                                            <select class="form-control select2"  name="sample_details_test" id="sample_details_test"
                                                style="width:100%;">
                                                <option value="0" selected>---Select---</option>
                                            </select>

                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Quantity</label>
                                            <input class="form-control" type="text" name="quantity" id="quantity" placeholder="Quantity">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Start Time For Single Points</label>
                                            <input class="form-control" type="time" name="start_time_single_point" id="start_time_single_point">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">End Time For Single Points</label>
                                            <input class="form-control" type="time" name="end_time_single_point" id="end_time_single_point" >
                                        </div>
                                    </div>


                                <div class="row measurement_div" style="display:none;">
                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Start Time For Analysis</label>
                                            <input class="form-control" type="time" name="start_time_analysis" id="start_time_analysis" >
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">End Time For Analysis</label>
                                            <input class="form-control" type="time" name="end_time_analysis" id="end_time_analysis" >
                                        </div>
                                    </div>
                                </div>



                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Actual Time Taken By HEPL in Minutes</label>
                                            <input class="form-control" type="text" name="actual_time_hepl" id="actual_time_hepl" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Standard Time for single Point in Minutes</label>
                                            <input class="form-control" type="text" name="standard_time_single_point" id="standard_time_single_point" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Difference for Single Point</label>
                                            <input class="form-control" type="text" name="difference_single_point" id="difference_single_point" readonly value="20h 30m">
                                        </div>
                                    </div>

                                <div class="row measurement_div" style="display:none;">

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Actual Time Taken By Analysis in Minutes</label>
                                            <input class="form-control" type="text" name="actual_time_analysis" id="actual_time_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Standard Time Taken By HEPL in Minutes</label>
                                            <input class="form-control" type="text" name="standard_time_analysis" id="standard_time_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                    

                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Difference for Single Analysis</label>
                                            <input class="form-control" type="text" name="diference_analysis" id="diference_analysis" readonly value="20h 30m">
                                        </div>
                                    </div>

                                </div>


                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">Remarks</label>
                                            <textarea class="form-control" name="remarks" id="remarks" placeholder="remarks"></textarea>
                                        </div>
                                    </div>
                                  
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary form_reset" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary" id="preferedconceptsave">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
     
        <!-- edit test Modal Start -->

        <div class="content">
            <div class="container-fluid">
                <div class="content">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="portlet">
                                    <div class="portlet-header portlet-header-bordered">
                                        <h3 class="portlet-title">Test Sample
                                            <div class="page-title-right">

                                            </div>
                                    </div>

                                    <div class="portlet-body">
                                        <div class="row">

                                            <div class="col-2">
                                              
                                                    <button type="button" 
                                                        class="btn btn-primary waves-effect waves-light"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#add_test_sample_modal">Add Test Sample
                                                    </button>
                                             
                                            </div>
                                        </div>
                                    </div>


                                   
                                    <div class="portlet-body">

                                        <div class="mt-3">

                                            <table id="test_sample_table"
                                                class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Date</th>
                                                        <th>Work Type</th>
                                                        <th>Sub Work Type</th>
                                                        <th>Category</th>
                                                        <th>Sample Code<th>    
                                                        <th>Sample Details/Test</th>
                                                        <th>Quantity</th>
                                                        <th>start Time</th>
                                                        <th>end Time</th>
                                                        <th>Actual Time Taken By single Points in Mins</th>
                                                        <th>Standard Time for single Points in Mins</th>
                                                        <th>Difference</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td></td>
                                                        <td>03-04-2024</td>
                                                        <td>Internal</td>
                                                        <td>Test</td>
                                                        <td>Category 1</td>
                                                        <td>Test 1</td>
                                                        <td>20</td>
                                                        <td>10:00 AM</td>
                                                        <td>11:00 AM</td>
                                                        <td>20 min</td>
                                                        <td>10 min</td>
                                                        <td>10 min</td>
                                                        <td>20 min</td>
                                                        <td>05 min</td>
                                                        <td>
                                                            <a class="btn btn-success btn-icon" href="#" data-bs-toggle="modal"  data-bs-target="#editprojecttaskmodal" onclick="edit_sample_test(1)"><i class="fa-solid fa-pen-to-square"></i></a>&nbsp;
                                                            <a class="btn btn-danger btn-icon" href="#" data-bs-toggle="modal" data-bs-target="#delete_test_sample" onclick="delete_test_sample(1)"><i class="fa fa-trash"></i></a>&nbsp;
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>


                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

@include('layouts.footer')

</div>



</div>
@include('layouts.theme_change')
@include('layouts.left_menu')


@include('layouts.vendor-scripts')
<script type="text/javascript" src="{{ asset('app/pages/form/datepicker.js') }}"></script>
</body>

</html>
<script>
   
    $('.select2').select2();


    $(document).ready(function() {

        get_test_sample_list();


    });
    


 

    function get_test_sample_list() {

        
        var table1 = $("#test_sample_table").DataTable({
            "scrollX": true,
            "order": [1, 'asc'],
            fixedHeader: true,
            "pageLength": 10,
            "processing": true,
            // "serverSide": true,
            "paging": true,
            "ordering": false,
            "info": true,
            "searching": true,
            
            dom: 'Bfrtip',
            // buttons: [
            //     'copyHtml5',
            //     'excelHtml5',
            //     'csvHtml5',
            //     'pdfHtml5'

            // ],
            buttons: [
                
                {
                    extend: 'copy',
                    title: "Primus",
                    eexportOptions: {
                        modifier: {
                            selected: null
                        }
                    }
                },
                {
                    extend: 'csv',
                    title: "Primus",
                    exportOptions: {
                        modifier: {
                            selected: null
                        }
                    }
                },
                // 'excel',
                {
                    extend: 'pdf',
                    orientation : 'landscape',
                    pageSize : 'A2',
                    title: "Primus",
                    exportOptions: {
                        modifier: {
                            selected: null
                        }
                    }, 
                    customize : function(doc) {
                        doc.defaultStyle.fontSize = 6; //2, 3, 4,etc
                        doc.styles.tableHeader.fontSize = 8; //2, 3, 4, etc
                        // doc.styles['td:nth-child(8)'] = { 
                        //     width: '1000px',
                        //     'max-width': '1000px'
                        // }
                    },
                },
                {
                    extend: 'excel',
                    title: "Primus",
                    exportOptions: {
                        modifier: {
                            selected: null
                        }
                    }
                }
            ],
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
           /*  ajax: {
                url: '{{ url('get_prefered_concept_list') }}',
                type: 'post',               
                data: function (d) {
                d.monthgoal =  $('#monthgoal').val();
                d.scientistfilter_cl = $('#scientistfilter_cl').val();
                d.scientistfilter_tl = $('#scientistfilter').val();
                d.scientistfilter_member = $('#scientistfilter_member').val();
                d.statusfilter = $('#statusfilter').val();
                d.divisionfilter = $('#divisionfilter').val();
                d._token  =  $('#token').val();              
                },
                beforeSend: function() {},
               
                error: function(xhr, error, thrown) {
                    alert(error);
                }
            }, */
            "fnRowCallback": function(nRow, aData, iDisplayIndex) {
                $("td:first", nRow).html(iDisplayIndex + 1);
                return nRow;
            },

          /*   columns: [{
                    data: 'id',
                    name: 'id'
                },
                {
                    data: 'date',
                    name: 'date'
                },
                {
                    data: 'work_type',
                    name: 'work_type'
                },
                {
                    data: 'sub_work_type',
                    name: 'sub_work_type'
                },
                {
                    data: 'category',
                    name: 'category'
                },

                {
                    data: 'sample_code',
                    name: 'sample_code'
                },
                {
                    data: 'sample_details_test',
                    name: 'sample_details_test'
                },
                {
                    data: 'quantity',
                    name: 'quantity'
                },
                {
                    data: 'start_time_single_point',
                    name: 'start_time_single_point'
                },
                {
                    data: 'end_time_single_point',
                    name: 'end_time_single_point'
                },
                {
                    data: 'start_time_analysis',
                    name: 'start_time_analysis'
                },

                {
                    data: 'end_time_analysis',
                    name: 'end_time_analysis'
                },
                {
                    data: 'actual_time_hepl',
                    name: 'actual_time_hepl'
                },
                {
                    data: 'standard_time_single_point',
                    name: 'standard_time_single_point'
                },
                {
                    data: 'actual_time_analysis',
                    name: 'actual_time_analysis'
                },
                {
                    data: 'standard_time_analysis',
                    name: 'standard_time_analysis'
                },
                {
                    data: 'difference_single_point',
                    name: 'difference_single_point'
                },
                {
                    data: 'diference_analysis',
                    name: 'diference_analysis'
                },
                {
                    data: 'remarks',
                    name: 'remarks'
                },
                {
                    data: 'action',
                    name: 'action'
                },
                
            ] */
        });
    }

    function edit_sample_test(id) {
        $('#edit_test_sample_modal').modal('show');
    }


    function delete_test_sample(id) {
        $.confirm({
            title: 'Are you sure you want',
            content: '' +
            'Delete the Records?',
            buttons: {
                formSubmit: {
                    text: 'Delete',
                    btnClass: 'btn-blue',
                    action: function () {
                        $.ajax({
                            type: "POST",
                            url: '{{ url('test') }}',
                            data: {
                                id:id,
                                _token:"{{ csrf_token() }}"
                            },
                            success: function (data) {
                            if (data.length != 0) {
                                var t = JSON.parse(data);
                                if(t['response'] =='success'){
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'success',
                                        title: "Deleted Successfully",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                    // $("#prefered_concept_table").DataTable().ajax.reload();
                                    var dt = $("#test_sample_table").DataTable();
                                    dt.ajax.reload(null, false);
                                    setTimeout(function() {
                                    }, 2000);
                                }else{
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'error',
                                        title: "Delete Failed",
                                        showConfirmButton: false,
                                        customClass: 'swal-wide',
                                        timer: 1500
                                    });
                                }
                            }
                            }
                        });
                    }
                },
                cancel: function () {
                    //close
                    var newVal = $(e).val();
                    if(newVal==1){
                        $(e).val(0);
                    }else{
                        $(e).val(1);
                    }
                },
            },
        });
    }


    $(document).ready(function() {
        $('#start_time_single_point, #end_time_single_point').on('change', function() {
            var startTime = $('#start_time_single_point').val();
            var endTime = $('#end_time_single_point').val();

            if (startTime && endTime) {
                var start = new Date('1970-01-01T' + startTime + 'Z');
                var end = new Date('1970-01-01T' + endTime + 'Z');
                var diffInMinutes = (end - start) / (1000 * 60);
                console.log('Total minutes:', diffInMinutes);
                $('#actual_measure_time_hepl').val(diffInMinutes,"ms");
            }
        });
    });



    $('#st_work_type').change(function() {
    work_type = $('#st_work_type').val();
    if(work_type == 'formulation'){
        $('.st_sub_work_type_div').hide();      
    }else if(work_type == 'assays'){
        $('.st_sub_work_type_div').show();
    }else{
        $('.st_sub_work_type_div').hide();
    }
    
    });

$('#st_sub_work_type').change(function() {
    sub_work_type = $('#st_sub_work_type').val();
    if(sub_work_type == 'measurement'){
        $('.measurement_div').show();   
    }else if(sub_work_type == 'microbiology'){
        $('.measurement_div').hide();
    }else{
        $('.measurement_div').hide();
    }

});

</script>
