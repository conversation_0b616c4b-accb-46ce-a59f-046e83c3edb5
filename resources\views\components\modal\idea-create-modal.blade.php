<input type="hidden" name="authid" id="authid" value="{{ Auth::user()->id }}">
<div class="modal fade" id="add_preferedconcepts_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Create Ideas</h5>
                <button type="button" id="btn_close_ideas" class="btn btn-label-danger btn-icon btn_close_ideas" data-bs-dismiss="modal"><i class="fa fa-times"></i></button>
            </div>
            <form id="learning_devlopment_idea" method="POST">
                <div class="modal-body" id="refresh_ideas">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Category</label>
                                    <select class="form-control select2" name="idea_category" id="idea_category" style="width:100%;">
                                        <option value="0">Select</option>
                                        <option value="1">Parent Idea</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Type</label>
                                    <select class="form-control select2" name="idea_type" id="idea_type" style="width:100%;">
                                        <option value="0">Select</option>
                                        @foreach ($ideatype as $type)
                                        <option value="{{ $type->id }}">{{ $type->parent_idea }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Insight</label>
                                    <select class="form-control select2" name="idea_insight" id="idea_insight" multiple style="width:100%;">
                                        <option value="0">Select</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Co-Creator</label>
                                    <select class="form-control select2" name="scientist[]" id="scientist" multiple style="width:100%;">
                                    </select>
                                </div>
                            </div>

                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Idea Description</label>
                                    <textarea class="form-control" name="idea_txt" id="idea_txt" placeholder="Enter description of the idea"></textarea>
                                </div>
                            </div>

                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Comments</label>
                                    <textarea class="form-control" name="ideas_comments" id="ideas_comments" placeholder="Enter your comments"></textarea>
                                </div>
                            </div>

                            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Target Date</label>
                                    <input type="date" class="form-control" name="target_date" id="target_date" min="{{ date('Y-m-d') }}">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn_close_ideas" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="save_learning_idea_iiy" class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>
