<div class="modal fade" id="add_insights_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Insights</h5>
                <button type="button" class="btn btn-label-danger btn-icon" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <form id="learning_devlopment_insights">
                <div class="modal-body">
                    <div class="card-body">
                        <div class="row">
                            <!-- Insights Generated -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">Insights Generated <span class="text-danger">*</span></label>
                                    <textarea class="form-control" name="insight_generated" id="insight_generated"
                                        placeholder="Enter the insights"></textarea>
                                </div>
                            </div>

                            <!-- Insights Category -->
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Insights Category <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="insight_category" id="insight_category" style="width:100%;">
                                        <option value="">Select</option>
                                        @foreach ($insightcategoryhr as $category)
                                            <option value="{{ $category->id }}">{{ $category->source }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Scientist (Auto-fetched, disabled) -->
                            
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Packaging Technologist <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="scientist_insights" id="scientist_insights"
                                                style="width:100%;" disabled>
                                                <option value="{{ Auth::user()->id }}">{{ Auth::user()->name }}</option>

                                            </select>
                                </div>
                            </div>
                           

                            <!-- Remarks -->
                            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                <div class="mb-3">
                                    <label class="form-label">Remarks </label>
                                    <textarea class="form-control" name="insight_remarks" id="insight_remarks"
                                        placeholder="Enter Remarks"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>
