<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600&amp;family=Roboto+Mono&amp;display=swap" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/emojionearea/3.4.2/emojionearea.min.css" />



<style>
  .scroll-btn{
    margin-top: 50px;
  }
  .font_btn {
    display: none;
    position: fixed;
    left: -49px;
    top: 39px;
    padding: 8px;
    background: #ddd;
    text-align: center;
  }

  .floating-btn {
    top: 55% !important;
  }

  .font_color_btn {
    display: none;
    position: fixed;
    left: -73px;
    top: 88px;
    padding: 8px;
    background: #ddd;
    text-align: center;
    right: 41px;
  }

  .font_color_btn img {
    display: none;
  }

  .bg-orange {
    background-color: orange;
  }

  /* 
     {
    background: #22547c;
    } */

  body {
    display: flex;
    flex-direction: column;
    margin-top: 1%;
    justify-content: center;
    align-items: center;
  }

  /* #rowAdder {
                margin-left: 17px;
            } */

  .switch {
    position: relative;
    display: inline-block;
    width: 90px;
    height: 34px;

  }

  .switch input {
    display: none;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ca2222;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 34px;

  }

  .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked+.slider {
    background-color: #2ab934;
  }

  input:focus+.slider {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(55px);
  }

  /*------ ADDED CSS ---------*/
  .slider:after {
    content: 'NO';
    color: white;
    display: block;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    font-size: 10px;
    font-family: Verdana, sans-serif;
  }

  input:checked+.slider:after {
    content: 'YES';
  }

  /*------ end task delay CSS ---------*/

  .slider2 {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ca2222;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 34px;

  }

  .slider2:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked+.slider2 {
    background-color: #2ab934;
  }

  input:focus+.slider2 {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked+.slider2:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(55px);
  }

  .slider2:after {
    content: 'CFT';
    color: white;
    display: block;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    font-size: 10px;
    font-family: Verdana, sans-serif;
  }

  input:checked+.slider2:after {
    content: 'RD';
  }

  /*--------- END --------*/

  .searchBoxElement {
    background-color: white;
    border: 1px solid #aaa;
    position: absolute;
    max-height: 150px;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    line-height: 23px;
    list-style: none;
    z-index: 1;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .searchBoxElement span {
    padding: 0 5px;
  }


  .searchBoxElement li {
    background-color: white;
    color: black;
  }

  .searchBoxElement li:hover {
    background-color: #50a0ff;
    color: white;
  }

  .searchBoxElement li.selected {
    background-color: #50a0ff;
    color: white;
  }

  .swal2-container {
    z-index: 20000 !important;
  }

  .form-control-sm {
    min-height: calc(1.5em + (0.5rem + 2px));
    padding: 0.25rem 0.5rem;
    font-size: .875rem;
    border-radius: 0.2rem;
    height: 36px;
  }

  .swal2-container {
    z-index: 20000 !important;
  }

  .formTextbox {
    border: 1px solid #f2f2f2;
    padding: 5px;
    border-radius: 2px;
    width: 100% !important;
  }

  .dt-buttons {
    margin-bottom: 10px;
  }

  .dt-buttons button {
    margin: 7px 0;
  }

  .dataTables_scrollHead {
    -ms-overflow-style: none;
    scrollbar-width: none;
    position: relative;
    top: auto;
  }

  .scroll-head-in.transformed {
    transform: translateY(-10px);
    /* Adjust the translation value as needed */
  }

  .scroll-btn {
    position: fixed;
    z-index: 111;
    right: 0;
    top: 60%;
  }

  .scroll-head-in {
    overflow: scroll !important;
    position: fixed !important;
    top: 0px !important;
    width: calc(100% - 89px) !important;
    z-index: 99 !important;
    box-shadow: 5px 5px 12px #b9b9b9;
    transition: transform 0.5s ease;
  }

  .dataTables_scrollHead::-webkit-scrollbar {
    display: none;
  }
.fixedHeader-floating{
  box-shadow: 5px 5px 12px #b9b9b9;
    transition: transform 0.5s ease;
}
  /* .refineText{
    1px 0 5px 0 !important
} */
</style>
<link href="{{ URL::asset('build/styles/ltr-core.css')}}" rel="stylesheet" />
<link href="{{ URL::asset('build/styles/ltr-vendor.css')}}" rel="stylesheet" />

<link rel="stylesheet" href="{{ asset('external_assets/jquery-confirm.min.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/uploadfile.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/modal-loading.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/modal-loading-animate.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/alert/css/alertify.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/alert/css/alertify.min.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/alert/css/alertify.rtl.css') }}" />
<link rel="stylesheet" href="{{ asset('external_assets/alert/css/alertify.rtl.min.css') }}" />
{{-- <link rel="stylesheet" href="{{ asset('select2/select2.min.css') }}" /> --}}

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/10.5.1/sweetalert2.min.css">