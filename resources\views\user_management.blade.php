@include('layouts.main')

<head>
    @include('layouts.title-meta')
    <title>User Management | CK Projects</title> 
    @include('layouts.head-css')
</head>
@include('layouts.body')

 


<style>
    .jconfirm.jconfirm-white .jconfirm-box,
    .jconfirm.jconfirm-light .jconfirm-box {
        width: 100%;
    }
    
    .refineText .formTextbox{
        width: 100px;
    }
    .formTextbox:focus{
        border-color: #2196f3;
        outline: 0;
    }
</style>




<div class="holder">

    <div class="wrapper">

        @include('layouts.topbar')

        <!-- Add user Modal Start -->

        <div class="modal fade" id="user_modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modal_head_title_user">Create User</h5>
                        <button type="button" class="btn btn-label-danger btn-icon form_reset" data-bs-dismiss="modal"><i
                                class="fa fa-times"></i></button>
                    </div>
                    <form id="preferedconceptform" action="#">
                        <div class="modal-body">
                            <div class="card-body">
                                <div class="row">

                                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Employee Code</label>
                                            <input class="form-control" type="text" name="emp_code" id="emp_code" value="">                                   
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Employee Name</label>
                                            <input class="form-control" type="text" name="emp_name" id="emp_name" value="">                                   
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Employee Email</label>
                                            <input class="form-control" type="text" name="emp_email" id="emp_email" value="">                                   
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Type of work</label>
                                            <select class="form-control select2"  name="emp_work_type" id="emp_work_type" style="width:100%;" multiple>
                                                <option value="" selected disabled>---Select---</option>    
                                                <option value="1">Formulation</option>
                                                <option value="2">Assays</option>
                                            </select> 
                                        </div>
                                    </div>

                                    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                                        <div class="mb-3">
                                            <label class="form-label">Division</label>
                                            <select class="form-control select2"  name="emp_division" id="emp_division" style="width:100%;">
                                                <option value="" selected disabled>---Select---</option>
                                                    <option value="1">Division 1 </option>
                                                    <option value="2">Division 2 </option>
                                            </select>                               
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary form_reset" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary" id="preferedconceptsave">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
     
        <!-- Add user Modal End -->

        

        <div class="content">
            <div class="container-fluid">
                <div class="content">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-12">
                                <div class="portlet">
                                    <div class="portlet-header portlet-header-bordered">
                                        <h3 class="portlet-title">User Management
                                            <div class="page-title-right">

                                            </div>
                                    </div>

                                    <div class="portlet-body">
                                        <div class="row">

                                            <div class="col-2">
                                              
                                                    <button type="button" 
                                                        class="btn btn-primary waves-effect waves-light"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#user_modal">Add User
                                                    </button>
                                             
                                            </div>
                                        </div>
                                    </div>


                                   
                                    <div class="portlet-body">

                                        <div class="mt-3">

                                            <table id="user_table"
                                                class="table table-bordered table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Employee Code</th>
                                                        <th>Employee Name</th>
                                                        <th>Email ID</th>
                                                        <th>Division</th>
                                                        <th>Type of work<th>    
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>1</td>
                                                        <td>900104</td>
                                                        <td>Premkumar M</td>
                                                        <td><EMAIL></td>
                                                        <td>IT</td>
                                                        <td>Formulation</td>
                                                        <td>
                                                            <a class="btn btn-success btn-icon" href="#" data-bs-toggle="modal"  data-bs-target="#editusermodal" onclick="edit_user(1)"><i class="fa-solid fa-pen-to-square"></i></a>&nbsp;
                                                            <a class="btn btn-danger btn-icon" href="#" data-bs-toggle="modal" data-bs-target="#delete_user" onclick="delete_user(1)"><i class="fa fa-trash"></i></a>&nbsp;
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>


                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

@include('layouts.footer')

</div>



</div>
@include('layouts.theme_change')
@include('layouts.left_menu')


@include('layouts.vendor-scripts')
<script type="text/javascript" src="{{ asset('app/pages/form/datepicker.js') }}"></script>
</body>

</html>
<script>
   
    $('.select2').select2();


    $(document).ready(function() {

        get_user_list();


    });
    
   /*  $(document).ready(function() {
        var table = $("#user_list1").DataTable({
            "order": [0, 'desc'],
            "scrollX": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "paging": true,
            "ordering": false,
            "info": true,
            "searching": true,
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": true,
                "searchable": true,
            }],
            ajax: {
                url: "{{ url('get_all_user') }}",
                type: 'post',
                data: {
                    _token: "{{ csrf_token() }}",
                },
                error: function(xhr, error, thrown) {
                    alert(error);
                }
            },
               "rowCallback": function (nRow, aData, iDisplayIndex) {
                        var oSettings = this.fnSettings ();
                         $("td:first", nRow).html(oSettings._iDisplayStart+iDisplayIndex +1);
                       return nRow;
                      }, 
            columns: [{
                    data: 'user_id',
                    name: 'user_id'
                },
                {
                    data: 'user_name',
                    name: 'user_name'
                },
                {
                    data: 'user_email',
                    name: 'user_email'
                },
                {
                    data: 'user_division',
                    name: 'user_division'
                },
                {
                    data: 'supervisor',
                    name: 'supervisor'
                },
                {
                    data: 'role',
                    name: 'role'
                },
                {
                    data: 'action',
                    name: 'action'
                },



            ]
        });



    });


    $("#add_users").click(function() {
        var name = $("#user_name").val();
        var division = $("#user_division").val();
        var email = $("#user_email").val();
        var supervisor_id = $("#user_supervisor_add").val();
        var is_supervisor = $("#user_is_supervisor").val(); 
        var role = $("#user_role").val();
        var mailformat = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        if ((name == '') || (email == '' )|| (supervisor_id == '0') || (is_supervisor == '') || (role == '') || (
                division == '')) {
            $.alert({
                title: 'Alert!',
                content: "Please fill all mandatory fields !!!",
            });
        }else if(!email.match(mailformat)){
            $.alert({
                title: 'Alert!',
                content: "Please Enter valid E-mail address !!!",
            });
        } else {
            $.ajax({
                type: "POST",
                url: "{{ url('save_new_user') }}",
                dataType: "json",
                data: {
                    name: name,
                    email: email,
                    division: division,
                    supervisor_id: supervisor_id,
                    is_supervisor: is_supervisor,
                    role: role,
                    _token: "{{ csrf_token() }}",
                },
                beforeSend: function() {},
                success: function(data) {
                    if (data == "success") {
                        
                        Swal.fire({
                            position: 'top-end',
                            icon: "success",
                            title: 'New User Added successfully',
                            showConfirmButton: false,
                            customClass: 'swal-wide',
                            timer: 1500
                        });
                        var table = $('#user_list').DataTable();
                        table.ajax.reload();
                        $("#update_user_list_modal_refresh").load(window.location.href + " #update_user_list_modal_refresh");
                        $('#add_users_modal').modal('toggle');
                        $("#user_name").val('');
                        $("#user_division").val('');
                        $("#user_email").val('');
                        $("#user_supervisor_add").val('');
                        $("#user_is_supervisor").val(''); 
                        $("#user_role").val('');
                       
                        
                    } else {
                        $.alert({
                            title: 'Alert!',
                            content: "Email Already Exists !!!",
                        });
                    }
                }
            });
        }
    }); */
    

    
/** Tab user Start */

function user_table()
    {
        $("#user_table").DataTable().destroy();
        var table41 = $('#user_table').DataTable({
            "scrollX": true,
            "order": [0, 'desc'],
            "pageLength": 25,
            "processing": true,
            "serverSide": true,
            "destroy": true,
            "columnDefs": [{
                "targets": 'no-sort',
                "orderable": false,
                "searchable": true,
            }],
            ajax: {
                url: '{{ url('get_all_user') }}',
                type: 'GET',
                data: {
                    "_token": "{{ csrf_token() }}",
                },

                beforeSend: function() {},
                error: function(xhr, error, thrown) {
                    //   alert(error);
                }
            },
            "rowCallback": function (nRow, aData, iDisplayIndex) {
                var oSettings = this.fnSettings ();
                $("td:first", nRow).html(oSettings._iDisplayStart+iDisplayIndex +1);
                return nRow;
            },
            columns: [{
                    data: 'id',
                    name: 'id'
                },

                {
                    data: 'name',
                    name: 'name'
                },
                {
                    data: 'worktype',
                    name: 'worktype'
                },
                {
                    data: 'subworktype',
                    name: 'subworktype'
                },
                {
                    data: 'category',
                    name: 'category'
                },

                {
                    data: 'action',
                    name: 'action'
                },

            ]
        });
        table41.ajax.reload();
    }
    $("#user_tab").click(function() {
        user_table();
    });
    $("#new_user").click(function() {
        $("#user_id").val('');
        $("#user_name").val('');
        $("#modal_head_title_user").text('Add user');
        $("#submit_user").text('Submit');
        
    });
    
    $("#submit_user").click(function() {
        var id = $("#user_id").val();
        var name = $("#user_name").val();
        var worktype=$("#user_worktype").val();
        var subworktype=$("#user_subworktype").val();
        var category=$("#user_category").val();
        if($('#submit_user').text()=='Update'){
            url= "{{ url('update_user') }}";
            actions='Update';
        }else{
            url= "{{ url('save_user') }}";
            actions='Add';
        }
        if ((name == '') || (worktype== '') || (subworktype == '') || (category == '')) {
            $.alert({
                title: 'Alert!',
                content: "Please fill all mandatory fields !!!",
            });
        } else {
            $.confirm({
                title: 'Are you sure',
                content: '' +
                'Work Type Details '+actions+'?',
                buttons: {
                    formSubmit: {
                        text: actions,
                        btnClass: 'btn-blue',
                        action: function () {
                            $.ajax({
                                type: "Post",
                                url: url,
                                dataType: "json",
                                data: {
                                    id: id,
                                    name: name,
                                    worktype:worktype,
                                    subworktype:subworktype,
                                    category:category,
                                    _token: "{{ csrf_token() }}",
                                },
                                beforeSend: function() {},
                                success: function(data) {
                                    console.log(data);
                                    if (data.action== "success") {
                                        Swal.fire({
                                            position: 'top-end',
                                            icon: data.action,
                                            title: data.message,
                                            showConfirmButton: false,
                                            customClass: 'swal-wide',
                                            timer: 1500
                                        });
                                        var table = $('#user_table').DataTable();
                                        $('#user_modal').modal('toggle');
                                        table.ajax.reload();

                                    } else {
                                        Swal.fire({
                                            position: 'top-end',
                                            icon: data.action,
                                            title: data.message,
                                            showConfirmButton: false,
                                            customClass: 'swal-wide',
                                            timer: 1500
                                        });
                                    }

                                }
                            });
                        }
                    },
                    cancel: function () {
                        //close
                    },
                },
            });
        }
    });
    function edit_user(id) {
        $("#user_name").val('');
        $("#user_worktype").val('');
        $("#user_modal").modal('show');
        $.ajax({
            type: "GET",
            url: "{{ url('edit_user') }}",
            data: {
                id:id,
                _token:"{{ csrf_token() }}",
            },
            success: function (data) {
                if (data.length != 0) {
                    $("#modal_head_title_user").text('Edit Work type');
                    $("#submit_user").text('Update');
                    $("#user_id").val(data[0].id);
                    $("#user_name").val(data[0].user);
                    //$("#user_worktype").val(data[0].worktype_id);
                    $("#user_subworktype").val(data[0].subworktype_id);
                }
            }
        });
    }
    function delete_user(id) {
        $.confirm({
            title: 'Delete Work type',
            content: 'Are you sure  want to Delete?',
            type: 'red',
            typeAnimated: true,
            buttons: {
                tryAgain: {
                    text: 'Delete',
                    btnClass: 'btn-red',
                    action: function() {
                        $.ajax({
                            type: "post",
                            url: "{{ url('delete_user') }}",
                            data: {
                                "_token": "{{ csrf_token() }}",
                                'id': id,

                            },
                            success: function(data) {
                                console.log(data);
                                Swal.fire({
                                    position: 'top-end',
                                    icon: data.action,
                                    title: data.message,
                                    showConfirmButton: false,
                                    customClass: 'swal-wide',
                                    timer: 1500
                                });
                                var table = $('#user_table').DataTable();
                                table.ajax.reload();
                            }
                        });
                    }
                },
                close: function() {
                }
            }
        });
    }
    
    $('#tab44-tab').on('click', function () {
        user_table();
    });


/** Tab sample test Ends */
 

   

   

</script>
