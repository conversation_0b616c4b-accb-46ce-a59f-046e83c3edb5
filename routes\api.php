<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('business_list',[App\Http\Controllers\ApiController::class, 'business_list']);
Route::get('functional_list',[App\Http\Controllers\ApiController::class, 'functional_list']);
Route::get('ideas_list',[App\Http\Controllers\ApiController::class, 'ideas_list']);
Route::get('business_list_one_to_one',[App\Http\Controllers\ApiController::class, 'business_list_one_to_one']);
Route::get('function_list_one_to_one',[App\Http\Controllers\ApiController::class, 'function_list_one_to_one']);
Route::get('ideas_list_one_to_one',[App\Http\Controllers\ApiController::class, 'ideas_list_one_to_one']);
