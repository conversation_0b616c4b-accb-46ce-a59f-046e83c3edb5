<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
/* Route::get('/', function () {
    return view('welcome');
}); */
Route::get('overall_view/{id}', 'App\Http\Controllers\HomeController@overall_view')->name('overall_view');

Auth::routes();
Route::get('login_user', '\App\Http\Controllers\Auth\LoginController@login_user');
Route::get('/', [App\Http\Controllers\HomeController::class, 'index']);
Route::get('/home', [App\Http\Controllers\HomeController::class, 'index']);
Route::get('/project_dashboard', [App\Http\Controllers\HomeController::class, 'project_dashboard']);
Route::get('/bussiness', [App\Http\Controllers\HomeController::class, 'bussiness']);
//Route::get('/functional', [App\Http\Controllers\HomeController::class, 'functional']);

//////////new Add ////////////////
Route::get('product_cate', [App\Http\Controllers\HomeController::class, 'product_cate']);
Route::get('scientist_colab', [App\Http\Controllers\HomeController::class, 'scientist_colab']);
Route::get('project_tasks_get', [App\Http\Controllers\HomeController::class, 'project_tasks_get']);
Route::get('project_categories', [App\Http\Controllers\HomeController::class, 'project_categories']);
Route::any('project_details', [App\Http\Controllers\HomeController::class, 'project_details']);
Route::any('head_user_project_details', [App\Http\Controllers\HomeController::class, 'head_user_project_details']);
//////////////////////////////////

// ->middleware('Admin')->name('dashboard');
// Route::get('project_dashboard', 'App\Http\Controllers\AdminController@project_dashboard');
// ->middleware('Admin')->name('project_dashboard');
Route::get('users', 'App\Http\Controllers\AdminController@users');
// ->middleware('Admin')->name('users');
Route::get('messages', 'App\Http\Controllers\AdminController@messages');
// ->middleware('Admin')->name('messages');
Route::get('events', 'App\Http\Controllers\AdminController@events');
// ->middleware('Admin')->name('events');
Route::get('cfr', 'App\Http\Controllers\AdminController@cfr');
// ->middleware('Admin')->name('cfr');
//Route::get('overall_view', 'App\Http\Controllers\AdminController@overall_view')->middleware('Admin')->name('overall_view');
Route::get('logout', '\App\Http\Controllers\Auth\LoginController@logout');
Route::post('save_yearly_form', 'App\Http\Controllers\AdminController@save_yearly_form');
Route::post('update_cfr_status', 'App\Http\Controllers\AdminController@update_cfr_status');
Route::post('delete_cfr', 'App\Http\Controllers\AdminController@delete_cfr');
Route::post('update_cfr', 'App\Http\Controllers\AdminController@update_cfr');
Route::post('add_new_user', 'App\Http\Controllers\AdminController@add_new_user');
Route::post('get_all_problem', 'App\Http\Controllers\AdminController@get_all_problem');
Route::post('get_all_major_setbacks', 'App\Http\Controllers\AdminController@get_all_major_setbacks');
Route::post('get_yearly_table_list_admin', 'App\Http\Controllers\AdminController@get_yearly_table_list_admin');
Route::post('get_quarterly_table_list_admin', 'App\Http\Controllers\AdminController@get_quarterly_table_list_admin');
Route::post('get_monthly_table_list_admin', 'App\Http\Controllers\AdminController@get_monthly_table_list_admin');
Route::post('get_weekly_table_list_admin', 'App\Http\Controllers\AdminController@get_weekly_table_list_admin');
Route::post('update_deadline_admin', 'App\Http\Controllers\AdminController@update_deadline_admin');
Route::post('update_objective_admin', 'App\Http\Controllers\AdminController@update_objective_admin');
Route::post('admin_approval', 'App\Http\Controllers\AdminController@admin_approval');
Route::post('level1_approval', 'App\Http\Controllers\Level1Controller@level1_approval');
Route::post('admin_approval_key_result', 'App\Http\Controllers\AdminController@admin_approval_key_result');
Route::post('admin_approval_key_initiative', 'App\Http\Controllers\AdminController@admin_approval_key_initiative');
Route::get('cfr', 'App\Http\Controllers\AdminController@cfr')->middleware('Admin')->name('cfr');
Route::get('get_all_cfr', 'App\Http\Controllers\AdminController@get_all_cfr')->middleware('Admin');
Route::post('save_new_cfr', 'App\Http\Controllers\AdminController@save_new_cfr')->middleware('Admin');
Route::post('get_feedback_list_admin', 'App\Http\Controllers\AdminController@get_feedback_list_admin');
Route::post('get_recognition_list_admin', 'App\Http\Controllers\AdminController@get_recognition_list_admin');
Route::get('dashboard_user', 'App\Http\Controllers\Level1Controller@dashboard_user')->middleware('Level1')->name('dashboard_user');
Route::get('project_dashboard_user', 'App\Http\Controllers\Level1Controller@project_dashboard_user')->middleware('Level1')->name('project_dashboard_user');
Route::get('messages_user', 'App\Http\Controllers\Level1Controller@messages_user')->middleware('Level1')->name('messages_user');
Route::get('events_user', 'App\Http\Controllers\Level1Controller@events_user')->middleware('Level1')->name('events_user');
Route::post('get_m_quarterly_monthly', 'App\Http\Controllers\Level1Controller@get_m_quarterly_monthly');
Route::post('get_m_division_monthly', 'App\Http\Controllers\Level1Controller@get_m_division_monthly');
Route::post('get_m_year_monthly', 'App\Http\Controllers\Level1Controller@get_m_year_monthly');
Route::post('get_m_objective_monthly', 'App\Http\Controllers\Level1Controller@get_m_objective_monthly');
Route::post('get_m_task_monthly_quarterly', 'App\Http\Controllers\Level1Controller@get_m_task_monthly_quarterly');
Route::post('get_m_user_weekly', 'App\Http\Controllers\Level1Controller@get_m_user_weekly');
Route::post('get_m_user_yearly', 'App\Http\Controllers\Level1Controller@get_m_user_yearly');
Route::post('save_quarterly_form', 'App\Http\Controllers\Level1Controller@save_quarterly_form');
Route::post('save_quarterly_table', 'App\Http\Controllers\Level1Controller@save_quarterly_table');
Route::post('save_monthly_form', 'App\Http\Controllers\Level1Controller@save_monthly_form');
Route::post('save_monthly_table', 'App\Http\Controllers\Level1Controller@save_monthly_table');
Route::post('save_monthly_table_weightage', 'App\Http\Controllers\Level1Controller@save_monthly_table_weightage');
Route::post('save_quarterly_table_target', 'App\Http\Controllers\Level1Controller@save_quarterly_table_target');
Route::post('get_yearly_table_list', 'App\Http\Controllers\Level1Controller@get_yearly_table_list');
Route::post('get_m_division_quarterly', 'App\Http\Controllers\Level1Controller@get_m_division_quarterly');
Route::post('get_m_year_quarterly', 'App\Http\Controllers\Level1Controller@get_m_year_quarterly');
Route::post('get_monthly_table_list', 'App\Http\Controllers\Level1Controller@get_monthly_table_list');
Route::post('get_quarterly_table_list', 'App\Http\Controllers\Level1Controller@get_quarterly_table_list');
Route::post('get_weekly_table_list', 'App\Http\Controllers\Level1Controller@get_weekly_table_list');
Route::post('get_m_division_weekly', 'App\Http\Controllers\Level1Controller@get_m_division_weekly');
Route::post('get_m_year_weekly', 'App\Http\Controllers\Level1Controller@get_m_year_weekly');
Route::post('get_m_objective_weekly', 'App\Http\Controllers\Level1Controller@get_m_objective_weekly');
Route::post('get_m_quarterly_weekly', 'App\Http\Controllers\Level1Controller@get_m_quarterly_weekly');
Route::post('get_m_keyresult_weekly', 'App\Http\Controllers\Level1Controller@get_m_keyresult_weekly');
Route::post('get_m_month_weekly', 'App\Http\Controllers\Level1Controller@get_m_month_weekly');
Route::post('save_weekly_form', 'App\Http\Controllers\Level1Controller@save_weekly_form');
Route::post('update_deadline', 'App\Http\Controllers\Level1Controller@update_deadline');
Route::post('save_feedback', 'App\Http\Controllers\Level1Controller@save_feedback');
Route::post('get_feedback_list', 'App\Http\Controllers\Level1Controller@get_feedback_list');
Route::post('delete_feedback', 'App\Http\Controllers\Level1Controller@delete_feedback');
Route::post('save_recognition', 'App\Http\Controllers\Level1Controller@save_recognition');
Route::post('get_recognition_list', 'App\Http\Controllers\Level1Controller@get_recognition_list');
Route::post('delete_recognition', 'App\Http\Controllers\Level1Controller@delete_recognition');
Route::get('dashboard_level2', 'App\Http\Controllers\Level2Controller@dashboard_level2')->middleware('Level2')->name('dashboard_level2');
Route::get('project_dashboard_level2', 'App\Http\Controllers\Level2Controller@project_dashboard_level2')->middleware('Level2')->name('project_dashboard_level2');
Route::post('get_weekly_table_list_week', 'App\Http\Controllers\Level2Controller@get_weekly_table_list_week');
Route::post('update_task_status', 'App\Http\Controllers\Level2Controller@update_task_status');
Route::get('dashboard_level2', 'App\Http\Controllers\Level2Controller@dashboard_level2')->middleware('Level2')->name('dashboard_level2');
Route::get('project_dashboard_level2', 'App\Http\Controllers\Level2Controller@project_dashboard_level2')->middleware('Level2')->name('project_dashboard_level2');
Route::post('get_weekly_table_list_week', 'App\Http\Controllers\Level2Controller@get_weekly_table_list_week');
Route::post('update_task_status', 'App\Http\Controllers\Level2Controller@update_task_status');
Route::post('update_inprogress_status', 'App\Http\Controllers\Level2Controller@update_inprogress_status');
Route::post('update_completed_status', 'App\Http\Controllers\Level2Controller@update_completed_status');
Route::post('update_delay_status', 'App\Http\Controllers\Level2Controller@update_delay_status');
Route::post('save_conversation', 'App\Http\Controllers\Level2Controller@save_conversation');
Route::post('get_feedback_list_level2', 'App\Http\Controllers\Level2Controller@get_feedback_list_level2');
Route::post('get_recognition_list_level2', 'App\Http\Controllers\Level2Controller@get_recognition_list_level2');
Route::any('select_project_reasonfordelay', 'App\Http\Controllers\AdminController@select_project_reasonfordelay');
//---------------------------------------------------- conversation----------------------------------------------//
Route::get('conversation_view', 'App\Http\Controllers\Level1Controller@conversation_view');
Route::get('teammatelist', 'App\Http\Controllers\Level1Controller@teammatelist');
Route::post('createconversation', 'App\Http\Controllers\Level1Controller@createconversation');
Route::get('reviewteammatelist', 'App\Http\Controllers\Level1Controller@reviewteammatelist');
Route::post('saverating', 'App\Http\Controllers\Level1Controller@saverating');
Route::any('/change_role', 'App\Http\Controllers\Level1Controller@change_role');
Route::GET('/reviewslist', 'App\Http\Controllers\Level1Controller@reviewslist')->name('reviewslist');
Route::GET('/reviewslistadmin', 'App\Http\Controllers\AdminController@reviewslistadmin')->name('reviewslistadmin');
Route::GET('/crf', 'App\Http\Controllers\AdminController@crf')->name('crf');
Route::GET('/crfsub/{id}/{month}', 'App\Http\Controllers\AdminController@crfsub')->name('crfsub');
Route::GET('/crfsub_month/{id}', 'App\Http\Controllers\AdminController@crfsub_month')->name('crfsub_month');
Route::GET('/conversation_badge/{id}', 'App\Http\Controllers\AdminController@conversation_badge')->name('conversation_badge');
Route::GET('/reviewslistadminsub', 'App\Http\Controllers\AdminController@reviewslistadminsub')->name('reviewslistadminsub');
Route::GET('/reviewslistadminsub_month', 'App\Http\Controllers\AdminController@reviewslistadminsub_month')->name('reviewslistadminsub_month');
Route::post('updaterating', 'App\Http\Controllers\AdminController@updaterating')->name('updaterating');
Route::post('update_recog', 'App\Http\Controllers\AdminController@update_recog')->name('update_recog');
Route::post('circle_percentage','App\Http\Controllers\AdminController@circle_percentage');
Route::post('delete_objective','App\Http\Controllers\AdminController@delete_objective');
Route::get('dashboardcfttask_list', [App\Http\Controllers\DashboardController::class, 'projectcfttask_list']);
Route::get('dashboardranddtask_list', [App\Http\Controllers\DashboardController::class, 'projectranddtask_list']);
Route::get('dashboardcoll_list', [App\Http\Controllers\DashboardController::class, 'collabrativecreate_list']);
Route::get('dashboardipr_list', [App\Http\Controllers\DashboardController::class, 'ipruser_list']);
Route::get('dashboardlandd_list', [App\Http\Controllers\DashboardController::class, 'get_learning_devlopment_idea_list']);
Route::get('dashboardpref_concept', [App\Http\Controllers\DashboardController::class, 'get_prefered_concept_list']);
Route::get('dashboardpref_product', [App\Http\Controllers\DashboardController::class, 'get_prefered_products_list']);
Route::get('dashboardprod_outsource', [App\Http\Controllers\DashboardController::class, 'get_productivity_outsourcing_list']);
Route::get('dashboardcomp_info', [App\Http\Controllers\DashboardController::class, 'get_competition_information_list']);
Route::get('dashboard', [App\Http\Controllers\HomeController::class, 'dashboard']);
// Route::get('dashboard', [App\Http\Controllers\AdminController::class, 'dashboard_new'])->middleware('authCheck');
// ---------------------------------- sugumar - -------------------------
Route::post('get_quarterly_table_list_project', 'App\Http\Controllers\Level1Controller@get_quarterly_table_list_project');
Route::post('get_all_achivements', [App\Http\Controllers\AdminController::class, 'get_all_achivements'])->name('get_all_achivements');
Route::post('get_goals_achivement_list', [App\Http\Controllers\AdminController::class, 'get_goals_achivement_list'])->name('get_goals_achivement_list');
Route::post('get_goals_achivement_list_division', [App\Http\Controllers\AdminController::class, 'get_goals_achivement_list_division']);
Route::post('get_all_support_required', [App\Http\Controllers\AdminController::class, 'get_all_support_required'])->name('get_all_support_required');
Route::post('get_all_human_resource', [App\Http\Controllers\AdminController::class, 'get_all_human_resource'])->name('get_all_human_resource');
Route::post('get_all_infrastructure', [App\Http\Controllers\AdminController::class, 'get_all_infrastructure'])->name('get_all_infrastructure');
Route::post('get_all_knowledgeenhancement', [App\Http\Controllers\AdminController::class, 'get_all_knowledgeenhancement'])->name('get_all_knowledgeenhancement');
Route::post('get_all_statutorycompliances', [App\Http\Controllers\AdminController::class, 'get_all_statutorycompliances'])->name('get_all_statutorycompliances');
Route::post('save_new_majorsetbacks', [App\Http\Controllers\AdminController::class, 'save_new_majorsetbacks']);

Route::post('save_problem_solved', [App\Http\Controllers\AdminController::class, 'save_problem_solved']);
Route::post('get_all_project_category', [App\Http\Controllers\AdminController::class, 'get_all_project_category']);
Route::post('update_project_category', [App\Http\Controllers\AdminController::class, 'update_project_category']);
Route::post('update_support', [App\Http\Controllers\AdminController::class, 'update_support']);
Route::post('update_human', [App\Http\Controllers\AdminController::class, 'update_human']);
Route::post('update_infrastructuredevelopment', [App\Http\Controllers\AdminController::class, 'update_infrastructuredevelopment']);
Route::post('update_knowledgeenhancement', [App\Http\Controllers\AdminController::class, 'update_knowledgeenhancement']);
Route::post('update_statutorycompliances', [App\Http\Controllers\AdminController::class, 'update_statutorycompliances']);
Route::post('updateinsight', [App\Http\Controllers\AdminController::class, 'updateinsight']);
Route::post('save_new_achivement', [App\Http\Controllers\AdminController::class, 'save_new_achivement']);
Route::post('save_new_support', [App\Http\Controllers\AdminController::class, 'save_new_support']);
Route::post('save_new_human', [App\Http\Controllers\AdminController::class, 'save_new_human']);
Route::post('save_new_infrastructure', [App\Http\Controllers\AdminController::class, 'save_new_infrastructure']);
Route::post('save_new_knowledgeenhancement', [App\Http\Controllers\AdminController::class, 'save_new_knowledgeenhancement']);
Route::post('save_new_statutorycompliances', [App\Http\Controllers\AdminController::class, 'save_new_statutorycompliances']);
//-------------
Route::post('save_innerproject', [App\Http\Controllers\AdminController::class, 'save_innerproject']);
Route::get('prefered_concepts', [App\Http\Controllers\HomeController::class, 'prefered_concepts']);
Route::get('prefered_products', [App\Http\Controllers\HomeController::class, 'prefered_products']);
Route::get('productivity_outsourcing', [App\Http\Controllers\HomeController::class, 'productivity_outsourcing']);
Route::get('update_scientist_forumlation_list', [App\Http\Controllers\HomeController::class, 'update_scientist_forumlation_list']);
Route::get('update_scientist_assays_list', [App\Http\Controllers\HomeController::class, 'update_scientist_assays_list']);
Route::get('learning_development', [App\Http\Controllers\HomeController::class, 'learning_development']);
Route::get('update_scientist_iiy_list', [App\Http\Controllers\HomeController::class, 'update_scientist_iiy_list']);
Route::get('ipr', [App\Http\Controllers\HomeController::class, 'ipr']);
Route::get('competition_information', [App\Http\Controllers\HomeController::class, 'competition_information']);
Route::get('intro', [App\Http\Controllers\HomeController::class, 'intro']);
Route::post('save_prefferedconcept', [App\Http\Controllers\AdminController::class, 'save_prefferedconcept']);
Route::post('save_prefferedproduct', [App\Http\Controllers\AdminController::class, 'save_prefferedproduct']);
Route::get('iprcreate_list', [App\Http\Controllers\AdminController::class, 'iprcreate_list']);
Route::post('createsubiprtask', [App\Http\Controllers\AdminController::class, 'createsubiprtask']);
Route::post('createiprtask', [App\Http\Controllers\AdminController::class, 'createiprtask']);
Route::get('cftcreate_list', [App\Http\Controllers\AdminController::class, 'cftcreate_list']);
Route::post('createcfttask', [App\Http\Controllers\AdminController::class, 'createcfttask']);
Route::post('createsubcfttask', [App\Http\Controllers\AdminController::class, 'createsubcfttask']);
Route::get('ipruser_list', [App\Http\Controllers\AdminController::class, 'ipruser_list']);
Route::get('iprstatus_list', [App\Http\Controllers\AdminController::class, 'iprstatus_list']);
Route::post('update_iprstatus',[App\Http\Controllers\AdminController::class, 'update_iprstatus']);
Route::post('update_iprstatus_list',[App\Http\Controllers\AdminController::class, 'update_iprstatus_list']);

Route::post('addpercentage', [App\Http\Controllers\AdminController::class, 'addpercentage']);

Route::get('collabrativecreate_list', [App\Http\Controllers\AdminController::class, 'collabrativecreate_list']);
Route::post('createcollareativetask', [App\Http\Controllers\AdminController::class, 'createcollareativetask']);
Route::post('updatetaskuser', [App\Http\Controllers\AdminController::class, 'updatetaskuser']);
Route::post('updatetaskstatus', [App\Http\Controllers\AdminController::class, 'updatetaskstatus']);
Route::post('updatetaskreasondelay', [App\Http\Controllers\AdminController::class, 'updatetaskreasondelay']);
Route::post('updatetaskcriticalupdate', [App\Http\Controllers\AdminController::class, 'updatetaskcriticalupdate']);
Route::post('updatetprefredconceptstatus', [App\Http\Controllers\AdminController::class, 'updatetprefredconceptstatus']);
Route::post('updatetprefredproductstatus', [App\Http\Controllers\AdminController::class, 'updatetprefredproductstatus']);
Route::post('updatecollabrativestatus', [App\Http\Controllers\AdminController::class, 'updatecollabrativestatus']);
Route::post('updateiprstatus', [App\Http\Controllers\AdminController::class, 'updateiprstatus']);
Route::post('getallcftdata', [App\Http\Controllers\AdminController::class, 'getallcftdata']);
Route::post('updatetaskdelay', [App\Http\Controllers\AdminController::class, 'updatetaskdelay']);
//modification
Route::get('fetch_insight_idea_list', [App\Http\Controllers\AdminController::class, 'fetch_insight_idea_list']);
Route::get('select_projectype', [App\Http\Controllers\AdminController::class, 'select_projectype']);
Route::get('get_reporting_scientist', [App\Http\Controllers\AdminController::class, 'get_reporting_scientist']);
Route::post('create_projecttask', [App\Http\Controllers\AdminController::class, 'create_projecttask']);
Route::get('projecttask_list', [App\Http\Controllers\AdminController::class, 'projecttask_list']);
Route::get('get_selectprojectstatus', [App\Http\Controllers\AdminController::class, 'get_selectprojectstatus']);
Route::post('updatetprojectstatus', [App\Http\Controllers\AdminController::class, 'updatetprojectstatus']);
Route::post('update_prefferedproduct', [App\Http\Controllers\AdminController::class, 'update_prefferedproduct']);
Route::post('updatetprojecttask', [App\Http\Controllers\AdminController::class, 'updatetprojecttask']);

// status letstatus_filter
Route::get('letstatus_filter', [App\Http\Controllers\AdminController::class, 'letstatus_filter']);
Route::get('letstatus', [App\Http\Controllers\AdminController::class, 'letstatus']);
Route::get('get_status', [App\Http\Controllers\AdminController::class, 'get_status']);
Route::get('get_status_filter', [App\Http\Controllers\AdminController::class, 'get_status_filter']);
Route::get('new_method_status_filter', [App\Http\Controllers\AdminController::class, 'new_method_status_filter']);
Route::get('critcal_rm_status_filter', [App\Http\Controllers\AdminController::class, 'critcal_rm_status_filter']);
Route::get('new_internal_status_filter', [App\Http\Controllers\AdminController::class, 'new_internal_status_filter']);
Route::get('new_method_status', [App\Http\Controllers\AdminController::class, 'new_method_status']);
Route::get('critcal_rm_status', [App\Http\Controllers\AdminController::class, 'critcal_rm_status']);
Route::get('new_internal_status', [App\Http\Controllers\AdminController::class, 'new_internal_status']);
//Route::get('get_topics_learnt_list', [App\Http\Controllers\AdminController::class, 'get_topics_learnt_list']);

Route::post('delete_project_landd', [App\Http\Controllers\AdminController::class, 'delete_project_landd']);
Route::get('/get-project-details/{id}', [App\Http\Controllers\AdminController::class, 'getProjectDetails']);
Route::post('/update-project/{id}', [App\Http\Controllers\AdminController::class, 'updateProject']);
Route::post('deleteprojecttask', [App\Http\Controllers\AdminController::class, 'deleteprojecttask']);
Route::post('updatecompetitontask', [App\Http\Controllers\AdminController::class, 'updatecompetitontask']);
Route::post('updatelearningidea', [App\Http\Controllers\AdminController::class, 'updatelearningidea']);
Route::get('goalsachivement', [App\Http\Controllers\HomeController::class, 'goalsachivement']);
Route::post('updateiprtasks', [App\Http\Controllers\AdminController::class, 'updateiprtasks']);
Route::post('save_goal_achivements', [App\Http\Controllers\AdminController::class, 'save_goal_achivements']);
Route::post('update_problem', [App\Http\Controllers\AdminController::class, 'update_problem']);
Route::post('update_major', [App\Http\Controllers\AdminController::class, 'update_major']);
Route::post('update_achivement', [App\Http\Controllers\AdminController::class, 'update_achivement']);
Route::get('masterachivement', [App\Http\Controllers\AdminController::class, 'masterachivement']);
Route::get('get_month_goal_achivements', [App\Http\Controllers\AdminController::class, 'get_month_goal_achivements']);
//
Route::post('get_prefered_concept_list', [App\Http\Controllers\AdminController::class, 'get_prefered_concept_list']);
Route::post('get_prefered_products_list', [App\Http\Controllers\AdminController::class, 'get_prefered_products_list']);
Route::post('get_project_category_list', [App\Http\Controllers\AdminController::class, 'get_project_category_list']);
Route::post('save_projectcategory', [App\Http\Controllers\AdminController::class, 'save_projectcategory']);
Route::post('save_learning_devlopment_idea', [App\Http\Controllers\AdminController::class, 'save_learning_devlopment_idea']);
Route::post('update_learning_devlopment_idea', [App\Http\Controllers\AdminController::class, 'update_learning_devlopment_idea']);
Route::post('get_learning_development', [App\Http\Controllers\HomeController::class, 'get_learning_development']);
Route::post('get_view_learning_development', [App\Http\Controllers\HomeController::class, 'get_view_learning_development']);
Route::post('save_learning_devlopment_insight', [App\Http\Controllers\AdminController::class, 'save_learning_devlopment_insight']);
Route::any('get_learning_devlopment_idea_list', [App\Http\Controllers\AdminController::class, 'get_learning_devlopment_idea_list']);
Route::post('save_learning_development', [App\Http\Controllers\AdminController::class, 'save_learning_development']);
Route::any('get_learning_devlopment_list', [App\Http\Controllers\AdminController::class, 'get_learning_devlopment_list']);
Route::get('get_learning_development_evaluation_list', [App\Http\Controllers\AdminController::class, 'get_learning_development_evaluation_list']);
Route::any('get_learning_devlopment_insight', [App\Http\Controllers\AdminController::class, 'get_learning_devlopment_insight']);
Route::post('save_productivity_outsourcing', [App\Http\Controllers\AdminController::class, 'save_productivity_outsourcing']);
Route::post('get_productivity_outsourcing_list', [App\Http\Controllers\AdminController::class, 'get_productivity_outsourcing_list']);
Route::post('get_productivity_outsourcing_list_Assays', [App\Http\Controllers\AdminController::class, 'get_productivity_outsourcing_list_Assays']);
Route::post('updateproductvitystatus', [App\Http\Controllers\AdminController::class, 'updateproductvitystatus']);
Route::post('get_productivity_outsourcing_list_overall', [App\Http\Controllers\AdminController::class, 'get_productivity_outsourcing_list_overall']);
Route::post('productivity_outsourcing_list_overall_Assays', [App\Http\Controllers\AdminController::class, 'productivity_outsourcing_list_overall_Assays']);
Route::post('save_competition_information', [App\Http\Controllers\AdminController::class, 'save_competition_information']);
Route::any('get_competition_information_list', [App\Http\Controllers\AdminController::class, 'get_competition_information_list']);
Route::any('update_iiy', [App\Http\Controllers\AdminController::class, 'update_iiy'])->name('update.iiy');
Route::any('view_iiy', [App\Http\Controllers\AdminController::class, 'view_iiy'])->name('view.iiy');
Route::any('get-insight-details', [App\Http\Controllers\AdminController::class, 'get_insight_details']);
Route::post('update-insight', [App\Http\Controllers\AdminController::class, 'update_insight']);
Route::get('get-idea-details', [App\Http\Controllers\AdminController::class, 'get_idea_details']);
Route::post('update_idea', [App\Http\Controllers\AdminController::class, 'update_idea']);
Route::post('get_area_of_dev', [App\Http\Controllers\AdminController::class, 'get_area_of_dev']);
Route::post('add_project', [App\Http\Controllers\AdminController::class, 'add_project']);

//sugu// In your routes/web.php or api.php (depending on your setup)
Route::post('delete_file', [App\Http\Controllers\AdminController::class, 'deleteFile'])->name('delete.file');

Route::post('update_prefferedconcept', 'App\Http\Controllers\AdminController@update_prefferedconcept');


Route::any('/login/callback_hemas', 'App\Http\Controllers\Auth\LoginController@microsoft_callback_hemas');
Route::get('/login/callback_cavincare', 'App\Http\Controllers\Auth\LoginController@microsoft_callback_cavincare');
Route::post('update_projecttask', [App\Http\Controllers\AdminController::class, 'update_projecttask']);
Route::any('edit_details_project_task', [App\Http\Controllers\AdminController::class, 'edit_details_project_task']);
Route::post('get_NPD_delay_status', [App\Http\Controllers\AdminController::class, 'get_NPD_delay_status']);
Route::post('get_NPD_delay_status_row', [App\Http\Controllers\AdminController::class, 'get_NPD_delay_status_row']);
//new
Route::post('select_status', [App\Http\Controllers\AdminController::class, 'select_status']);
Route::post('fselect_status', [App\Http\Controllers\AdminController::class, 'fselect_status']);
Route::any('get_scientist_insight', [App\Http\Controllers\HomeController::class, 'get_scientist_insight']);
Route::any('get_scientist_insight_edit', [App\Http\Controllers\HomeController::class, 'get_scientist_insight_edit']);
Route::post('get_parent_descri', [App\Http\Controllers\HomeController::class, 'get_parent_descri']);
Route::any('get_parent_idea', [App\Http\Controllers\AdminController::class, 'get_parent_idea']);
//newcorection
Route::any('project_details', [App\Http\Controllers\HomeController::class, 'project_details']);


Route::match(['GET','POST'],'/edit_learning_development', [App\Http\Controllers\AdminController::class, 'edit_learning_development'])->name('edit_learning_development');
Route::match(['GET','POST'],'/update_learning_development', [App\Http\Controllers\AdminController::class, 'update_learning_development'])->name('update_learning_development');
Route::get('/get_learning_development_details', [App\Http\Controllers\AdminController::class, 'getDetails']);

Route::get('dashboard_new', 'App\Http\Controllers\AdminController@dashboard_new');
Route::post('deletePreferedConcepts', [App\Http\Controllers\AdminController::class, 'deletePreferedConcepts']);
Route::post('deletePreferedProducts', [App\Http\Controllers\AdminController::class, 'deletePreferedProducts']);
Route::post('deleteL_D_idea', [App\Http\Controllers\AdminController::class, 'deleteL_D_idea']);
Route::post('deleteL_D_insight', [App\Http\Controllers\AdminController::class, 'deleteL_D_insight']);
Route::post('deleteIPR', [App\Http\Controllers\AdminController::class, 'deleteIPR']);
Route::post('delete_productivity_outsourcing', [App\Http\Controllers\AdminController::class, 'delete_productivity_outsourcing']);
Route::post('delete_lnd', [App\Http\Controllers\AdminController::class, 'delete_lnd']);
Route::post('delete_competition_information', [App\Http\Controllers\AdminController::class, 'delete_competition_information']);
Route::post('delete_all_achivements', [App\Http\Controllers\AdminController::class, 'delete_all_achivements'])->name('delete_all_achivements');
Route::post('delete_problem_solved', 'App\Http\Controllers\AdminController@delete_problem_solved');
Route::post('delete_major_setbacks', 'App\Http\Controllers\AdminController@delete_major_setbacks');
Route::post('delete_support_required', [App\Http\Controllers\AdminController::class, 'delete_support_required'])->name('delete_support_required');
Route::post('delete_human_resource', [App\Http\Controllers\AdminController::class, 'delete_human_resource'])->name('delete_human_resource');
Route::post('delete_infrastructure', [App\Http\Controllers\AdminController::class, 'delete_infrastructure'])->name('delete_infrastructure');
Route::post('delete_knowledgeenhancement', [App\Http\Controllers\AdminController::class, 'delete_knowledgeenhancement'])->name('delete_knowledgeenhancement');
Route::post('delete_statutorycompliances', [App\Http\Controllers\AdminController::class, 'delete_statutorycompliances'])->name('delete_statutorycompliances');

Route::get('/login/microsoft_hemas', function () {
    return Socialite::driver('azure')
    ->setConfig(getConfighemas())
    ->redirect();
});




Route::get('/login/microsoft_cavincare', function () {
    return Socialite::driver('azure')
    ->setConfig(getConfigcavincare())
    ->redirect();
});



function getConfigcavincare(): \SocialiteProviders\Manager\Config
{
  return new \SocialiteProviders\Manager\Config(
    env('Cavincare_AZURE_CLIENT_ID', 'some-client-id'),
    env('Cavincare_AZURE_CLIENT_SECRET'),
    url(env('Cavincare_AZURE_REDIRECT_URI', '/azuread/callback')),
    ['tenant' => env('Cavincare_AZURE_TENANT_ID', 'common')],
  );
}



function getConfighemas(): \SocialiteProviders\Manager\Config
{
  return new \SocialiteProviders\Manager\Config(
    env('Hemas_AZURE_CLIENT_ID', 'some-client-id'),
    env('Hemas_AZURE_CLIENT_SECRET'),
    url(env('Hemas_AZURE_REDIRECT_URI', '/azuread/callback')),
    ['tenant' => env('Hemas_AZURE_TENANT_ID', 'common')],
  );
}



Route::group(['middleware' => ['web', 'guest'], 'namespace' => 'App\Http\Controllers'], function(){
    Route::get('loginmic', 'Auth\LoginController@loginmic')->name('loginmic');
    Route::get('connect', 'Auth\LoginController@connect')->name('connect');
});

Route::group(['middleware' => ['web', 'MsGraphAuthenticated'], 'prefix' => 'app', 'namespace' => 'App\Http\Controllers'], function(){
     Route::get('logout', 'Auth\LoginController@logout')->name('logout');
});






Route::get('masters', [App\Http\Controllers\MasterController::class, 'masters'])->middleware('Admin')->name('masters');
Route::get('masters', [App\Http\Controllers\MasterController::class, 'masters_hepl'])->middleware('Admin');
/* Master1 From tab 1 Start */
Route::post('save_division', [App\Http\Controllers\MasterController::class, 'save_division']);
Route::post('update_division', [App\Http\Controllers\MasterController::class, 'update_division']);
Route::get('delete_division', [App\Http\Controllers\MasterController::class, 'delete_division']);
Route::post('get_all_division', 'App\Http\Controllers\MasterController@get_all_division');
/* Master1 From tab 1 end */
/* Master1 From tab 2 Start */
Route::post('save_new_bussiness', [App\Http\Controllers\MasterController::class, 'save_new_bussiness']);
Route::post('edit_bussiness', [App\Http\Controllers\MasterController::class, 'edit_bussiness']);
Route::post('update_bussiness', [App\Http\Controllers\MasterController::class, 'update_bussiness']);
Route::post('get_all_bussiness', 'App\Http\Controllers\MasterController@get_all_bussiness');
Route::get('delete_bussiness', [App\Http\Controllers\MasterController::class, 'delete_bussiness']);
/* Master1 From tab 2 end */

/* Master1 From tab 3 Start */
/* Route::post('save_new_user', [App\Http\Controllers\MasterController::class, 'save_new_user']);
Route::post('get_all_user', 'App\Http\Controllers\MasterController@get_all_user');
Route::post('update_user','App\Http\Controllers\MasterController@update_users');
Route::get('delete_user','App\Http\Controllers\MasterController@delete_user'); */

/* Master1 From tab 3 Start */

/* Master1 From tab 4 Start */
Route::post('save_new_project', [App\Http\Controllers\MasterController::class, 'save_new_project']);
Route::post('get_all_project', [App\Http\Controllers\MasterController::class, 'get_all_project']);
Route::get('delete_project', [App\Http\Controllers\MasterController::class, 'delete_project']);

/* Master1 From tab 4 end */
/* Master1 From tab 5 Start */

/* Master1 From tab 5 end */

/* Master1 From tab 6 Start */
Route::get('get_ipr_list', [App\Http\Controllers\MasterController::class, 'get_ipr_list']);
Route::post('create_ipr', [App\Http\Controllers\MasterController::class, 'create_ipr']);
Route::get('edit_ipr', [App\Http\Controllers\MasterController::class, 'edit_ipr']);
Route::post('update_ipr', [App\Http\Controllers\MasterController::class, 'update_ipr']);
ROute::get('delete_ipr', [App\Http\Controllers\MasterCOntroller::class, 'delete_ipr']);
/* Master1 From tab 6 end */

/* Master1 From tab 7 Start */
Route::post('get_collabrative_list', [App\Http\Controllers\MasterController::class, 'get_collabrative_list']);
Route::post('create_collabrative', [App\Http\Controllers\MasterController::class, 'create_collabrative']);
Route::get('edit_collabrative', [App\Http\Controllers\MasterController::class, 'edit_collabrative']);
Route::post('update_collabrative', [App\Http\Controllers\MasterController::class, 'update_collabrative']);
Route::get('delete_collabrative', [App\Http\Controllers\MasterController::class, 'delete_collabrative']);

/* Master1 From tab 7 end */






/* Master From tab 8 Start */
Route::post('get_all_insight_category', [App\Http\Controllers\MasterController::class, 'get_all_insight_category']);
Route::post('save_new_insight_category', [App\Http\Controllers\MasterController::class, 'save_new_insight_category']);
Route::post('update_insight_category', [App\Http\Controllers\MasterController::class, 'update_insight_category']);
Route::get('delete_insight_category', [App\Http\Controllers\MasterController::class, 'delete_insight_category']);
/* Master From tab 8 End */

/* Master From tab 9 End */



/* Master From tab 10 Start */
Route::post('get_all_idea_weight', [App\Http\Controllers\MasterController::class, 'get_all_idea_weight']);
Route::post('save_new_idea_weight', [App\Http\Controllers\MasterController::class, 'save_new_idea_weight']);
Route::post('update_idea_weight', [App\Http\Controllers\MasterController::class, 'update_idea_weight']);
Route::get('delete_idea_weight', [App\Http\Controllers\MasterController::class, 'delete_idea_weight']);
/* Master From tab 10 End */

/* Master From tab 11 Start */
Route::post('get_all_replication_competitors', [App\Http\Controllers\MasterController::class, 'get_all_replication_competitors']);
Route::post('save_new_replication_competitors', [App\Http\Controllers\MasterController::class, 'save_new_replication_competitors']);
Route::get('update_replication_competitors', [App\Http\Controllers\MasterController::class, 'update_replication_competitors']);
Route::get('delete_replication_competitors', [App\Http\Controllers\MasterController::class, 'delete_replication_competitors']);
/* Master From tab 11 End */

/* Master From tab 12 Start */
Route::post('get_all_publication_scope', [App\Http\Controllers\MasterController::class, 'get_all_publication_scope']);
Route::post('save_new_publication_scope', [App\Http\Controllers\MasterController::class, 'save_new_publication_scope']);
Route::post('update_publication_scope', [App\Http\Controllers\MasterController::class, 'update_publication_scope']);
Route::get('delete_publication_scope', [App\Http\Controllers\MasterController::class, 'delete_publication_scope']);
/* Master From tab 12 End */

/********************** Master2  Start ***************************/

/* Master2 tab1 Start */
Route::post('get_all_product_category', [App\Http\Controllers\MasterController::class, 'get_all_product_category']);
Route::get('save_new_product_category', [App\Http\Controllers\MasterController::class, 'save_new_product_category']);
Route::get('edit_product_category', [App\Http\Controllers\MasterController::class, 'edit_product_category']);
Route::get('update_product_category', [App\Http\Controllers\MasterController::class, 'update_product_category']);
Route::get('delete_product_category', [App\Http\Controllers\MasterController::class, 'delete_product_category']);
/* Master2 From tab1 End */

/* Master2 tab2 Start */
Route::post('get_all_brand', [App\Http\Controllers\MasterController::class, 'get_all_brand']);
Route::get('save_new_brand', [App\Http\Controllers\MasterController::class, 'save_new_brand']);
Route::get('edit_brand', [App\Http\Controllers\MasterController::class, 'edit_brand']);
Route::get('update_brand', [App\Http\Controllers\MasterController::class, 'update_brand']);
Route::get('delete_brand', [App\Http\Controllers\MasterController::class, 'delete_brand']);

/* Master2 From tab2 End */

/* Master2 tab3 Start */
Route::post('get_all_project_status', [App\Http\Controllers\MasterController::class, 'get_all_project_status']);
Route::get('save_new_project_status', [App\Http\Controllers\MasterController::class, 'save_new_project_status']);
Route::get('edit_project_status', [App\Http\Controllers\MasterController::class, 'edit_project_status']);
Route::get('update_project_status', [App\Http\Controllers\MasterController::class, 'update_project_status']);
Route::get('delete_project_status', [App\http\Controllers\MasterController::class, 'delete_project_status']);
/* Master2 From tab3 End */

/* Master2 tab4 Start */
Route::post('get_all_reasons_delay', [App\Http\Controllers\MasterController::class, 'get_all_reasons_delay']);
Route::get('save_new_reasons_delay', [App\Http\Controllers\MasterController::class, 'save_new_reasons_delay']);
Route::get('edit_reasons_delay', [App\Http\Controllers\MasterController::class, 'edit_reasons_delay']);
Route::get('update_reasons_delay', [App\Http\Controllers\MasterController::class, 'update_reasons_delay']);
Route::get('delete_reasons_delay', [App\Http\Controllers\MasterController::class, 'delete_reasons_delay']);
/* Master2 From tab4 End */

/* Master2 tab5 Start */
Route::post('get_all_cft_status', [App\Http\Controllers\MasterController::class, 'get_all_cft_status']);
Route::get('save_new_cft_status', [App\Http\Controllers\MasterController::class, 'save_new_cft_status']);
Route::get('edit_cft_status', [App\Http\Controllers\MasterController::class, 'edit_cft_status']);
Route::get('update_cft_status', [App\Http\Controllers\MasterController::class, 'update_cft_status']);
Route::get('delete_cft_status', [App\Http\Controllers\MasterController::class, 'delete_cft_status']);
/* Master2 From tab5 End */


/* Master2 tab6 Start */
Route::post('get_all_target_segment', [App\Http\Controllers\MasterController::class, 'get_all_target_segment']);
Route::get('save_new_target_segment', [App\Http\Controllers\MasterController::class, 'save_new_target_segment']);
Route::get('edit_target_segment', [App\Http\Controllers\MasterController::class, 'edit_target_segment']);
Route::get('update_target_segment', [App\Http\Controllers\MasterController::class, 'update_target_segment']);
Route::get('delete_target_segment', [App\Http\Controllers\MasterController::class, 'delete_target_segment']);

/* Master2 From tab6 End */


/* Master2 tab7 Start */
Route::post('get_all_collabrative_status', [App\Http\Controllers\MasterController::class, 'get_all_collabrative_status']);
Route::get('save_new_collabrative_status', [App\Http\Controllers\MasterController::class, 'save_new_collabrative_status']);
Route::get('update_collabrative_status', [App\Http\Controllers\MasterController::class, 'update_collabrative_status']);
Route::get('delete_collabrative_status',[App\Http\Controllers\MasterController::class, 'delete_collabrative_status']);
/* Master2 From tab7 End */

/* Master2 tab8 Start */
Route::post('get_all_ipr_status', [App\Http\Controllers\MasterController::class, 'get_all_ipr_status']);
Route::get('save_new_ipr_status', [App\Http\Controllers\MasterController::class, 'save_new_ipr_status']);
Route::get('edit_ipr_status', [App\Http\Controllers\MasterController::class, 'edit_ipr_status']);
Route::get('update_ipr_status', [App\Http\Controllers\MasterController::class, 'update_ipr_status']);
Route::get('delete_ipr_status', [App\Http\Controllers\MasterController::class, 'delete_ipr_status']);
/* Master2 From tab8 End */

/* Master2 tab9 Start */
Route::post('get_all_preferedconcepts_status', [App\Http\Controllers\MasterController::class, 'get_all_preferedconcepts_status']);
Route::get('save_new_preferedconcepts_status', [App\Http\Controllers\MasterController::class, 'save_new_preferedconcepts_status']);
Route::get('edit_preferedconcepts_status', [App\Http\Controllers\MasterController::class, 'edit_preferedconcepts_status']);
Route::get('update_preferedconcepts_status', [App\Http\Controllers\MasterController::class, 'update_preferedconcepts_status']);
Route::get('delete_preferedconcepts_status', [App\Http\Controllers\MasterController::class, 'delete_preferedconcepts_status']);

/* Master2 From tab9 End */

/* Master2 tab10 Start */
Route::post('get_all_preferedproducts_status', [App\Http\Controllers\MasterController::class, 'get_all_preferedproducts_status']);
Route::get('save_new_preferedproducts_status', [App\Http\Controllers\MasterController::class, 'save_new_preferedproducts_status']);
Route::get('edit_preferedproducts_status', [App\Http\Controllers\MasterController::class, 'edit_preferedproducts_status']);
Route::get('update_preferedproducts_status', [App\Http\Controllers\MasterController::class, 'update_preferedproducts_status']);
Route::get('delete_preferedproducts_status', [App\Http\Controllers\MasterController::class,'delete_preferedproducts_status']);

/* Master2 From tab10 End */

/* Advance DashBoard  Start */
Route::post('get_npd_division_tab', 'App\Http\Controllers\AdminController@get_npd_division_tab');
Route::post('get_npd_project_list_tab', 'App\Http\Controllers\AdminController@get_npd_project_list_tab');
Route::post('get_npd_project_list_tab1', 'App\Http\Controllers\AdminController@get_npd_project_list_tab1');
Route::post('dashboard_function_data', 'App\Http\Controllers\AdminController@dashboard_function_data');
Route::post('get_npd_business_tab', 'App\Http\Controllers\AdminController@get_npd_business_tab');
Route::post('get_delay_project_list_tab', 'App\Http\Controllers\AdminController@get_delay_project_list_tab');
Route::post('get_delay_project_list_tab1', 'App\Http\Controllers\AdminController@get_delay_project_list_tab1');
Route::post('get_npd_project_list_tab_delay', 'App\Http\Controllers\AdminController@get_npd_project_list_tab_delay');

// functional dashboard
Route::post('functional_preferred_dashboard', 'App\Http\Controllers\AdminController@functional_preferred_dashboard');
Route::post('division_wise_function', 'App\Http\Controllers\AdminController@division_wise_function');
Route::post('preferred_concept_passed', 'App\Http\Controllers\AdminController@preferred_concept_passed');
Route::post('preferred_concept_tacken', 'App\Http\Controllers\AdminController@preferred_concept_tacken');
Route::post('preferred_concept_convertproduct', 'App\Http\Controllers\AdminController@preferred_concept_convertproduct');

Route::post('fuctional_product_outsourcing', 'App\Http\Controllers\AdminController@fuctional_product_outsourcing');

Route::post('functional_preferred_product', 'App\Http\Controllers\AdminController@functional_preferred_product');
Route::post('functional_pre_product_division', 'App\Http\Controllers\AdminController@functional_pre_product_division');
Route::post('functional_pre_product_pass', 'App\Http\Controllers\AdminController@functional_pre_product_pass');
Route::post('functional_pre_product_tacken', 'App\Http\Controllers\AdminController@functional_pre_product_tacken');
Route::post('functional_pre_product_not_tacken', 'App\Http\Controllers\AdminController@functional_pre_product_not_tacken');

Route::post('functional_ipr', 'App\Http\Controllers\AdminController@functional_ipr');
Route::post('ipr_division_wise', 'App\Http\Controllers\AdminController@ipr_division_wise');
Route::post('ipr_filled', 'App\Http\Controllers\AdminController@ipr_filled');
Route::post('ipr_grand', 'App\Http\Controllers\AdminController@ipr_grand');
Route::post('ipr_other', 'App\Http\Controllers\AdminController@ipr_other');

Route::post('l_and_d', 'App\Http\Controllers\AdminController@l_and_d');
Route::post('landd_implement_open', 'App\Http\Controllers\AdminController@landd_implement_open');
Route::post('landd_implement_1000g', 'App\Http\Controllers\AdminController@landd_implement_1000g');

Route::post('functional_randd', 'App\Http\Controllers\AdminController@functional_randd');
Route::post('randd_let_invo_inprogress', 'App\Http\Controllers\AdminController@randd_let_invo_inprogress');
Route::post('randd_critical_rm', 'App\Http\Controllers\AdminController@randd_critical_rm');
Route::post('randd_critical_rm_complete', 'App\Http\Controllers\AdminController@randd_critical_rm_complete');
Route::post('randd_method_inpro', 'App\Http\Controllers\AdminController@randd_method_inpro');
Route::post('randd_method_implement', 'App\Http\Controllers\AdminController@randd_method_implement');
Route::post('rand_applied_inprog', 'App\Http\Controllers\AdminController@rand_applied_inprog');
Route::post('randd_applied_readyto_share', 'App\Http\Controllers\AdminController@randd_applied_readyto_share');
Route::post('randd_applied_tacken', 'App\Http\Controllers\AdminController@randd_applied_tacken');
Route::post('randd_applied_nottacken', 'App\Http\Controllers\AdminController@randd_applied_nottacken');
Route::post('randd_led_tacken', 'App\Http\Controllers\AdminController@randd_led_tacken');
Route::post('randd_let_invo_inpro_ready', 'App\Http\Controllers\AdminController@randd_let_invo_inpro_ready');
Route::post('randd_let_ino_inprg_scien', 'App\Http\Controllers\AdminController@randd_let_ino_inprg_scien');
Route::post('randd_let_ino_no_tacken', 'App\Http\Controllers\AdminController@randd_let_ino_no_tacken');
Route::post('randd_let_pro_div', 'App\Http\Controllers\AdminController@randd_let_pro_div');
Route::post('randd_let_pro_div_ready', 'App\Http\Controllers\AdminController@randd_let_pro_div_ready');
Route::post('randd_let_pro_div_scien', 'App\Http\Controllers\AdminController@randd_let_pro_div_scien');
Route::post('randd_let_pro_tacken', 'App\Http\Controllers\AdminController@randd_let_pro_tacken');
Route::post('randd_let_pro_not_tacken', 'App\Http\Controllers\AdminController@randd_let_pro_not_tacken');
Route::post('randd_inter_inpro', 'App\Http\Controllers\AdminController@randd_inter_inpro');
Route::post('randd_inter_tech', 'App\Http\Controllers\AdminController@randd_inter_tech');

Route::post('randd_let_readyto', 'App\Http\Controllers\AdminController@randd_let_readyto');
Route::post('rand_let_pro_readyto', 'App\Http\Controllers\AdminController@rand_let_pro_readyto');


Route::get('prefered_concept_dub', 'App\Http\Controllers\AdminController@prefered_concept_dub');
Route::get('prefered_product_dub', 'App\Http\Controllers\AdminController@prefered_product_dub');
Route::get('ipr_dub', 'App\Http\Controllers\AdminController@ipr_dub');
Route::get('landd_idea_dub', 'App\Http\Controllers\AdminController@landd_idea_dub');
// Route::post('get_learning_devlopment_parent_idea_type', 'App\Http\Controllers\AdminController@get_learning_devlopment_parent_idea_type');
// get_learning_devlopment_idea_status_list
Route::post('/updateChildIdeaStatus', 'App\Http\Controllers\AdminController@updateChildIdeaStatus');

// L and D
Route::post('landd_division_wise', 'App\Http\Controllers\AdminController@landd_division_wise');
Route::post('land_insight_wise', 'App\Http\Controllers\AdminController@land_insight_wise');
Route::post('landd_idea_division_wise', 'App\Http\Controllers\AdminController@landd_idea_division_wise');
Route::post('landd_idea_divisionideacategory', 'App\Http\Controllers\AdminController@landd_idea_divisionideacategory');

// landd_division_wise  land_insight_wise  landd_idea_division_wise    landd_idea_divisionideacategory
//

// functional_preferred_dashboard division_wise_function fuctional_product_outsourcing functional_preferred_product  preferred_concept_passed  preferred_concept_tacken  preferred_concept_convertproduct
// functional_pre_product_pass functional_pre_product_tacken  functional_pre_product_not_tacken
// functional_ipr  functional_pre_product_division landd_implement_1000g
// ipr ipr_division_wise  ipr_filled  ipr_grand  ipr_other
// l_and_d  landd_implement_open
// functional_randd  randd_let_invo_inprogress randd_critical_rm  randd_critical_rm_complete

// randd_method_inpro randd_method_implement rand_applied_inprog randd_applied_readyto_share
// randd_applied_tacken randd_applied_nottacken
// randd_led_tacken
// randd_let_invo_inpro_ready  randd_let_ino_inprg_scien  randd_let_ino_no_tacken
// randd_let_pro_div randd_let_pro_div_ready randd_let_pro_div_scien  randd_let_pro_tacken randd_let_pro_not_tacken
// randd_inter_inpro randd_inter_tech
// randd_let_readyto  rand_let_pro_readyto
/* Advance DashBoard End */
Route::any('/cronfun', 'App\Http\Controllers\AdminController@cronfun');
//sathish
Route::post('history_view_learning_development', [App\Http\Controllers\HomeController::class, 'history_view_learning_development']);
Route::post('historyIPR', [App\Http\Controllers\HomeController::class, 'historyIPR']);
Route::get('innovation_Quotient', [App\Http\Controllers\HomeController::class, 'innovation_Quotient']);
Route::get('get_crone_job', [App\Http\Controllers\AdminController::class, 'get_crone_job']);
Route::get('insights_idea_gms', 'App\Http\Controllers\AdminController@insights_idea_gms');
Route::get('insights_idea_gms_filter', 'App\Http\Controllers\AdminController@insights_idea_gms_filter');
//api_for_business
// Route::get('business_list',[App\Http\Controllers\AdminController::class, 'business_list']);



//ajay
Route::get('npd_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('epd_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('cft_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('innovation_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('revamp_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('mainstream_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('research_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('critical_rm_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('new_method_chart', [App\Http\Controllers\AdminController::class, 'bar_chart']);
Route::get('get_chart', [App\Http\Controllers\AdminController::class, 'get_chart']);
Route::get('get_reporting_scientist_cl', [App\Http\Controllers\HomeController::class, 'get_reporting_scientist_cl']);
Route::get('scientist_for_tl', [App\Http\Controllers\AdminController::class, 'scientist_for_tl']);
Route::get('pre_concept', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('pre_product', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('insight_chart', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('ideas_chart', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('ipr_chart', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('pro_outsource', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('new_product', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('improvement_idea', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('squeeze_idea', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('research_domain', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('research_methodology', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('new_application', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('new_method', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('existing_method', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('process_improvement', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('innovation_quotient_chart', [App\Http\Controllers\AdminController::class, 'line_chart']);
Route::get('innovation_quotient_concept_chart', [App\Http\Controllers\AdminController::class, 'innovation_quotient_concept_chart']);
Route::get('innovation_quotient_project_chart', [App\Http\Controllers\AdminController::class, 'innovation_quotient_project_chart']);
Route::post('project_status_chart', [App\Http\Controllers\AdminController::class, 'project_status_chart']);
Route::get('get_outsource_chart', [App\Http\Controllers\AdminController::class, 'get_outsource_chart']);
Route::get('pre_concept_chart', [App\Http\Controllers\AdminController::class, 'pre_concept_chart']);
Route::get('learn_dev_insight_chart', [App\Http\Controllers\AdminController::class, 'learn_dev_insight_chart']);
Route::get('learn_dev_ideas_chart', [App\Http\Controllers\AdminController::class, 'learn_dev_ideas_chart']);
Route::post('project_status_ipr_chart', [App\Http\Controllers\AdminController::class, 'project_status_ipr_chart']);
Route::get('get_ipr_chart', [App\Http\Controllers\AdminController::class, 'get_ipr_chart']);
Route::post('save_new_formulation', [App\Http\Controllers\AdminController::class, 'save_new_formulation']);
Route::post('get_all_formulation', [App\Http\Controllers\AdminController::class, 'get_all_formulation']);
Route::post('save_new_signature', [App\Http\Controllers\AdminController::class, 'save_new_signature']);
Route::post('get_signature_data', [App\Http\Controllers\AdminController::class, 'get_signature_data']);
Route::post('save_new_cost', [App\Http\Controllers\AdminController::class, 'save_new_cost']);
Route::post('get_cost_data', [App\Http\Controllers\AdminController::class, 'get_cost_data']);
Route::post('get_analysis_data', [App\Http\Controllers\AdminController::class, 'get_analysis_data']);
Route::post('save_new_analysis', [App\Http\Controllers\AdminController::class, 'save_new_analysis']);
Route::get('edit_others', [App\Http\Controllers\AdminController::class, 'edit_others']);
Route::get('update_formulation', [App\Http\Controllers\AdminController::class, 'update_formulation']);
Route::get('update_analysis', [App\Http\Controllers\AdminController::class, 'update_analysis']);
Route::get('update_cost', [App\Http\Controllers\AdminController::class, 'update_cost']);
Route::get('update_signature', [App\Http\Controllers\AdminController::class, 'update_signature']);
Route::post('delete_others', [App\Http\Controllers\AdminController::class, 'delete_others']);
Route::get('export_data', [App\Http\Controllers\AdminController::class, 'export_data']);
Route::post('get_cftdelay_data', [App\Http\Controllers\AdminController::class, 'get_cftdelay_data']);
Route::post('get_rd_delay_data', [App\Http\Controllers\AdminController::class, 'get_rd_delay_data']);
Route::post('division_sec_delay', [App\Http\Controllers\AdminController::class, 'division_sec_delay']);
Route::post('projectdiv_detail_rd', [App\Http\Controllers\AdminController::class, 'projectdiv_detail_rd']);
Route::post('bus_sec_delay', [App\Http\Controllers\AdminController::class, 'bus_sec_delay']);
Route::post('projectbus_detail_rd', [App\Http\Controllers\AdminController::class, 'projectbus_detail_rd']);
Route::post('edit_ipr_task', [App\Http\Controllers\AdminController::class, 'edit_ipr_task']);
Route::get('letstatus_filter_applaid', [App\Http\Controllers\AdminController::class, 'letstatus_filter_applaid']);
Route::get('idea_list', [App\Http\Controllers\AdminController::class, 'idea_list']);
Route::get('update_idea_list', [App\Http\Controllers\AdminController::class, 'update_idea_list']);
Route::get('internal_stage', [App\Http\Controllers\AdminController::class, 'internal_stage']);
Route::get('internal_stage_update', [App\Http\Controllers\AdminController::class, 'internal_stage_update']);
Route::get('tmg_idea_div', [App\Http\Controllers\AdminController::class, 'tmg_idea_div']);
Route::get('hmg_idea_div', [App\Http\Controllers\AdminController::class, 'hmg_idea_div']);
Route::get('idea_pro_detail', [App\Http\Controllers\AdminController::class, 'idea_pro_detail']);
Route::get('new_fragrance_status', [App\Http\Controllers\AdminController::class, 'new_fragrance_status']);

Route::post('addscientistpercentage', [App\Http\Controllers\AdminController::class, 'addscientistpercentage']);
/********************** Master2  End ***************************/


/********************** phase3 -- For HEPL  End ***************************/

Route::get('test_sample', [App\Http\Controllers\HomeController::class, 'test_sample']);
//Route::get('masters_hepl', [App\Http\Controllers\MasterController::class, 'masters_hepl']);
Route::get('masters_hepl', 'App\Http\Controllers\MasterController@masters_hepl')->name('masters_hepl');
Route::get('user_management', [App\Http\Controllers\HomeController::class, 'user_management']);

/* Route::get('masters_hepl', 'App\Http\Controllers\MasterController@masters_hepl')->middleware('Hepladmin')->name('masters_hepl');
Route::get('user_management', 'App\Http\Controllers\HomeController@user_management')->middleware('Hepladmin')->name('user_management');
Route::get('test_sample', 'App\Http\Controllers\HomeController@test_sample')->middleware('Hepluser')->name('test_sample');
Route::get('test_sample', 'App\Http\Controllers\HomeController@test_sample')->middleware('Hepladmin')->name('test_sample'); */



/* Master Phase 3 Start */
/*Route::post('get_all_ipr_status', [App\Http\Controllers\MasterController::class, 'get_all_ipr_status']);
Route::get('save_new_ipr_status', [App\Http\Controllers\MasterController::class, 'save_new_ipr_status']);
Route::get('edit_ipr_status', [App\Http\Controllers\MasterController::class, 'edit_ipr_status']);
Route::get('update_ipr_status', [App\Http\Controllers\MasterController::class, 'update_ipr_status']);
Route::get('delete_ipr_status', [App\Http\Controllers\MasterController::class, 'delete_ipr_status']);*/


Route::get('get_all_worktype', [App\Http\Controllers\MasterController::class, 'get_all_worktype']);
Route::post('save_worktype', [App\Http\Controllers\MasterController::class, 'save_worktype']);
Route::get('edit_worktype', [App\Http\Controllers\MasterController::class, 'edit_worktype']);
Route::post('update_worktype', [App\Http\Controllers\MasterController::class, 'update_worktype']);
Route::post('delete_worktype', [App\Http\Controllers\MasterController::class, 'delete_worktype']);

Route::get('get_all_subworktype', [App\Http\Controllers\MasterController::class, 'get_all_subworktype']);
Route::post('save_subworktype', [App\Http\Controllers\MasterController::class, 'save_subworktype']);
Route::get('edit_subworktype', [App\Http\Controllers\MasterController::class, 'edit_subworktype']);
Route::post('update_subworktype', [App\Http\Controllers\MasterController::class, 'update_subworktype']);
Route::post('delete_subworktype', [App\Http\Controllers\MasterController::class, 'delete_subworktype']);


Route::get('get_all_category', [App\Http\Controllers\MasterController::class, 'get_all_category']);
Route::post('save_category', [App\Http\Controllers\MasterController::class, 'save_category']);
Route::get('edit_category', [App\Http\Controllers\MasterController::class, 'edit_category']);
Route::post('update_category', [App\Http\Controllers\MasterController::class, 'update_category']);
Route::post('delete_category', [App\Http\Controllers\MasterController::class, 'delete_category']);

Route::get('get_all_sampletest', [App\Http\Controllers\MasterController::class, 'get_all_sampletest']);
Route::post('save_sampletest', [App\Http\Controllers\MasterController::class, 'save_sampletest']);
Route::get('edit_sampletest', [App\Http\Controllers\MasterController::class, 'edit_sampletest']);
Route::post('update_sampletest', [App\Http\Controllers\MasterController::class, 'update_sampletest']);
Route::post('delete_sampletest', [App\Http\Controllers\MasterController::class, 'delete_sampletest']);

Route::get('get_all_standardtime', [App\Http\Controllers\MasterController::class, 'get_all_standardtime']);
Route::post('save_standardtime', [App\Http\Controllers\MasterController::class, 'save_standardtime']);
Route::get('edit_standardtime', [App\Http\Controllers\MasterController::class, 'edit_standardtime']);
Route::post('update_standardtime', [App\Http\Controllers\MasterController::class, 'update_standardtime']);
Route::post('delete_standardtime', [App\Http\Controllers\MasterController::class, 'delete_standardtime']);
Route::get('get_catogery_list', [App\Http\Controllers\AdminController::class, 'get_catogery_list']);
Route::get('get_sample_list', [App\Http\Controllers\AdminController::class, 'get_sample_list']);
Route::post('add_sample_datas', [App\Http\Controllers\AdminController::class, 'add_sample_datas']);
Route::get('fetch_sample_data', [App\Http\Controllers\AdminController::class, 'fetch_sample_data']);
Route::get('get_stand_time', [App\Http\Controllers\AdminController::class, 'get_stand_time']);
Route::post('delete_test_sample', [App\Http\Controllers\AdminController::class, 'delete_test_sample']);
Route::post('edit_sample_test', [App\Http\Controllers\AdminController::class, 'edit_sample_test']);
Route::post('update_sample_datas', [App\Http\Controllers\AdminController::class, 'update_sample_datas']);
Route::post('work_filter', [App\Http\Controllers\AdminController::class, 'work_filter']);


Route::get('get_all_user', [App\Http\Controllers\MasterController::class, 'get_all_user']);
Route::post('save_user', [App\Http\Controllers\MasterController::class, 'save_user']);
Route::get('edit_user', [App\Http\Controllers\MasterController::class, 'edit_user']);
Route::post('update_user', [App\Http\Controllers\MasterController::class, 'update_user']);
Route::post('delete_user', [App\Http\Controllers\MasterController::class, 'delete_user']);
Route::post('update_user_status', [App\Http\Controllers\MasterController::class, 'update_user_status']);

Route::get('get_category', [App\Http\Controllers\MasterController::class, 'get_category']);

Route::get('getsubwork_worktype', [App\Http\Controllers\MasterController::class, 'getsubwork_worktype']);
Route::get('get_worktype', [App\Http\Controllers\MasterController::class, 'get_worktype']);
Route::get('get_subworktype', [App\Http\Controllers\MasterController::class, 'get_subworktype']);
Route::get('get_sampletest', [App\Http\Controllers\MasterController::class, 'get_sampletest']);

/* Master Phase 3 End */


Route::any('get_project_request', [App\Http\Controllers\AdminController::class, 'get_project_request']);
